# Transfer Market Scheduler Job Distribution

## Overview

This document explains the current implementation of transfer market scheduler jobs and the proposed changes to distribute these jobs across multiple nodes in PROD and STA environments.

## Current Implementation

### System Architecture

The HBL Handball Manager API currently runs on multiple nodes:
- **PROD Environment**: 3 nodes (PROD, PROD2, PROD3)
- **STA Environment**: 3 nodes (STA, STA2, STA3)

Each node has its own `activeProfile` that identifies it uniquely in the cluster.

### Current Transfer Market Job Scheduling

#### Key Components

1. **HmSchedulerService.scheduleTransferMarketJobs()**
   - Located in: `backend-persistence/src/main/java/com/pass/hbl/manager/backend/persistence/service/hm/HmSchedulerService.java`
   - Schedules transfer market jobs for system players
   - Currently assigns all jobs to the same node (the one that creates the league)

2. **Job Types**
   - `END_TRANSFER_AUCTION_JOB`: Ends transfer market auctions
   - `ADD_SYSTEM_BID`: Adds system bids to transfer market items

3. **System Player Creation**
   - When a new league is created, 10 system players are automatically added to the transfer market
   - These are managed by the system user "START7"
   - Each system player gets scheduled jobs for auction management

#### Current Flow

```java
public boolean scheduleTransferMarketJobs(Map<UUID, LocalDateTime> scheduleByIdMap, boolean isSystemPlayer, String jobName) {
    // Get template job
    Optional<HmSchedulerJob> transferMarketJobOpt = getOneTimeJobByNameIncludingDeletedReadOnly(jobName);
    
    // Create jobs for each transfer market item
    List<Pair<LocalDateTime, Map<String, String>>> jobs = scheduleByIdMap.entrySet().stream()
            .map(entry -> {
                Map<String, String> parameters = Map.of(
                    TRANSFER_MARKET_ID, entry.getKey().toString(), 
                    SYSTEM_PLAYER, Boolean.toString(isSystemPlayer)
                );
                LocalDateTime executionTime = entry.getValue();
                return Pair.of(executionTime, parameters);
            })
            .collect(Collectors.toList());

    return scheduleOneTimeJobs(template, jobs);
}
```

#### Current Issues

1. **Single Node Load**: All 10 system player jobs are scheduled on the same node
2. **Uneven Distribution**: No load balancing across available nodes
3. **Potential Bottleneck**: High load on one node during league creation

## Proposed Solution: Node Distribution

### Implementation Strategy

Distribute the 10 system player scheduler jobs across the 3 available nodes using a round-robin approach:
- Node 1: Jobs 1, 4, 7, 10 (4 jobs)
- Node 2: Jobs 2, 5, 8 (3 jobs)  
- Node 3: Jobs 3, 6, 9 (3 jobs)

### Code Changes

#### 1. Enhanced scheduleTransferMarketJobs Method

```java
public boolean scheduleTransferMarketJobs(Map<UUID, LocalDateTime> scheduleByIdMap, boolean isSystemPlayer, String jobName) {
    Optional<HmSchedulerJob> transferMarketJobOpt = getOneTimeJobByNameIncludingDeletedReadOnly(jobName);
    if (transferMarketJobOpt.isEmpty()) {
        log.warn("No template job found for " + jobName + ". Cannot add schedule");
        return false;
    }

    HmSchedulerJob template = transferMarketJobOpt.get();
    if (isNotCronJob(template)) {
        return false;
    }

    log.info("Scheduling job " + template.getName() + " [" + template.getJobClass() + "] to  {" + Util.convertToString(scheduleByIdMap) + "}");

    // Get available nodes for distribution
    List<String> availableNodes = getAvailableNodesForEnvironment();
    
    List<Pair<LocalDateTime, Map<String, String>>> jobs = new ArrayList<>();
    AtomicInteger nodeIndex = new AtomicInteger(0);
    
    scheduleByIdMap.entrySet().stream().forEach(entry -> {
        // Distribute jobs across nodes using round-robin
        String targetNode = availableNodes.get(nodeIndex.getAndIncrement() % availableNodes.size());
        
        Map<String, String> parameters = Map.of(
            TRANSFER_MARKET_ID, entry.getKey().toString(), 
            SYSTEM_PLAYER, Boolean.toString(isSystemPlayer)
        );
        LocalDateTime executionTime = entry.getValue();
        
        jobs.add(Pair.of(executionTime, parameters));
    });

    return scheduleOneTimeJobsWithNodeDistribution(template, jobs, availableNodes);
}
```

#### 2. New Helper Methods

```java
private List<String> getAvailableNodesForEnvironment() {
    String currentProfile = Util.getActiveProfile(environment);
    
    switch (currentProfile) {
        case PROD_PROFILE:
            return List.of(PROD_PROFILE, PROD2_PROFILE, PROD3_PROFILE);
        case PROD2_PROFILE:
            return List.of(PROD_PROFILE, PROD2_PROFILE, PROD3_PROFILE);
        case PROD3_PROFILE:
            return List.of(PROD_PROFILE, PROD2_PROFILE, PROD3_PROFILE);
        case STAGE_PROFILE:
            return List.of(STAGE_PROFILE, STAGE2_PROFILE, STAGE3_PROFILE);
        case STAGE2_PROFILE:
            return List.of(STAGE_PROFILE, STAGE2_PROFILE, STAGE3_PROFILE);
        case STAGE3_PROFILE:
            return List.of(STAGE_PROFILE, STAGE2_PROFILE, STAGE3_PROFILE);
        default:
            return List.of(currentProfile); // Single node for other environments
    }
}

private boolean scheduleOneTimeJobsWithNodeDistribution(HmSchedulerJob initialJob, 
        List<Pair<LocalDateTime, Map<String, String>>> executionTimesAndParameters, 
        List<String> availableNodes) {
    
    List<HmSchedulerJob> oneTimeJobs = new ArrayList<>();
    AtomicBoolean success = new AtomicBoolean(true);
    AtomicInteger nodeIndex = new AtomicInteger(0);

    executionTimesAndParameters.forEach(entry -> {
        String cronExpression = Util.toCronExpression(entry.getLeft());
        Map<String, String> parameters = entry.getRight();
        
        // Assign job to specific node using round-robin
        String targetNode = availableNodes.get(nodeIndex.getAndIncrement() % availableNodes.size());

        HmSchedulerJob job = getParameterizedInstanceForOneTimeJob(
            initialJob.getName(), 
            initialJob.getDescription(),
            initialJob.getMode(), 
            initialJob.getJobClass(), 
            targetNode  // Use target node instead of current activeProfile
        );

        if (job != null) {
            job.setCronExpression(cronExpression);
            job.setParameterMap(parameters);
            oneTimeJobs.add(job);
        }
    });

    // Save and schedule jobs
    Iterable<HmSchedulerJob> result = repository.saveAll(oneTimeJobs);
    Util.toStream(result).forEach(job -> {
        try {
            // Only schedule if job belongs to current node
            if (Objects.equals(job.getActiveProfile(), activeProfile)) {
                scheduleCronJob(job);
            }
        } catch (SchedulingException e) {
            logMessageService.logException("Cannot schedule cron job " + initialJob.getName(), e);
            log.error("Cannot schedule cron job " + initialJob.getName(), e);
            success.set(false);
        }
    });
    
    return success.get();
}
```

### Benefits

1. **Load Distribution**: Evenly distributes scheduler jobs across available nodes
2. **Scalability**: Automatically adapts to the number of available nodes
3. **Fault Tolerance**: If one node fails, other nodes continue processing their assigned jobs
4. **Performance**: Reduces load on individual nodes during peak times
5. **Backward Compatibility**: Falls back to single node for non-clustered environments

### Database Impact

- Jobs are stored with their assigned `activeProfile` (node identifier)
- Each node only executes jobs assigned to its profile
- Existing job termination mechanism works across nodes via database coordination

### Monitoring and Logging

- Enhanced logging shows which node each job is assigned to
- Job distribution is logged for debugging and monitoring
- Existing job monitoring tools continue to work across all nodes

## Testing Strategy

1. **Unit Tests**: Test node distribution logic with different environment configurations
2. **Integration Tests**: Verify jobs are correctly distributed and executed across nodes
3. **Load Tests**: Ensure performance improvement with distributed jobs
4. **Failover Tests**: Verify system behavior when nodes become unavailable

## Deployment Considerations

1. **Rolling Deployment**: Changes can be deployed gradually across nodes
2. **Configuration**: No additional configuration required - uses existing profile system
3. **Monitoring**: Existing monitoring tools will show distributed job execution
4. **Rollback**: Can easily revert to single-node scheduling if needed

## Future Enhancements

1. **Dynamic Load Balancing**: Consider actual node load when distributing jobs
2. **Health-Based Distribution**: Avoid assigning jobs to unhealthy nodes
3. **Job Migration**: Automatically redistribute jobs from failed nodes
4. **Metrics**: Add detailed metrics for job distribution effectiveness
