server:
  port: 8181
  servlet:
    context-path: /adminapi
  error:
    include-message: always
  forward-headers-strategy: NATIVE
spring:
  main:
    allow-circular-references: true
  application:
    name: HBL Manager Backend
  datasource:
    url: **********************************************
    #    url: ************************************,172.17.34.188:5432,172.17.34.189:5432/handball_manager?targetServerType=primary
    username: hbl
    password: hbl
    driver-class-name: org.postgresql.Driver
    hikari:
      idle-timeout: 60000
      maximum-pool-size: 2000
      minimum-idle: 20
  jpa:
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        jdbc:
          batch_size: 50
          time_zone: UTC
        ddl-auto: none
        order_insert: true
        order_update: true
    open-in-view: false
  liquibase:
    change-log: classpath:/db/db.changelog-master.yml
    enabled: true
management:
  server:
    port: 8182
  endpoint:
    chaosmonkey:
      enabled: true
    chaosmonkeyjmx:
      enabled: true
    health:
      group:
        custom:
          include: datasource,ping,diskspace
          show-components: always
          show-details: always
  metrics:
    tags:
      application: ${spring.application.name}
    distribution:
      percentiles:
        http:
          server:
            requests: 0.5, 0.9, 0.95, 0.99
  endpoints:
    web:
      exposure:
        include: "*"
chaos:
  monkey:
    enabled: true
    watcher:
      service: true
      controller: true
      rest-controller: true
      repository: true
      component: false
logging:
  config: file:/conf/logback-spring.xml
handball-manager:
  importer:
    sportradar-stage: production
    # noinspection SpellCheckingInspection
    api-key: n4gdhnm9d53kyypetumvkzjq
  datacore:
    scheduler-enabled: true
    staging-base-url: https://api.dc.stg.connect-nonprod.sportradar.dev/v1
    staging-auth-url: https://token.stg.connect-nonprod.sportradar.dev/v1/oauth2/rest/token
    staging-streaming-auth-url: https://token.stg.connect-nonprod.sportradar.dev/v1/stream/fixture/access
    staging-client-id: 137ZsT1EqzFPvlb1jOSaJaOXAz0SDx
    staging-client-secret: 1Sa7B4Mwa7AVn7WvF0Qh1qEijbhaJv
    # prod parameters
    prod-base-url: https://api.dc.connect.sportradar.com/v1
    prod-auth-url: https://token.connect.sportradar.com/v1/oauth2/rest/token
    prod-streaming-auth-url: https://token.connect.sportradar.com/v1/stream/fixture/access
    # Prod credentials
    prod-client-id: 297ZxjMzJ8Yu5WIp2yCkcwui5LwG5B
    prod-client-secret: OT3PadYzNOeV58fo7D1cWKnN19T3Ng
  security:
    admin-user: admin
    admin-password: admin
    admin-write-user: admin-write
    admin-write-password: admin-write
  http-proxy:
    enabled: true
    url: http://***********:3128