package com.pass.hbl.manager.backend.persistence.service.hm;

import com.pass.hbl.manager.backend.persistence.domain.admin.AdminUsersStatisticsDO;
import com.pass.hbl.manager.backend.persistence.domain.hm.HmAwardDescriptionDO;
import com.pass.hbl.manager.backend.persistence.domain.hm.HmUserSessionAttributesDO;
import com.pass.hbl.manager.backend.persistence.domain.hm.HmUserTotalScoreDO;
import com.pass.hbl.manager.backend.persistence.dto.hm.SessionAttribute;
import com.pass.hbl.manager.backend.persistence.dto.hm.UserDto;
import com.pass.hbl.manager.backend.persistence.dto.hm.UserProfileDto;
import com.pass.hbl.manager.backend.persistence.dto.shared.ImageDomain;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmUserLevel;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmUserProfile;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.exception.InvalidOperationException;
import com.pass.hbl.manager.backend.persistence.exception.TechnicalException;
import com.pass.hbl.manager.backend.persistence.mapper.hm.HmUserMapper;
import com.pass.hbl.manager.backend.persistence.mapper.hm.HmUserProfileMapper;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmUserLevelRepository;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmUserProfileRepository;
import com.pass.hbl.manager.backend.persistence.service.AbstractService;
import com.pass.hbl.manager.backend.persistence.service.admin.LogMessageService;
import com.pass.hbl.manager.backend.persistence.service.hm.handlers.LeagueScoreHandler;
import com.pass.hbl.manager.backend.persistence.service.hm.handlers.UserProfileAdminHandler;
import com.pass.hbl.manager.backend.persistence.service.shared.ImageService;
import com.pass.hbl.manager.backend.persistence.service.shared.TransactionHandler;
import com.pass.hbl.manager.backend.persistence.util.Constants;
import com.pass.hbl.manager.backend.persistence.util.Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.EventListener;
import org.springframework.core.env.Environment;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.persistence.LockModeType;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.pass.hbl.manager.backend.persistence.exception.TechnicalException.Type.DATABASE_UPDATE_FAILED;
import static com.pass.hbl.manager.backend.persistence.util.Constants.*;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static org.springframework.transaction.annotation.Propagation.REQUIRES_NEW;

@Slf4j
@Service
@Transactional
public class UserProfileService extends AbstractService<HmUserProfile, UserProfileDto> {

    private final HmUserProfileMapper mapper;
    private final HmUserMapper userMapper;

    private final HmUserProfileRepository repository;
    private final HmUserLevelRepository userLevelRepository;

    private final ImageService imageService;
    private final LeagueService leagueService;
    private final LogMessageService logMessageService;
    private final AwardService awardService;

    private final TransactionHandler transactionHandler;
    private final LeagueScoreHandler leagueScoreHandler;
    private final UserProfileAdminHandler userProfileAdminHandler;

    private final Environment environment;

    private final Object lock = new Object();
    private volatile HmUserProfile systemUser;

    List<HmUserLevel> sortedUserLevelsCache = new ArrayList<>();

    public UserProfileService(HmUserProfileMapper mapper,
                              HmUserMapper userMapper,
                              HmUserProfileRepository repository,
                              HmUserLevelRepository userLevelRepository, @Lazy ImageService imageService,
                              @Lazy LeagueService leagueService,
                              LogMessageService logMessageService,
                              @Lazy AwardService awardService, TransactionHandler transactionHandler,
                              @Lazy LeagueScoreHandler leagueScoreHandler, UserProfileAdminHandler userProfileAdminHandler, Environment environment) {
        super(repository, mapper, HmUserProfile.class);
        this.mapper = mapper;
        this.userMapper = userMapper;
        this.repository = repository;
        this.userLevelRepository = userLevelRepository;
        this.imageService = imageService;
        this.leagueService = leagueService;
        this.logMessageService = logMessageService;
        this.awardService = awardService;
        this.transactionHandler = transactionHandler;
        this.leagueScoreHandler = leagueScoreHandler;
        this.userProfileAdminHandler = userProfileAdminHandler;
        this.environment = environment;
    }

    @EventListener(ApplicationReadyEvent.class)
    @Transactional(readOnly = true)
    public void initializeUserLevels() {
        sortedUserLevelsCache =  this.getSortedUserLevels();
        log.info("UserProfileService: sortedUserLevelsCache initialized. count = " + sortedUserLevelsCache.size());
    }

    @EventListener(ApplicationReadyEvent.class)
    public void createSystemUser() {
        if (!repository.existsByEmailAddressIgnoreCase(Constants.SYSTEM_EMAIL)) {
            log.info("System user (bank) does not exist. Will create it.");
            HmUserProfile system = new HmUserProfile(Constants.SYSTEM_USERNAME, Constants.SYSTEM_EMAIL);
            system.setEmailValidated(true);
            repository.save(system);
            try {
                initSystemProfilePicture(Util.getAppIcon(environment));
                log.info("System profile picture successfully set");
            } catch (Exception e) {
                log.error("Failed to set system profile picture. Reason: " + e.getMessage() + ". Skipping..");
            }
        }
    }

    public void initSystemProfilePicture(String hblAppIcon) throws IOException, TechnicalException {
        InputStream inputStream = getClass().getResourceAsStream("/" + hblAppIcon);
        if (nonNull(inputStream)) {
            MultipartFile multipartFile = new MockMultipartFile("file",
                    hblAppIcon, "image/png", inputStream.readAllBytes());
            setSystemPicture(multipartFile);
        } else {
            throw new TechnicalException(TechnicalException.Type.RESOURCE_NOT_FOUND, "Unable to find the resource file " + hblAppIcon);
        }
    }

    public void setSystemPicture(MultipartFile file) throws IOException {
        HmUserProfile user = getSystemUser();
        imageService.delete(user.getPicture());
        if (file == null) {
            user.setPicture(null);
        } else {
            user.setPicture(imageService.saveAsUUID(file, ImageDomain.PLAYER_PROFILE_PHOTO));
        }
        repository.save(user);
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true)
    public Optional<UserProfileDto> getDtoByAuthentication(String ssoId) {
        if (StringUtils.isEmpty(ssoId)) {
            return Optional.empty();
        }
        return getEntityBySsoId(ssoId).map(mapper::mapToDto);
    }

    public Optional<HmUserProfile> getEntityBySsoId(String ssoId) {
        return repository.findBySsoIdIgnoreCase(ssoId);
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public HmUserProfile getSystemUser() {
        if (systemUser == null) {
            synchronized (lock) {
                if (systemUser == null) {
                    systemUser = repository.findByEmailAddressIgnoreCase(Constants.SYSTEM_EMAIL)
                            .orElseThrow(() -> new RuntimeException("Fatal: no system user exists (email=" + Constants.SYSTEM_EMAIL + ". Create it, otherwise system will not start"));
                }
            }
        }
        return systemUser;
    }

    public String getSystemUserName() {
        return Constants.SYSTEM_USERNAME;
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public UUID getSystemUserId() {
        return getSystemUser().getId();
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public boolean isSystemUser(@NotNull String id) {
        return Objects.equals(getSystemUserId().toString(), id);
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public boolean isSystemUser(@NotNull UUID id) {
        return isSystemUser(id.toString());
    }

    public HmUserProfile save(@NotNull UserProfileDto dto) throws FormatException {
        Optional<HmUserProfile> existing = repository.findById(Util.convertId(dto.getId()));
        HmUserProfile user;
        if (existing.isEmpty()) {
            user = mapper.mapToEntity(dto);
        } else {
            user = mapper.mapToEntity(dto, existing.get());
        }
        return save(user);
    }

    @Lock(LockModeType.PESSIMISTIC_WRITE)
    public UserProfileDto setSessionAttribute(@NotNull String id, @NotNull SessionAttribute key, String value) throws FormatException {
        UserProfileDto userProfileDto = getUserProfileDto(id);
        Map<SessionAttribute, String> sessionAttributes = userProfileDto.getSessionAttributes();
        if (sessionAttributes == null) {
            sessionAttributes = new HashMap<>();
        }
        sessionAttributes.put(key, value);
        // update the session attribute in the database
        repository.updateSessionAttributes(Util.convertId(id) , sessionAttributes);
        userProfileDto.setSessionAttributes(sessionAttributes);
        return userProfileDto;
    }

    public void setSessionAttribute(HmUserProfile user, SessionAttribute key, String value) {
        user.setSessionAttribute(key, value);
        Map<SessionAttribute, String> sessionAttributes = user.getSessionAttributes();
        repository.updateSessionAttributes(user.getId(), sessionAttributes);
    }

    private UserProfileDto getUserProfileDto(String id) {
        return transactionHandler.runInNewTransactionReadOnly(() -> {
            try {
                return getByIdAsDto(id);
            } catch (Exception e) {
                return null;
            }
        });
    }


    @SuppressWarnings("UnusedReturnValue")
    public UserProfileDto saveAsDTO(@NotNull UserProfileDto dto) throws FormatException {
        return mapper.mapToDto(save(dto));
    }

    public void delete(@NotNull String id) throws FormatException, InvalidOperationException, EntityNotExistException {
        HmUserProfile user = getUser(id);
        doDeleteUser(user);
    }

    private void doDeleteUser(HmUserProfile user) {
        UUID userId = user.getId();
        Set<UUID> leagueIds = leagueService.getAllLeaguesByUserInAllSeasons(userId);
        for (UUID leagueId: leagueIds) {
            leagueService.doLeaveLeague(leagueId, user, false);
        }
        awardService.deleteByUserId(userId);
        repository.deleteById(userId);
    }

    public UserProfileDto setPicture(@NotNull UserProfileDto dto, MultipartFile file) throws IOException, EntityNotExistException, FormatException {
        HmUserProfile user = getById(dto.getId());
        imageService.delete(user.getPicture());
        if (file == null) {
            user.setPicture(null);
            dto.setPicture(null);
        } else {
            user.setPicture(imageService.saveAsUUID(file, ImageDomain.USER_PROFILE_PHOTO));
            dto.setPicture(user.getPicture().toString());
            //check image right award
            Optional<HmAwardDescriptionDO> awardDescriptionOptional = awardService.checkImageRightAward(user);
            awardDescriptionOptional.ifPresent(awardDescriptionDO -> awardService.sendNotificationByUserAward(user.getId(), awardDescriptionDO));
        }
        //unnecessary mapping removed fot transaction reasons
        //return mapper.mapToDto(repository.save(user));
        repository.save(user);
        return dto;
    }

    public boolean assignImageRightAwardToUser(HmUserProfile user) {
        return awardService.addImageRightUserAward(user);
    }

    @Nullable
    private UUID getSystemUserPicture() {
        HmUserProfile systemUser = transactionHandler.runInNewTransactionReadOnly(this::getSystemUser);
        return nonNull(systemUser)? systemUser.getPicture(): null;
    }

    public int getImageRightAwardExperiencePoints () {
        return awardService.getImageRightAwardExperiencePoints();
    }

    public void addExperiencePoints(@NotNull UUID userId, Integer experiencePointsToAdd) {
        if (isNull(experiencePointsToAdd)) return;
        int row = repository.addExperiencePointsById(userId, experiencePointsToAdd);
        checkUserProfileUpdate(row, "Failed to update experience points for user id: " + userId + " , skipping...", "addExperiencePoints");
    }

    public UserProfileDto mapToDto(HmUserProfile entity) {
        return mapper.mapToDto(entity);
    }

    public UserDto mapToReducedDto(HmUserProfile entity) {
        return userMapper.mapToDto(entity);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true)
    public UserDto getUserDto(String userId) throws EntityNotExistException, FormatException {
        return userMapper.mapToDto(getById(userId));
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void setUserCurrentLeague(UUID userId, String newCurrentLeague) throws FormatException {
        setSessionAttribute(userId.toString(), SessionAttribute.CURRENT_LEAGUE, newCurrentLeague);
    }

    @Transactional(readOnly = true)
    public List<HmUserLevel> getSortedUserLevels() {
        List<HmUserLevel> sortedUserLevels;
        sortedUserLevels = new ArrayList<>(this.sortedUserLevelsCache);
        if (sortedUserLevels.isEmpty()) {
            List<HmUserLevel> userLevels = Util.toStream(userLevelRepository.findAll()).toList();
            sortedUserLevels = new ArrayList<>(userLevels);
            sortedUserLevels.sort(Comparator.comparing(HmUserLevel::getExperiencePoints));
            log.info("UserProfileService: sortedUserLevelsCache is empty, it will be retrieved from the database.");
        }
        return sortedUserLevels;
    }

    public boolean existsBySsoSubjectId (String ssoSubjectId) {
        return repository.existsBySsoIdIgnoreCase(ssoSubjectId);
    }

    public boolean updatePremiumStatus(String userId, boolean premium, ZonedDateTime premiumExpiration) {
        return updatePremiumStatus(UUID.fromString(userId), premium, isNull(premiumExpiration)? null: premiumExpiration.toLocalDateTime());
    }

    public boolean updatePremiumStatus(UUID userId, boolean premium, LocalDateTime premiumExpiration) {
        boolean premiumStatusChanged = getUser(userId.toString()).isPremium() != premium;
        String message;
        int rows;
        String columns;

        if (premiumStatusChanged) {
            rows = repository.updatePremiumAndPremiumExpirationById(userId, premium, premiumExpiration);
        } else {
            rows =  repository.updatePremiumExpirationById(userId, premiumExpiration);
        }
        if (rows == 0) {
            columns = premiumStatusChanged? "premium and premium_expiration" : "premium_expiration";
            message = "Failed to update " + columns + " for user id[" + userId + "], skipping...";
            log.info(message);
            logMessageService.logException("updatePremiumStatus", new TechnicalException(DATABASE_UPDATE_FAILED, message));
            return false;
        } else {
            // if the user is already premium, we receive a renewal notification so the on-hold leagues should not be automatically
            // activated. These memberships correspond to the leagues that he already left
            if (premium && premiumStatusChanged) {
                leagueService.activateOnHoldLeaguesByUser(userId);
            }
            columns = premiumStatusChanged? "premium [" + premium + "] and premium_expiration [" + premiumExpiration + "]" : "premium_expiration [" + premiumExpiration + "]";
            message = columns + " updated for user id[" + userId + "]";
            log.info(message);
            return true;
        }
    }

    private HmUserProfile getUser(String id) {
        return transactionHandler.runInNewTransactionReadOnly(()-> {
            try {
                return getById(id);
            } catch (Exception e) {
                return null;
            }
        });
    }

    public void downgradeToFree(UserProfileDto dto, HttpServletResponse response) throws FormatException {
        HmUserProfile user = getUser(dto.getId());
        if (isFreeUser(user)) {
            log.info("downgrade to free failed for user id[" + dto.getId() + "]: user has already free role");
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        } else {
            dto.setPremium(false);
            saveAsDTO(dto);
        }
    }

    private boolean isFreeUser(HmUserProfile userProfile) {
        return !userProfile.isPremium();
    }

    @Transactional(readOnly = true)
    public Boolean usernameAvailable(String name) throws TechnicalException {
        List<HmUserProfile> existingUsers = repository.findByUsernameIgnoreCase(name);
        boolean userIsPresent = !existingUsers.isEmpty();
        return !(userIsPresent || isNameBlackListed(name));
    }

    @Transactional(propagation = REQUIRES_NEW, readOnly = true)
    public void validateUsernameIsNotExistingOrBlacklisted(String operation, String name, String userId) throws InvalidOperationException, TechnicalException {
        if (!usernameAvailable(name)) throw new InvalidOperationException(operation, userId, "Username: " + name + " already exists or blacklisted");
    }

    /**
     * Admin method to update experience points and level of all users based on the managers scores in a given round
     *
     * @param roundId id of the round of which the score will be retrieved
     */
    @Lock(LockModeType.PESSIMISTIC_WRITE)
    public void updateAllUserLevels(String roundId) {
        // update experience points levels for all users with positive total score (score over all leagues). No change for users with negative total score.
        List<HmUserTotalScoreDO> usersWithPositiveTotalScore = transactionHandler.runInNewTransactionReadOnly(() ->  leagueScoreHandler.getUsersWithPositiveTotalScore(UUID.fromString(roundId)));
        int usersSize = usersWithPositiveTotalScore.size();
        log.info("updateAllUserLevels: usersWithPositiveTotalScore count = " + usersSize);
        updateAllExperiencePointsAndLevels(usersWithPositiveTotalScore);
    }

    /**
     * Resets the experience points and levels of users: subtracts the experience points gained by a positive total score in round.
     *
     * @param usersWithPositiveTotalScore users to which the experience points and levels will be reset
     * @param usersSize the number of users to reset
     */
    public void resetUserExperiencePointsAndLevel(List<HmUserTotalScoreDO> usersWithPositiveTotalScore, int usersSize) {
        List<HmUserLevel> sortedUserLevels = transactionHandler.runInNewTransactionReadOnly(this::getSortedUserLevels);
        List<Integer> levelsExperiencePoints = sortedUserLevels.stream().map(HmUserLevel::getExperiencePoints).toList();
        AtomicInteger counter = new AtomicInteger(1);
        usersWithPositiveTotalScore.stream().parallel().forEach(userTotalScoreDO ->
                // IMPORTANT: transaction handler added to ensure that each thread runs in a transaction (required)
                transactionHandler.runInTransaction(() -> {
                    updateExperiencePointsAndLevelByUser(sortedUserLevels, levelsExperiencePoints, userTotalScoreDO, usersSize, counter, true);
                    return null;
                })
        );
    }

    /**
     * Updates the experience points and levels of users: adds the experience points gained by a positive total score in round.
     *
     * @param usersWithPositiveTotalScore users to which the experience points and levels will be reset
     */
    public void updateAllExperiencePointsAndLevels(List<HmUserTotalScoreDO> usersWithPositiveTotalScore) {
        List<HmUserLevel> sortedUserLevels = transactionHandler.runInNewTransactionReadOnly(this::getSortedUserLevels);
        List<Integer> levelsExperiencePoints = sortedUserLevels.stream().map(HmUserLevel::getExperiencePoints).toList();
        AtomicInteger counter = new AtomicInteger(1);
        int size = usersWithPositiveTotalScore.size();
        usersWithPositiveTotalScore.stream().parallel().forEach(userTotalScoreDO -> {
            // IMPORTANT: transaction handler added to ensure that each thread runs in a transaction (required)
            transactionHandler.runInTransaction(() -> {
                updateExperiencePointsAndLevelByUser(sortedUserLevels, levelsExperiencePoints, userTotalScoreDO, size, counter, false);
                return null;
            });
            log.info("updateUserLevels: Updating user levels finished for for users " + size);
        });
    }

    private void updateExperiencePointsAndLevelByUser(List<HmUserLevel> sortedUserLevels, List<Integer> levelsExperiencePoints,
                                                      HmUserTotalScoreDO userTotalScore, int usersSize, AtomicInteger counter, boolean resetScore) {
        UUID userId = UUID.fromString(userTotalScore.getUserId());
        int totalScore = userTotalScore.getTotalScoreInRound();
        String message = resetScore? "reset": "update";
        log.info(message + " user XP and level: total score [" + totalScore + "] by user [" + userId + "] found");
        // double-check the total score, since experience points are only valid for positive scores
        if (totalScore > 0) {
            int experiencePointsInRound = totalScore * EXPERIENCE_POINTS_FACTOR;
            int newExperiencePoints = resetScore? userTotalScore.getCurrentExperiencePoints() - experiencePointsInRound
                    : userTotalScore.getCurrentExperiencePoints() + experiencePointsInRound;
            // double check if the Xps still positive after reset
            if (newExperiencePoints >= 0) {
                doUpdateExperiencePointsAndLevelByUser(sortedUserLevels, levelsExperiencePoints, userId, newExperiencePoints);
            } else {
                log.info(message + "ManagerScores: negative experience points for user " + userId + "found. Skipping..");
            }
        }
        counter.getAndIncrement();
        String operation = resetScore? "resetManagerScores: reset": "updateManagerScores: update";
        log.info(operation + " experience points and level for user " + userId + " done (" + counter.get() + " / " + usersSize + ")");
    }

    /**
     * Sets new experience points and calculates the according level for a given user
     *
     * @param userId id of the user to which set the experience points will be set
     * @param experiencePoints new experience points
     */
    public void updateExperiencePointsAndLevelByUser(UUID userId, int experiencePoints) {
        List<HmUserLevel> sortedUserLevels =  transactionHandler.runInNewTransactionReadOnly(this::getSortedUserLevels);
        List<Integer> levelsExperiencePoints = sortedUserLevels.stream().map(HmUserLevel::getExperiencePoints).toList();
        doUpdateExperiencePointsAndLevelByUser(sortedUserLevels, levelsExperiencePoints, userId, experiencePoints);
    }

    private void doUpdateExperiencePointsAndLevelByUser(List<HmUserLevel> sortedUserLevels, List<Integer> levelsExperiencePoints, UUID userId, int experiencePoints) {
        int userLevel = 0;
        // a valid user level should be between [1 - 50] for example, - 1 or 51 are invalid
        boolean validLevelFound = false;
        // the index of the search key, if it is contained in the list; otherwise, (-(insertion point) - 1)
        int index = Collections.binarySearch(levelsExperiencePoints, experiencePoints);
        // the current user XP points exactly corresponds to an XP points entry from the list
        if (index >= 0) {
            userLevel = sortedUserLevels.get(index).getLevel();
            validLevelFound = true;
        } else {
            int insertPoint = -index - 1;
            // the current user XP points is not less than the lowest XP points in the list
            // the current user XP points is not more than the highest XP points in the list
            //if (insertPoint != 0 && insertPoint != levelsExperiencePoints.size()) {
            if (insertPoint > 0 && insertPoint <= levelsExperiencePoints.size()) {
                userLevel = sortedUserLevels.get(insertPoint - 1).getLevel();
                validLevelFound = true;
            }
        }
        // save XP points & level if valid level found
        int rows;
        if (validLevelFound) {
            rows = repository.updateExperiencePointsAndLevelById(userId, experiencePoints, userLevel);
            // save XP points even if level still unchanged (no valid level found)
        } else {
            rows = repository.updateExperiencePointsById(userId, experiencePoints);
        }
        if (rows == 0) {
            String columns = validLevelFound? "experience points and level": " experience points";
            String message = "Failed to update " + columns + " for user id: " + userId + " , skipping...";
            logMessageService.logException("updateUserLevels", new TechnicalException(DATABASE_UPDATE_FAILED, message));
        }
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true)
    public UserProfileDto getUserProfileAsDto(String id) throws EntityNotExistException, FormatException {
        if (id.equals(ANONYMOUS_ID)) {
            return UserProfileDto.getAnonymous();
        }
        return getByIdAsDto(id);
    }

    @Lock(LockModeType.PESSIMISTIC_WRITE)
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void setAppVersion(UUID id, String appVersion) {
        int row = repository.setAppVersionById(id, appVersion);
        checkUserProfileUpdate(row, "Failed to set app version for user id: " + id.toString() + " , skipping...", "setAppVersion");
    }

    @Lock(LockModeType.PESSIMISTIC_WRITE)
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public int downgradeManualSubscription(UUID id) {
        int row = repository.downgradeManualSubscriptionById(id);
        if (row == 0) {
            String message = "Failed to downgrade manual subscription for user id: " + id.toString() + " , skipping...";
            logMessageService.logException("downgradeManualSubscription", new TechnicalException(DATABASE_UPDATE_FAILED, message));
        }
        return row;
    }

    @Lock(LockModeType.PESSIMISTIC_WRITE)
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void setUserAppLanguage(UUID id, String appLanguage) {
        int row = repository.setAppLanguageById(id, appLanguage);
        checkUserProfileUpdate(row, "Failed to set app language for user id: " + id.toString() + " , skipping...", "setUserAppLanguage");
    }

    private void checkUserProfileUpdate(int row, String message, String process) {
        if (row == 0) {
            log.error(message);
            logMessageService.logException(process, new TechnicalException(DATABASE_UPDATE_FAILED, message));
        }
    }

    public void updateAppSettings(String id, String language) throws FormatException {
        setUserAppLanguage(Util.convertId(id), isNull(language)? DEFAULT_APP_LANGUAGE: language);
    }

    @Transactional(propagation = REQUIRES_NEW, readOnly = true)
    public List<HmUserProfile> getAllByIdIn(Set<UUID> userIds) {
        return repository.findAllByIdIn(userIds);
    }

    @Transactional(propagation = REQUIRES_NEW, readOnly = true)
    public List<HmUserSessionAttributesDO> getAllUserSessionAttributes() {return repository.findAllUserSessionAttributes();}

    @Transactional(propagation = REQUIRES_NEW, readOnly = true)
    public HmUserProfile getByUserNameOrEmailAddress(String usernameOrEmail) throws EntityNotExistException {
        Optional<HmUserProfile> optionalHmUserProfile = repository.findByUsernameIgnoreCaseOrEmailAddressIgnoreCase(usernameOrEmail, usernameOrEmail).stream().findFirst();
        if (optionalHmUserProfile.isPresent()) {
            return optionalHmUserProfile.get();
        } else {
            throw new EntityNotExistException(HmUserProfile.class, "username or email address", usernameOrEmail);
        }
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true)
    public UserProfileDto getByUserDtoByNameOrEmailAddress(String usernameOrEmailAddress) throws EntityNotExistException {
        return mapper.mapToDto(getByUserNameOrEmailAddress(usernameOrEmailAddress));
    }

    public void deleteByEmailAddress(String emailAddress) throws EntityNotExistException {
        HmUserProfile user = userProfileAdminHandler.getByEmail(emailAddress);
        doDeleteUser(user);
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true)
    public AdminUsersStatisticsDO getUserStatistics() {
        return userProfileAdminHandler.getUserStatistics(getAll());
    }

    public String registerDummyUser(String username) throws InvalidOperationException {
        return userProfileAdminHandler.registerDummyUser(username);
    }

    @Lock(LockModeType.READ)
    @Transactional(propagation = REQUIRES_NEW, readOnly = true)
    Set<UUID> getExistingUsersFromList(Set<String> userList) {
        return userList.stream().filter(userId -> {
            try {
                return exists(userId);
            } catch (Exception e) {
                return false;
            }
        }).map(UUID::fromString).collect(Collectors.toSet());
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true, propagation = REQUIRES_NEW)
    public String getUserAppLanguage(UUID id) {
        String userAppLanguage = repository.findAppLanguageById(id);
        return isNull(userAppLanguage)? DEFAULT_APP_LANGUAGE: userAppLanguage;
    }

}
