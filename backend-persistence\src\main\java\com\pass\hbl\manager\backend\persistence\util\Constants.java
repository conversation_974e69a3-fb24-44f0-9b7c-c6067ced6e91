package com.pass.hbl.manager.backend.persistence.util;

import java.time.LocalDate;
import java.util.Locale;

public class Constants {
    public static final String SYSTEM_USERNAME = "START7";
    public static final String PERSON_TYPE = "PERSON";
    public static final String ENTITY_TYPE = "ENTITY";
    public static final String IMAGE_TYPE = "LOGO";
    public static final String SYSTEM_EMAIL = "<EMAIL>";
    public static final int DEFAULT_STATISTICS_QUANTITY = 5;
    public static final String LOCAL_PROFILE = "dev";
    public static final String DEV_PROFILE = "DEV";
    public static final String STAGE_PROFILE = "STA";
    public static final String STAGE2_PROFILE = "STA2";
    public static final String STAGE3_PROFILE = "STA3";
    public static final String PROD_PROFILE = "PROD";

    public static final String PROD2_PROFILE = "PROD2";
    public static final String PROD3_PROFILE = "PROD3";
    public static final String PROD4_PROFILE = "PROD4";
    public static final String PROD5_PROFILE = "PROD5";

    public static final Locale DEFAULT_LOCALE = Locale.GERMAN;
    public static final Locale EN_LOCALE = Locale.ENGLISH;


    public static final String END_TRANSFER_AUCTION_JOB = "EndTransferAuctionJob";
    public static final String ADD_SYSTEM_BID = "AddSystemBidJob";
    public static final String ROUND_RESULT_JOB = "RoundResultJob";
    public static final String ROUND_RESULT_NOTIFICATION_JOB = "RoundResultNotificationJob";
    public static final String ROUND_RESULT_UPDATE_JOB = "RoundResultUpdateJob";
    public static final String LEAGUE_SCORE_AWARDS_JOB = "LeagueScoreAwardsJob";
    public static final String CONSISTENCY_CHECK_JOB = "ConsistencyCheckJob";


    // transfer market jobs parameters
    public static final String TRANSFER_MARKET_ID = "transfer_market_id";
    public static final String SYSTEM_PLAYER = "system_player";

    // rounds setup parameters
    public static final int ROUND_END_OFFSET_HOURS = 4;

    // round results job parameters
    public static final String ROUND_ID = "round_id";
    public static final String AUTO_RERUN = "auto_rerun";

    // firebase
    public static final String FCM_ANALYTIC_LABEL = "sent_from_backend";

    public static final double MAX_BID_FACTOR = 7.5;

    public static final String HBL_APP_ICON_DEV = "HBLAppIcon_dev.png";

    public static final String HBL_APP_ICON_STAGE = "HBLAppIcon_stage.png";

    public static final String HBL_APP_ICON_PROD = "HBLAppIcon_prod.png";

    public static final String PLAYER_IDS_FILE = "player_ids.json";
    public static final String DATACORE_MATCH_IDS_FILE = "DataCore_match_ids.json";
    public static final String DATACORE_PLAYER_IDS_FILE = "DataCore_player_ids.json";
    public static final String DATACORE_TEAM_IDS_FILE = "DataCore_team_ids.json";
    public static final String DATACORE_PLAYER_IDS_FILE_PROD = "DataCore_player_ids_prod.json";
    public static final String DATACORE_TEAM_IDS_FILE_PROD = "DataCore_team_ids_prod.json";



    public static final int SCORE_MONEY_FACTOR = 500;
    public static final int EXPERIENCE_POINTS_FACTOR = 10;

    public static final String DEFAULT_FMP_ID = "0";

    public static final String PING_EVENT_NAME = "ping";

    public static final String PING_EVENT_DATA = "ping";

    public static final String SSO_USER_CONFIRMED = "CONFIRMED";
    public static final String COGNITO_USER_UPDATED = "CognitoUser.updated";
    public static final String COGNITO_USER_DELETED = "CognitoUser.deleted";
    public static final String NOT_ASSIGNED = "NOT_ASSIGNED";
    public static final int TRIAL_SUBSCRIPTION_60_DAYS = 60;
    public static final int TRIAL_SUBSCRIPTION_30_DAYS = 30;

    public static final String BLACK_LIST_NAMES_FILE = "Blacklist_names.csv";

    public static final LocalDate TRIAL_SUBSCRIPTION_REFERENCE_DATE = LocalDate.of(2023, 2, 28);

    public static final String ANONYMOUS_ID = "f5b700a4-b9c8-11ed-afa1-0242ac120002";

    public static final String ANONYMOUS_USERNAME = "Anonymous";

    public static final String ANONYMOUS_EMAIL = "<EMAIL>";
    public static final String START_7_APPLICATION = "Start7 Application";
    public static final String APPLE_ROOT_CA_G_3_CER = "AppleRootCA-G3.cer";
    public static final String X_509_CERTIFICATE_TYPE = "X.509";
    public static final String POST_COMMIT_ANNOTATION_EXPRESSION = "com.pass.hbl.manager.backend.persistence.service.shared.PostCommit";
    public static final int MIN_MARKET_VALUE = 10000;
    public static final int MAX_MARKET_VALUE = 2500000;
    public static final int RANDOM_MARKET_VALUE_LOWER_LIMIT = 2400000;
    public static final int ON_HOLD_MEMBERSHIP_MAX_DAYS = 14;
    public static final int POOR_PLAYER_MARKET_VALUE = 15000;
    public static final int POOR_PLAYERS_PERCENTAGE = 75;
    public static final String DEFAULT_APP_VERSION = "1.0.5";
    public static final String DEFAULT_APP_LANGUAGE = DEFAULT_LOCALE.getLanguage();
    public static final String NOTIFICATION = "NOTIFICATION";
    public static final String BODY_SUFFIX = "_BODY";
    public static final String TITLE_SUFFIX = "_TITLE";

    // Disable displaying live tab hours after round end
    public static final int LIVE_RESULTS_DISPLAY_HOURS = 0;
    public static final int DEALMAKER_GOLD_MIN_BID = 1000000;
    public static final int DEALMAKER_BRONZE_MIN_BID = 150000;
    public static final int DEALMAKER_SILVER_MIN_BID = 500000;

    public static final int TRANSFER_KING_GOLD_MIN = 100;
    public static final int TRANSFER_KING_SILVER_MIN = 50;
    public static final int TRANSFER_KING_BRONZE_MIN = 20;

    public static final int POINT_PICKER_GOLD_MIN = 1000;
    public static final int POINT_PICKER_SILVER_MIN = 500;
    public static final int POINT_PICKER_BRONZE_MIN = 250;
    public static final String AWARD_KEY_NAME = "name";
    public static final int ROUND_RESULT_NOTIFICATION_DELAY = 30;
    public static final String TOPIC_NAME = "topic_name";
    //MQTT Topics
    public static final String READ_STREAM_STATUS = "read:stream_status";
    public static final String READ_STREAM_PLAY_PLAY = "read:stream_play_by_play";
    public static final String READ_STREAM_PLAY_PLAY_CATCHUP = "read:stream_play_by_play_catchup";
    public static final String READ_STREAM_STATISTICS = "read:stream_statistics";
    public static final String READ_STREAM_STATISTICS_CATCHUP = "read:stream_statistics_catchup";
    public static final String PERSONS_KEY = "persons";

    public static final String HBL_IMAGE_URL = "https://images.dc.prod.cloud.atriumsports.com/%s/%s?size=raw";

    public static final String TWO_MINUTES = "twoMinutes";
    public static final String FOUR_MINUTES = "fourMinutes";
    public static final String X_RATE_LIMIT_RESET = "X-RateLimit-Reset";
    public static final int API_EXCEPTION_CODE = 429;
    public static final int DEFAULT_ROUND_RESULT_MAX_RETRIES_DAYS = 3;
    public static final int MIN_HOURS_BEFORE_CLOSING = 4;

    public static final String DEFAULT_EXTERNAL_CLIENT = "client_hbl";

    /**
     * Default external client for statistics requests
     * Used for tracking and rate limiting statistics API calls
     */
    public static final String DEFAULT_STAT_EXTERNAL_CLIENT = "client_stat";

    /**
     * Default external client for subscription statistics requests
     * Used for tracking and rate limiting subscription statistics API calls
     */
    public static final String DEFAULT_SUBSCRIPTION_STAT_EXTERNAL_CLIENT = "client_subscription_stat";
    public static final String DEFAULT_LEAGUE_STAT_EXTERNAL_CLIENT = "client_league_stat";

    public static final String ROUNDS = "ROUNDS";
    public static final String LEAGUE_CACHE = "leagueCache";
    public static final String AWARDS_CACHE = "awardsCache";
}
