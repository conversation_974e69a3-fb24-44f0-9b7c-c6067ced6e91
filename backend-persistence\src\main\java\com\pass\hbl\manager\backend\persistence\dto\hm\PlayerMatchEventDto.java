package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonRootName;
import com.pass.hbl.manager.backend.persistence.dto.AbstractDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@AllArgsConstructor
@NoArgsConstructor
@JsonRootName("MatchEvents")
@Getter
@Setter
@Schema(description = "Player match events object")
public class PlayerMatchEventDto extends AbstractDto<PlayerMatchEventDto, String> {

    @NotNull
    @Schema(description = "id of the player match event object", required = true)
    private String id;

    @NotNull
    @Schema(description = "id of the player", required = true)
    private String playerId;

    @Schema(description = "optional id of the match the points were gained in")
    private String matchId;

    @NotNull
    @Schema(description = "event type", required = true)
    private Event event;

    @NotNull
    @Schema(description = "event characteristic", required = true)
    private EventCharacteristic eventCharacteristic;

    @Schema(description = "optional time in minutes when the event in")
    private Integer eventTime;

    @NotNull
    @Schema(description = "score gained by the given event", required = true)
    private int score;

    @Schema(description = "description of the given event")
    private String description;
}
