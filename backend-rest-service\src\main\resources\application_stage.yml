server:
  compression:
    enabled: true
    mime-types: text/html,text/plain,text/css,application/javascript,application/json
    min-response-size: 1024
  port: 8081
  servlet:
    context-path: /api
  error:
    include-message: always
  forward-headers-strategy: NATIVE
  tomcat:
    max-threads: 1000
    max-connections: 15000
spring:
  main:
    allow-circular-references: true
  application:
    name: HBL Manager Backend
  datasource:
    url: **********************************************
    # connection with only primary node
    #url: ************************************,*************:5432/handball_manager?targetServerType=primary
    # connection with the local database running in docker container
    #url: ************************************************
    username: hbl
    password: hbl
    driver-class-name: org.postgresql.Driver
    hikari:
      idle-timeout: 60000
      maximum-pool-size: 3000
      minimum-idle: 20
      connectionTimeout: 50000
  jpa:
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        jdbc:
          batch_size: 50
          time_zone: UTC
        ddl-auto: none
        order_insert: true
        order_update: true
    open-in-view: false
  liquibase:
    change-log: classpath:/db/db.changelog-master.yml
  cache:
    type: redis    # Enable Redis caching

  redis:
    host: 127.0.0.1  # Redis host (adjust for your setup, e.g., localhost or Redis server IP)
    port: 6379       # Redis default port
    password: 'tfhq8B4Q/J1fM2&jFM(='   # Set Redis password if needed (e.g., 'yourpassword')
    timeout: 1000ms  # Timeout for Redis connection
    lettuce:
      pool:
        max-active: 10000        # Max number of active Redis connections
        max-idle: 5           # Max number of idle Redis connections
        min-idle: 1           # Min number of idle Redis connections
        max-wait: 1000ms     # Max wait time when no connections are available
    jedis:
      pool:
        max-active: 10000        # Max number of active Redis connections
        max-idle: 5           # Max number of idle Redis connections
        min-idle: 1           # Min number of idle Redis connections
        max-wait: 1000ms     # Max wait time when no connections are available
management:
  server:
    port: 8082
  endpoint:
    chaosmonkey:
      enabled: true
    chaosmonkeyjmx:
      enabled: true
    health:
      group:
        custom:
          include: datasource,ping,diskspace
          show-components: always
          show-details: always
  metrics:
    tags:
      application: ${spring.application.name}
    distribution:
      percentiles:
        http:
          server:
            requests: 0.5, 0.9, 0.95, 0.99
  endpoints:
    web:
      exposure:
        include: "*"
chaos:
  monkey:
    enabled: true
    watcher:
      service: true
      controller: true
      rest-controller: true
      repository: true
      component: false
handball-manager:
  importer:
    sportradar-stage: production
    # noinspection SpellCheckingInspection
    api-key: n4gdhnm9d53kyypetumvkzjq
  datacore:
    scheduler-enabled: false
    staging-base-url: https://api.dc.stg.connect-nonprod.sportradar.dev/v1
    staging-auth-url: https://token.stg.connect-nonprod.sportradar.dev/v1/oauth2/rest/token
    staging-streaming-auth-url: https://token.stg.connect-nonprod.sportradar.dev/v1/stream/fixture/access
    staging-client-id: 137ZsT1EqzFPvlb1jOSaJaOXAz0SDx
    staging-client-secret: 1Sa7B4Mwa7AVn7WvF0Qh1qEijbhaJv
    # prod parameters
    prod-base-url: https://api.dc.connect.sportradar.com/v1
    prod-auth-url: https://token.connect.sportradar.com/v1/oauth2/rest/token
    prod-streaming-auth-url: https://token.connect.sportradar.com/v1/stream/fixture/access
    # Stage credentials
    prod-client-id: 341Zyfg2L2xqb4v1rNysOrp5NX40Ts
    prod-client-secret: azMT1zYyGfzBERncXCkh3zfqmlPvJF
  security:
    api-key: 0cb1ca734d51c1379545h29f52a337fd
    hbl-api-key: 01939200bf6e7d129210a75194eaeb63
    stat-api-key: 01939200bf6e7d129210a75194eaeb67
    magic-token: 1234567890
    hbl-magic-token: 9619800525
    stat-magic-token: 9619800527
    admin-user: admin
    admin-password: admin
    dev-issuer-uri: https://signin.dev-bookaroundme.com
    dev-jwk-set-uri: https://signin.dev-bookaroundme.com/certs
    prod-issuer-uri: https://login.liquimoly-hbl.de
    prod-jwk-set-uri: https://login.liquimoly-hbl.de/certs
    prod-issuer-uri-v2: https://login.handball-bundesliga.de
    prod-jwk-set-uri-v2: https://login.handball-bundesliga.de/certs
  http-proxy:
    enabled: true
    url: http://10.10.0.250:3128
  amazon-notification-service:
    region: eu-central-1
    # noinspection SpellCheckingInspection
    dev-access-key: ********************
    # noinspection SpellCheckingInspection
    dev-secret-key: 4hxmnYiXmTlvM4o6jzJyg23BUZvrtBdDl8Y5S4of
    sns-dev-topic: arn:aws:sns:eu-central-1:108154344453:oidc-iam-topic
    sns-dev-endpoint: https://hbl-dev.pass-consulting.com/api/v1/sns
    sns-stage-endpoint: https://hbl-sta.pass-consulting.com/api/v1/sns
    sns-prod-endpoint: https://hbl.pass-consulting.com/api/v1/sns
    # noinspection SpellCheckingInspection
    prod-access-key: ********************
    # noinspection SpellCheckingInspection
    prod-secret-key: kb+siQFE95CISi/o5tdjkbhBCkadV2BJm8pArX2B
    sns-prod-topic: arn:aws:sns:eu-central-1:749340686513:oidc-iam-topic
firebase-cloud-messaging:
  realtime-database:
    # noinspection SpellCheckingInspection
    name: start7-ca512-default-rtdb
    location: europe-west1
logging:
  config: file:/conf/logback-spring.xml
