package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonRootName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@JsonRootName("Player")
@Getter
@Setter
@NoArgsConstructor
@Schema(description = "Reduced player info object")
public class PlayerLobbyDto extends AbstractPictureDto<PlayerLobbyDto, String> {

    @Schema(description = "Player id", example = "005aa59d-81d9-41eb-9df7-94867a1f7542", required = true)
    @NotBlank
    @Size(min = 36, max = 36)
    private String id;

    @NotBlank
    @Schema(description = "First name", example = "John", required = true)
    private String firstName;

    @NotBlank
    @Schema(description = "Last name", example = "Doe", required = true)
    private String lastName;

    @NotNull
    @Schema(description = "Position of the player", required = true)
    private Position position;

    @Schema(description = "Current market value", required = true)
    private Integer marketValue;

    @Schema(description = "Hbl URL of the image", example = "663678a4b6344926a8ac580bc0398884")
    private String hblImageId;

    public PlayerLobbyDto(String id, String firstName, String lastName, Position position, Integer marketValue, String picture, String hblImageId) {
        this.id = id;
        this.firstName = firstName;
        this.lastName = lastName;
        this.position = position;
        this.marketValue = marketValue;
        this.setPicture(picture);
        this.hblImageId = hblImageId;
    }
}
