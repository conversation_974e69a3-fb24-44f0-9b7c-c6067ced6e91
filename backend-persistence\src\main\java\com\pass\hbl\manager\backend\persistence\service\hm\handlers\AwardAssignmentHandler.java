package com.pass.hbl.manager.backend.persistence.service.hm.handlers;

import com.pass.hbl.manager.backend.persistence.domain.hm.HmAwardDescriptionDO;
import com.pass.hbl.manager.backend.persistence.domain.hm.HmLeagueMemberAwardsDO;
import com.pass.hbl.manager.backend.persistence.domain.hm.HmLeagueOwnerDO;
import com.pass.hbl.manager.backend.persistence.dto.hm.AwardCode;
import com.pass.hbl.manager.backend.persistence.entity.hm.*;
import com.pass.hbl.manager.backend.persistence.entity.shared.SharedLocalization;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.exception.InvalidOperationException;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmUserAwardRepository;
import com.pass.hbl.manager.backend.persistence.service.admin.LogMessageService;
import com.pass.hbl.manager.backend.persistence.service.hm.LeagueService;
import com.pass.hbl.manager.backend.persistence.service.hm.SeasonService;
import com.pass.hbl.manager.backend.persistence.service.hm.UserProfileService;
import com.pass.hbl.manager.backend.persistence.service.shared.TransactionHandler;
import com.pass.hbl.manager.backend.persistence.util.Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.pass.hbl.manager.backend.persistence.dto.hm.AwardCode.FULL_HOUSE;
import static com.pass.hbl.manager.backend.persistence.util.Constants.AWARD_KEY_NAME;
import static java.util.Objects.nonNull;
import static java.util.Optional.empty;

@Transactional
@Service
@Slf4j
public class AwardAssignmentHandler {

    private final HmUserAwardRepository hmUserAwardRepository;

    private final UserProfileService userProfileService;
    private final LeagueService leagueService;
    private final LogMessageService logMessageService;
    private final SeasonService seasonService;

    private final LeagueMembershipHandler leagueMembershipHandler;
    private final TransactionHandler transactionHandler;

    public AwardAssignmentHandler(HmUserAwardRepository hmUserAwardRepository, UserProfileService userProfileService, LeagueService leagueService, LogMessageService logMessageService, SeasonService seasonService, LeagueMembershipHandler leagueMembershipHandler, TransactionHandler transactionHandler) {
        this.hmUserAwardRepository = hmUserAwardRepository;
        this.userProfileService = userProfileService;
        this.leagueService = leagueService;
        this.logMessageService = logMessageService;
        this.seasonService = seasonService;
        this.leagueMembershipHandler = leagueMembershipHandler;
        this.transactionHandler = transactionHandler;
    }

    public HmUserAward assignUserAward(UUID userId, UUID leagueId, HmAward award) throws InvalidOperationException, EntityNotExistException {
        UUID awardId = award.getId();
        // load the user award read only to avoid loading the attributes: user, league and award in write-transaction
        Optional<HmUserAward> hmUserAwardOptional = transactionHandler.runInNewTransactionReadOnly(() ->
                Util.toStream(hmUserAwardRepository.findByUserProfileIdAndAwardIdAndLeagueId(userId, awardId, leagueId)).toList().stream().findFirst());
        HmUserAward hmUserAward;
        // update existing user award if available otherwise create a new one
        if (hmUserAwardOptional.isPresent()) {
            hmUserAward = hmUserAwardOptional.get();
            if (award.getStackable()) {
                hmUserAwardRepository.incrementNumberOfAchievements(hmUserAward.getId());
                if (nonNull(award.getMoney()))
                    updateBalance(award, hmUserAward.getUserProfile(), hmUserAward.getLeague());
                if (nonNull(award.getExperiencePoints()))
                    updateExperiencePoints(award, hmUserAward.getUserProfile().getId());
            } else {
                // if not stackable award will be unchanged
                throw new InvalidOperationException("Assign user award", userId, String.format("Non-stackable award id %s is already assigned to user", awardId));
            }
        } else {
            HmUserProfile user = userProfileService.getByIdInNewTransaction(userId);
            HmLeague league = leagueService.getByIdInNewTransaction(leagueId);
            if (nonNull(award.getMoney())) updateBalance(award, user, league);
            if (nonNull(award.getExperiencePoints())) updateExperiencePoints(award, user.getId());
            hmUserAward = hmUserAwardRepository.save(new HmUserAward(award, user, league));
        }
        return hmUserAward;
    }

    public Optional<HmLeagueMemberAwardsDO> assignLeagueWinnerAward(UUID leagueId, HmAward award, HmUserProfile user, HmAwardDescriptionDO awardDescription) throws EntityNotExistException, InvalidOperationException {
        UUID awardId = award.getId();
        // No update league winner user award is already available for the given user and league
        if (hmUserAwardRepository.findIdByUserIdAndAwardIdAndLeagueId(user.getId(), awardId, leagueId).isEmpty()) {
            HmLeague league = leagueService.getByIdInNewTransaction(leagueId);
            if (nonNull(award.getMoney()) && award.getMoney() != 0) updateBalance(award, user, league);
            if (nonNull(award.getExperiencePoints()) && award.getExperiencePoints() != 0) updateExperiencePoints(award, user.getId());
            hmUserAwardRepository.save(new HmUserAward(award, user, league));
            // experience points by awards are already added to user, no need to set it explicitly.
            return Optional.of(new HmLeagueMemberAwardsDO(user.getId(), leagueId, league.getName(), 0, List.of(awardDescription)));
        } else {
            String message = "assignLeagueWinnerAward: league winner award [" + awardId + "] found for league [" + leagueId + "] and user [" + user.getId() + "]";
            log.info(message);
            logMessageService.logInfo("assignAwardsToAllLeagueWinners", message);
            return empty();
        }
    }

    private void updateBalance(HmAward award, HmUserProfile user, HmLeague league) throws InvalidOperationException {
        try {
            leagueService.addMoneyToBalance(user, league, award.getMoney());
        } catch (EntityNotExistException e) {
            throw new InvalidOperationException("Assign user award", user.getId().toString(), String.format("Membership for league id %s does not exist for user", league.getId().toString()));
        }
    }

    private void updateExperiencePoints(HmAward award, UUID userId) {
        userProfileService.addExperiencePoints(userId, award.getExperiencePoints());
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public Pair<List<HmUserProfile>, List<HmLeagueMemberAwardsDO>> assignAllFullHouseAwards(HmAward award) throws EntityNotExistException {
        HmRound currentRound = seasonService.getCurrentRound();
        LocalDateTime now = LocalDateTime.now();
        List<HmLeagueMemberAwardsDO> leagueMemberAwardsResult = new ArrayList<>();
        List<HmUserProfile> userCache = new ArrayList<>();
        if (now.isAfter(currentRound.getClosing()) && now.isBefore(currentRound.getTo())) {
            log.info("assignAllFullHouseAwards: disabled in order to keep user balance on round closing unchanged. Reason: we are now in interval [closing - end] of current round");
        } else {
            UUID currentSeasonId = seasonService.getCurrentSeason().getId();
            List<UUID> leagueIdsWithFullMemberships = leagueMembershipHandler.getLeagueIdsWithFullMemberships(currentSeasonId);
            log.info("assignAllFullHouseAwards: leagueIdsWithFullMemberships count = " + leagueIdsWithFullMemberships.size());
            List<HmLeagueOwnerDO> hmLeagueOwnersByLeagueIds = leagueService.getHmLeagueOwnersByLeagueIds(leagueIdsWithFullMemberships);
            int size = hmLeagueOwnersByLeagueIds.size();
            log.info("assignAllFullHouseAwards: LeagueOwners count = " + size);
            AtomicInteger counter = new AtomicInteger(1);

            // Create a map from the list to handle multiple full house awards user
            Map<UUID, List<UUID>> leaguesByUserMap = hmLeagueOwnersByLeagueIds.stream()
                    .collect(Collectors.groupingBy(
                            HmLeagueOwnerDO::getOwnerId,  // group by ownerId
                            Collectors.mapping(HmLeagueOwnerDO::getId, Collectors.toList()) // Map to a list of leagueIds
                    ));
            leaguesByUserMap.entrySet().stream().parallel().forEach(entry -> {
                try {
                    transactionHandler.runInNewTransaction(() -> {
                                assignFullHouseAwardsToUser(award, leagueMemberAwardsResult, userCache, entry);
                                return null;
                            }
                    );
                    log.info("assignAllFullHouseAwards: user with id [" + entry.getKey() + "] done (" + counter + " / " + size + ")");
                } catch (Exception e) {
                    String msg = "assignAllFullHouseAwards: failed user with id [" + entry.getKey() + "]. Skipping..";
                    log.error(msg, e);
                    logMessageService.logException(getClass().getSimpleName(), msg, e);
                }
                counter.getAndIncrement();
            });
        }
        return Pair.of(userCache, leagueMemberAwardsResult);
    }

    private void assignFullHouseAwardsToUser(HmAward award, List<HmLeagueMemberAwardsDO> leagueMemberAwardsResult, List<HmUserProfile> userCache, Map.Entry<UUID, List<UUID>> entry) {
        List<UUID> leagueIds = entry.getValue();
        UUID ownerId = entry.getKey();
        HmUserProfile owner = null;
        AtomicInteger numberOfAwardsByUser = new AtomicInteger(0);
        AtomicInteger counter = new AtomicInteger(1);
        int size = leagueIds.size();
        for (UUID leagueId : leagueIds) {
            int moneyByFullHouseAward;
            try {
                boolean userAwardExists = hmUserAwardRepository.findIdByUserIdAndAwardIdAndLeagueId(ownerId, award.getId(), leagueId).isPresent();
                if (userAwardExists) {
                    log.info("award with code [" + FULL_HOUSE + "] and id [" + award.getId() + "] found for league [" + leagueId
                            + "] and user [" + ownerId + "] . Skipping..");
                } else {
                    // get user profile from cache since the user could have multiple full house leagues
                    // search in tempUserCache to avoid ConcurrentModification since it could be modified from another thread
                    List<HmUserProfile> tempUserCache = new ArrayList<>(userCache);
                    Optional<HmUserProfile> userProfileOptional = tempUserCache.stream().filter(u -> Objects.equals(u.getId(), ownerId)).findFirst();
                    if (userProfileOptional.isPresent()) {
                        owner = userProfileOptional.get();
                    } else {
                        owner = userProfileService.getByIdInNewTransaction(ownerId);
                        userCache.add(owner);
                    }
                    HmLeague league = leagueService.getByIdInNewTransaction(leagueId);
                    HmUserAward userAward = hmUserAwardRepository.save(new HmUserAward(award, owner, league));
                    log.info("award with code [" + FULL_HOUSE + "] and id[" + userAward.getId()
                            + "] assigned to user [" + ownerId + "] in league [" + leagueId + "] ");
                    // count how often should the experience points by full house award added to user
                    if (nonNull(award.getExperiencePoints())) {
                        // increment the number of full house awards by user
                        numberOfAwardsByUser.getAndIncrement();
                    }
                    moneyByFullHouseAward = nonNull(award.getMoney()) ? award.getMoney() : 0;
                    if (moneyByFullHouseAward > 0) {
                        transactionHandler.runInTransaction(() -> leagueService.addMoneyToUserBalance(leagueId, ownerId, moneyByFullHouseAward));
                        // check if user is currently negative on round closing skipped since this method could not be applied
                        // in interval [start - closing] of the current round
                    }
                    // add award LeagueMemberAwardsDO to list: encapsulates the league membership and the award information
                    Locale locale = Util.getLocaleByLanguageTag(owner.getAppLanguage());
                    HmAwardDescriptionDO awardDescriptionDo = getFullHouseAwardDescriptionByLocale(award, locale);
                    leagueMemberAwardsResult.add(new HmLeagueMemberAwardsDO(ownerId, leagueId, league.getName(), award.getExperiencePoints(), List.of(awardDescriptionDo)));
                }
            } catch (Exception e) {
                String msg = "assignAllFullHouseAwards:assignFullHouseAwardsToUser failed user with id [" + ownerId + "] in league [" + leagueId + "]. Skipping..";
                log.error(msg, e);
                logMessageService.logException(getClass().getSimpleName(), msg, e);
            }
            log.info("assignAllFullHouseAwards:assignFullHouseAwardsToUser user with id [" + ownerId + "] in league [" + leagueId + "] done (" + counter + " / " + size + ")");
            counter.getAndIncrement();
        }
        int countAwardsByUser = numberOfAwardsByUser.get();
        log.info("assignAllFullHouseAwards:assignFullHouseAwardsToUser user with id [" + ownerId + "] has " + countAwardsByUser + " full house awards");
        if (nonNull(owner) && countAwardsByUser > 0) {
            int newExperiencePoints = owner.getExperiencePoints() + (countAwardsByUser * award.getExperiencePoints());
            userProfileService.updateExperiencePointsAndLevelByUser(owner.getId(), newExperiencePoints);
        }
    }

    @NotNull
    private HmAwardDescriptionDO getFullHouseAwardDescriptionByLocale(HmAward award, Locale locale) {
        Optional<SharedLocalization> localizationOptional = award.getDescriptions().stream().filter(localization ->
                Objects.equals(localization.getLocale().toString(), locale.toString()) && Objects.equals(localization.getKey(), AWARD_KEY_NAME)).findFirst();
        String awardName = localizationOptional.isPresent() ? localizationOptional.get().getValue() : AwardCode.FULL_HOUSE.name();
        return new HmAwardDescriptionDO(AwardCode.FULL_HOUSE, awardName, award.getPicture(), locale, award.getMoney());
    }
}
