package com.pass.hbl.manager.backend.persistence.mapper.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.PlayerDto;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmPlayer;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import com.pass.hbl.manager.backend.persistence.mapper.hm.converters.PlayerStatisticsAggregator;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmPlayerRoundScoreRepository;
import com.pass.hbl.manager.backend.persistence.util.ParameterDefaults;
import org.modelmapper.AbstractConverter;
import org.modelmapper.Converter;
import org.modelmapper.TypeMap;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

import static com.pass.hbl.manager.backend.persistence.util.Constants.HBL_IMAGE_URL;
import static com.pass.hbl.manager.backend.persistence.util.Constants.ROUND_ID;

@Component
public class HmPlayerMapper extends AbstractMapper<HmPlayer, PlayerDto> {

    private final PlayerStatisticsAggregator playerStatisticsAggregator;

    private final HmPlayerRoundScoreRepository playerRoundScoreRepository;


    public HmPlayerMapper(PlayerStatisticsAggregator playerStatisticsAggregator, HmPlayerRoundScoreRepository playerRoundScoreRepository) {
        super(HmPlayer.class, PlayerDto.class);
        this.playerStatisticsAggregator = playerStatisticsAggregator;
        this.playerRoundScoreRepository = playerRoundScoreRepository;
    }

    @Override
    protected void customizeInit() {
        TypeMap<HmPlayer, PlayerDto> e2d = getModelMapper().createTypeMap(HmPlayer.class, PlayerDto.class);
    }

        @Override
    protected PlayerDto customizeMapToDto(PlayerDto playerDto, HmPlayer hmPlayer, Map<String, Object> context) {
        if (context != null && context.containsKey(ROUND_ID)) {
            UUID roundId = UUID.fromString ((String) context.get(ROUND_ID));
            //The player could have player more than one match by round, that's why we sum all his scores
            List<Integer> scoreByPlayerIdAndRoundId = playerRoundScoreRepository.findScoreByPlayerIdAndRoundId(hmPlayer.getId(), roundId);
            if (!scoreByPlayerIdAndRoundId.isEmpty()) {
                int score = scoreByPlayerIdAndRoundId.stream()
                        .filter(Objects::nonNull).mapToInt(Integer::intValue).sum();
                playerDto.setRoundScore(score);
            }
        }
        playerDto.setTotalScore(playerStatisticsAggregator.getTotalScorePlayedRounds(hmPlayer.getId()));
        playerDto.setPlayedGames(playerStatisticsAggregator.getGamesPlayed(hmPlayer.getId()));
        return super.customizeMapToDto(playerDto, hmPlayer, context);
    }
}
