package com.pass.hbl.manager.backend.persistence.service.hm.handlers;

import com.pass.hbl.manager.backend.persistence.domain.hm.*;
import com.pass.hbl.manager.backend.persistence.dto.hm.*;
import com.pass.hbl.manager.backend.persistence.entity.hm.*;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.exception.InvalidOperationException;
import com.pass.hbl.manager.backend.persistence.mapper.hm.HmTransferMarketBidMapper;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmTransferMarketBidRepository;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmTransferMarketRepository;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmUserRoundScoreRepository;
import com.pass.hbl.manager.backend.persistence.service.AbstractService;
import com.pass.hbl.manager.backend.persistence.service.admin.LogMessageService;
import com.pass.hbl.manager.backend.persistence.service.admin.ParameterService;
import com.pass.hbl.manager.backend.persistence.service.hm.*;
import com.pass.hbl.manager.backend.persistence.service.shared.TransactionHandler;
import com.pass.hbl.manager.backend.persistence.util.ParameterDefaults;
import com.pass.hbl.manager.backend.persistence.util.Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.*;

import static com.pass.hbl.manager.backend.persistence.dto.hm.BidStatus.ACCEPTED;
import static com.pass.hbl.manager.backend.persistence.dto.hm.BidStatus.REJECTED;
import static com.pass.hbl.manager.backend.persistence.dto.hm.NotificationEvent.*;
import static com.pass.hbl.manager.backend.persistence.service.hm.helpers.TransferMarketHelper.*;
import static com.pass.hbl.manager.backend.persistence.util.Constants.SYSTEM_USERNAME;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

@Slf4j
@Service
@Transactional
public class TransferMarketBidHandler extends AbstractService<HmTransferMarketBid, TransferMarketBidDto> {

    private final Random RAND = new Random();

    private final HmTransferMarketRepository transferMarketRepository;
    private final HmTransferMarketBidRepository transferMarketBidRepository;

    private final TransferMarketService transferMarketService;
    private final UserProfileService userProfileService;

    private final LeagueService leagueService;
    private final MessagingService messagingService;
    private final TeamService teamService;
    private final ParameterService parameterService;
    private final PlayerService playerService;
    private final SeasonService seasonService;
    private final AwardService awardService;

    private final HmUserRoundScoreRepository userRoundScoreRepository;
    private final HmTransferMarketBidMapper transferMarketBidMapper;

    private final TransferMarketSchedulingHandler schedulingHandler;
    private final TransferMarketInfoHandler transferMarketInfoHandler;
    private final UserNotificationHandler userNotificationHandler;
    private final TransactionHandler transactionHandler;

    private final LogMessageService logMessageService;

    public TransferMarketBidHandler(HmTransferMarketRepository transferMarketRepository,
                                    HmTransferMarketBidRepository transferMarketBidRepository,
                                    @Lazy TransferMarketService transferMarketService,
                                    UserProfileService userProfileService,
                                    @Lazy LeagueService leagueService,
                                    MessagingService messagingService,
                                    TeamService teamService,
                                    ParameterService parameterService,
                                    PlayerService playerService,
                                    SeasonService seasonService,
                                    @Lazy AwardService awardService, HmUserRoundScoreRepository userRoundScoreRepository,
                                    HmTransferMarketBidMapper transferMarketBidMapper,
                                    TransferMarketSchedulingHandler schedulingHandler,
                                    @Lazy TransferMarketInfoHandler transferMarketInfoHandler, UserNotificationHandler userNotificationHandler, TransactionHandler transactionHandler, LogMessageService logMessageService) {
        super(transferMarketBidRepository, transferMarketBidMapper, HmTransferMarketBid.class);
        this.transferMarketRepository = transferMarketRepository;
        this.transferMarketBidRepository = transferMarketBidRepository;
        this.transferMarketService = transferMarketService;
        this.userProfileService = userProfileService;
        this.leagueService = leagueService;
        this.messagingService = messagingService;
        this.teamService = teamService;
        this.parameterService = parameterService;
        this.playerService = playerService;
        this.seasonService = seasonService;
        this.awardService = awardService;
        this.userRoundScoreRepository = userRoundScoreRepository;
        this.transferMarketBidMapper = transferMarketBidMapper;
        this.schedulingHandler = schedulingHandler;
        this.transferMarketInfoHandler = transferMarketInfoHandler;
        this.userNotificationHandler = userNotificationHandler;
        this.transactionHandler = transactionHandler;
        this.logMessageService = logMessageService;
    }

    public TransferMarketBidDto addTransferItemBid(String userId, String transferMarketId, int value) throws EntityNotExistException, FormatException, InvalidOperationException {
        // transfer item
        HmTransferMarket transferMarket = transferMarketService.getByIdInNewTransaction(transferMarketId);
        TransferMarketInfoDo transferMarketInfo = transferMarketInfoHandler.getTransferMarketInfoDo(Util.convertId(transferMarketId));
        // bidder
        HmUserProfile currentUser = userProfileService.getByIdInNewTransaction(userId);
        UserDto bidderDto = userProfileService.getUserDto(userId);
        UUID currentUserId = currentUser.getId();
        // owner
        String ownerId = transferMarketInfo.ownerId().toString();
        UserDto owner = userProfileService.getUserDto(ownerId);
        // league
        String leagueId = transferMarketInfo.leagueId().toString();
        String operation = "Add player bid to transfer market id[" + transferMarketId + "]";
        // player
        String playerId = transferMarketInfo.playerId().toString();
        PlayerDto playerDto = playerService.getByIdAsDtoInNewTransaction(playerId);
        HmPlayer player = playerService.getByIdInNewTransaction(playerId);

        // validate that bidder is member of the league
        Optional<HmLeagueMembership> leagueMembershipOpt = this.leagueService.getMembership(currentUserId, UUID.fromString(leagueId));
        if (leagueMembershipOpt.isEmpty())
            throw new InvalidOperationException(operation, userId, "The user ist not member of the league");

        // validate that the bidder is not the owner of the transfer item
        validateUserIsNotTransferItemOwner(operation, userId, ownerId);

        // validate bid value between 10000 and 7,5 times the current player market value
        validateBidValue(operation, value, userId, playerDto.getMarketValue(), getLowestPlayerBid(userId));

        // create new transfer market bid
        HmTransferMarketBid transferMarketBid = new HmTransferMarketBid(transferMarket, currentUser, value, BidStatus.OPEN);

        // validate the bidder budget is sufficient, knowing that bidder is not authorized to owe more than 20% of current squad value
        int squadValue = teamService.getSquadValue(currentUserId, UUID.fromString(leagueId));
        int totalUserBid = getTotalUserBids(userId, value,UUID.fromString(leagueId));
        validateCurrentBudget(operation, userId, totalUserBid, bidderDto.getId(), leagueMembershipOpt.get().getBalance(), squadValue);

        // save bid after all validation steps passed
        HmTransferMarketBid savedBid = transferMarketBidRepository.save(transferMarketBid);
        // send notification to the owner, if the owner is not the system
        if (!ownerId.equals(userProfileService.getSystemUserId().toString())) {
            String playerName = player.getFirstName() + " " + player.getLastName();
            String leagueName = leagueService.getLeagueNameById(UUID.fromString(leagueId));
            Map<NotificationBodyKeyword, String> keywords = userNotificationHandler.getNotificationBodyKeywords(bidderDto.getUsername(), owner.getUsername(), Integer.toString(value), leagueName, playerName);
            Locale locale = Util.getLocaleByLanguageTagOrDefault(owner.getAppLanguage());
            String notificationId = messagingService.sendNotification(userNotificationHandler.getNotificationTitle(ADD_BID, locale),
                    userNotificationHandler.getNotificationBody(ADD_BID, keywords, locale),
                    ownerId, new TransferMarketNotificationDo(transferMarketId, player.getHblImageId(), savedBid.getId().toString(), leagueId, ADD_BID));
            log.info("Firebase message with id [" + notificationId + "] sent. Event: " + ADD_BID);
        }
        return new TransferMarketBidDto(savedBid.getId().toString(), bidderDto, ownerId, leagueId, playerId, value, BidStatus.OPEN);
    }

    public void addSystemBid(String transferMarketId) throws EntityNotExistException, InvalidOperationException, FormatException {
        // transfer item
        HmTransferMarket transferMarket = transferMarketService.getByIdInNewTransaction(transferMarketId);
        TransferMarketInfoDo transferMarketInfo = transferMarketInfoHandler.getTransferMarketInfoDo(Util.convertId(transferMarketId));
        // system user
        HmUserProfile systemUser = userProfileService.getSystemUser();
        String systemUserId = systemUser.getId().toString();
        // league
        String leagueName = leagueService.getLeagueNameById(transferMarketInfo.leagueId());
        // player
        PlayerDto playerDto = playerService.getByIdAsDtoInNewTransaction(transferMarketInfo.playerId().toString());
        Integer playerMarketValue = playerDto.getMarketValue();
        // owner
        String ownerId = transferMarketInfo.ownerId().toString();
        UserDto owner = userProfileService.getUserDto(ownerId);

        // get random bid value between -10% and +10% of the player market value
        int bidValue;
        int price = transferMarketInfo.price();
        if (price <= playerMarketValue) {
            bidValue = price + ((RAND.nextInt(-10, 10) * price) / 100);
        } else {
            bidValue = playerMarketValue + ((RAND.nextInt(-10, 10) * playerMarketValue) / 100);
        }

        // validate bid value between 10000 and 7,5 times the current player market value
        validateBidValue("Add player bid to transfer market id[" + transferMarketId + "]", bidValue, systemUserId, playerMarketValue, getLowestPlayerBid(systemUserId));

        // save new transfer item bid
        HmTransferMarketBid savedBid = transferMarketBidRepository.save(new HmTransferMarketBid(transferMarket, systemUser, bidValue, BidStatus.OPEN));

        // send notification to the owner: owner could not be the system
        String playerName = playerDto.getFirstName() + " " + playerDto.getLastName();
        Map<NotificationBodyKeyword, String> keywords = userNotificationHandler.getNotificationBodyKeywords(SYSTEM_USERNAME, owner.getUsername(), Integer.toString(bidValue), leagueName, playerName);
        Locale locale = Util.getLocaleByLanguageTagOrDefault(owner.getAppLanguage());
        String notificationId = messagingService.sendNotification(userNotificationHandler.getNotificationTitle(ADD_BID, locale),
                userNotificationHandler.getNotificationBody(ADD_BID, keywords, locale),
                ownerId, new TransferMarketNotificationDo(transferMarketId, playerDto.getHblImageId(), savedBid.getId().toString()
                        , transferMarketInfo.leagueId().toString(), ADD_BID));
        log.info("Firebase message with id [" + notificationId + "] sent. Event: " + ADD_BID);
    }

    // currently, not used
    public TransferMarketBidDto updateTransferItemBid(String userId, String bidId, int value) throws EntityNotExistException, FormatException, InvalidOperationException {
        HmTransferMarketBid transferMarketBid = getById(bidId);
        HmTransferMarket transferMarket = transferMarketBid.getOffer();
        HmLeague league = transferMarket.getLeague();
        Optional<HmLeagueMembership> leagueMembershipOpt = this.leagueService.getMembership(userProfileService.getById(userId), league);
        String operation = "Update transfer market bid id[" + bidId + "]";
        if (leagueMembershipOpt.isPresent()) {
            String bidderId = transferMarketBid.getBidder().getId().toString();
            validateUserIsBidOwner(operation, userId, bidderId);
            validateBidValue(operation, value, userId, transferMarket.getPlayer().getMarketValue(), getLowestPlayerBid(userId));
            int squadValue = teamService.getSquadValue(UUID.fromString(userId), league.getId());

            int totalUserBid = getTotalUserBids(userId, value,league.getId());
            validateCurrentBudget(operation, userId, totalUserBid, bidderId, leagueMembershipOpt.get().getBalance(), squadValue);
            transferMarketBid.setBid(value);
            // send notification, if required
            return transferMarketBidMapper.mapToDto(transferMarketBidRepository.save(transferMarketBid));
        }
        throw new InvalidOperationException(operation, userId, "The user ist not member of the league");
    }

    private int getTotalUserBids(String userId, int value,UUID leagueId) {
        Integer totalUserBidByLeague= transferMarketBidRepository.getTotalUserBidsByLeague(UUID.fromString(userId),leagueId);
        int totalBidByLeague=totalUserBidByLeague!=null ? totalUserBidByLeague : 0;
        return totalBidByLeague + value;
    }

    @Transactional(propagation = Propagation.MANDATORY)
    public AcceptBidNotificationDo acceptTransferMarketBid(UUID userId, UUID bidId, boolean deleteTransferMarket) throws FormatException, EntityNotExistException, InvalidOperationException {

        String operation = "Accept transfer market bid " + bidId + " by user " + userId;
        log.info(operation);
        HmTransferMarketBid winningBid = getById(bidId);
        TransferMarketBidInfoDo winningBidInfo = transferMarketInfoHandler.getBidInfoById(bidId);
        TransferMarketInfoDo transferMarketInfo = transferMarketInfoHandler.getTransferMarketInfoByBidId(bidId);

        // get these objects in separate transaction to avoid updates of these objects during transaction
        //int maxTeamSize = parameterService.getAsInteger(ParameterDefaults.PARAM_MAX_TEAM_SIZE, ParameterDefaults.DEFAULT_MAX_TEAM_SIZE, userProfileService.getSystemUserName());
        int maxTeamSize = teamService.getMaxTeamSize();

        HmPlayer player = playerService.getByIdInNewTransaction(transferMarketInfo.playerId());
        HmUserProfile owner = userProfileService.getByIdInNewTransaction(transferMarketInfo.ownerId());
        HmUserProfile winningBidder = userProfileService.getByIdInNewTransaction(winningBidInfo.bidderId());
        HmLeague league = leagueService.getByIdInNewTransaction(transferMarketInfo.leagueId());

        // validate that current user is the transfer item owner
        boolean isSellerOwnerOfTransferMarketItem = Objects.equals(owner.getId(), userId);
        if (!isSellerOwnerOfTransferMarketItem) {
            throw new InvalidOperationException(operation, userId, "The user ist not authorized to accept a bid of another manager");
        }

        int buyersTeamSize = teamService.getTeamSize(league.getId(), winningBidder.getId());
        if (buyersTeamSize > maxTeamSize) {
            throw new InvalidOperationException(operation, userId, "Buyer " + winningBidder.getId() + " already has " + buyersTeamSize + " players in league " + league.getId());
        }

        UUID systemUserId = userProfileService.getSystemUserId();
        boolean isStart7Seller = Objects.equals(systemUserId, owner.getId());
        boolean isStart7Buyer = Objects.equals(systemUserId, winningBidder.getId());

        // check that seller owns the player to sell
        boolean isPlayerOwnedByUser = teamService.isPlayerOwnedByUser(owner.getId(), league.getId(), player.getId());
        if (!isStart7Seller && !isPlayerOwnedByUser) {
            throw new InvalidOperationException(operation, userId, "The user " + owner.getId() + " does not own player " + player.getId() + " in league " + league.getId());
        }

        // double check that player is not in league
        if (isStart7Seller && teamService.playerExistsInLeague(player.getId(), league.getId())) {
            throw new InvalidOperationException(operation, userId, "Player " + player.getId() + " already exists in league " + league.getId());
        }

        // check league memberships
        if (!isStart7Seller && !leagueService.isMember(owner.getId(), league.getId())) {
            throw new InvalidOperationException(operation, userId, "Owner " + owner.getId() + " is not member of league " + league.getId());
        }
        if (!isStart7Buyer && !leagueService.isMember(winningBidder.getId(), league.getId())) {
            throw new InvalidOperationException(operation, userId, "Bidder " + winningBidder.getId() + " is not member of league " + league.getId());
        }

        // now everything is fine. Start the player transaction

        // update bid status

        List<HmTransferMarketBid> rejectedBids = Util.toStream(transferMarketBidRepository.findAllByOfferId(transferMarketInfo.id()))
                .filter(bid -> !(bid.getId().equals(bidId)))
                .peek(bid -> bid.setStatus(REJECTED))
                .toList();
        winningBid.setStatus(ACCEPTED);

        LocalDateTime now = LocalDateTime.now();

        // the list of awards that a user (seller or buyer) have earned by this auction
        Map<UUID, List<HmAwardDescriptionDO>> awardsByUserId = new HashMap<>();

        // if bidder is the system, the owner can only be a manager, since the system doesn't add bids to his own transfer items.
        // Manager accepts system bid
        if (isStart7Buyer) {

            // remove player from users team
            HmTeam leftMember = teamService.getTeamMember(owner.getId(), league.getId(), player.getId())
                    .orElseThrow(() -> new InvalidOperationException(operation, userId, "The user " + owner.getId() + " does not own player " + player.getId() + " in league " + league.getId()));
            leftMember.setLeft(LocalDateTime.now());
            // TODO the delete has to be removed as soon as the "left-logic" is implemented fully
            teamService.save(leftMember);
            teamService.deleteTeamById(leftMember.getId());

            // check if user currently is negative on round closing
            checkUserRoundScore(owner.getId(), league.getId(), now);

            // add money to seller's league based balance
            if (!leagueService.addMoneyToUserBalance(league.getId(), owner.getId(), winningBid.getBid())) {
                throw new InvalidOperationException(operation, userId, "Could not add " + winningBid.getBid() + " balance to user " + owner.getId() + " in league " + league.getId());
            }

            // System accepts manager bid
        } else if (isStart7Seller) {

            // add player to users team
            HmTeam joinedMember = new HmTeam(winningBidder, league, player, LocalDateTime.now());
            teamService.save(joinedMember);

            // check if user currently is negative on round closing
            checkUserRoundScore(winningBidder.getId(), league.getId(), now);

            // Check the buyer's awards (transfer-king, deal-maker, full-bench) . In case het gets awards, the according money will be added to user balance otherwise money = 0
            int countUserBuysAndSalesInLeague = transactionHandler.runInNewTransactionReadOnly(() -> transferMarketRepository.countUserBuysAndSalesInLeague(winningBidder.getId(), league.getId()));
            Pair<Integer, List<HmAwardDescriptionDO>> moneyByBuyerAwardsPair = awardService.getMoneyByTransferMarketBuyerAwards(transferMarketInfo.id(), league, winningBidder, new AwardCheckingDo(countUserBuysAndSalesInLeague, winningBid.getBid(), (buyersTeamSize + 1), maxTeamSize, Util.getLocaleByLanguageTag(winningBidder.getAppLanguage())));
            int moneyByBuyerAwards = moneyByBuyerAwardsPair.getKey();
            List<HmAwardDescriptionDO> awardDescriptionsByBuyer = moneyByBuyerAwardsPair.getValue();
            if (!awardDescriptionsByBuyer.isEmpty()) {
                awardsByUserId.put(winningBidder.getId(), awardDescriptionsByBuyer);
            }

            // remove money from buyer's league based balance and add the money earned by league winner award (0 if not exists)
            if (!leagueService.addMoneyToUserBalance(league.getId(), winningBidder.getId(), (-1 * winningBid.getBid()) + moneyByBuyerAwards)) {
                throw new InvalidOperationException(operation, userId, "Could not remove balance " + winningBid.getBid() + " from user " + winningBidder.getId() + " in league " + league.getId());
            }

            // manager to manager transaction
        } else {

            // remove player from users team
            HmTeam leftMember = teamService.getTeamMember(owner.getId(), league.getId(), player.getId())
                    .orElseThrow(() -> new InvalidOperationException(operation, userId, "The user " + owner.getId() + " does not own player " + player.getId() + " in league " + league.getId()));
            // TODO the delete has to be replaced by an update with left as soon as the "left-logic" is implemented fully (do not forget to change DB index)
            teamService.deleteTeamById(leftMember.getId());

            // add player to users team
            HmTeam joinedMember = new HmTeam(winningBidder, league, player, LocalDateTime.now());
            teamService.save(joinedMember);

            // check if user currently is negative on round closing
            checkUserRoundScore(owner.getId(), league.getId(), now);
            checkUserRoundScore(winningBidder.getId(), league.getId(), now);

            // Check the seller's awards (transfer-king)
            int countUserBuysAndSalesInLeague = transactionHandler.runInNewTransactionReadOnly(() -> transferMarketRepository.countUserBuysAndSalesInLeague(owner.getId(), league.getId()));
            Pair<Integer, List<HmAwardDescriptionDO>> moneyBySellerAwardsPair = awardService.getMoneyByTransferMarketSellerAwards(transferMarketInfo.id(), league, owner, new AwardCheckingDo(countUserBuysAndSalesInLeague, Util.getLocaleByLanguageTag(owner.getAppLanguage())));
            int moneyBySellerAwards = moneyBySellerAwardsPair.getKey();
            List<HmAwardDescriptionDO> awardDescriptionsBySeller = moneyBySellerAwardsPair.getValue();
            if (!awardDescriptionsBySeller.isEmpty()) {
                awardsByUserId.put(owner.getId(), awardDescriptionsBySeller);
            }

            // add money to seller's league based balance
            if (!leagueService.addMoneyToUserBalance(league.getId(), owner.getId(), winningBid.getBid() + moneyBySellerAwards)) {
                throw new InvalidOperationException(operation, userId, "Could not add balance to user " + owner.getId() + " in league " + league.getId());
            }

            // Check the buyer's awards (transfer-king, deal-maker, full-bench)
            countUserBuysAndSalesInLeague = transactionHandler.runInNewTransactionReadOnly(() -> transferMarketRepository.countUserBuysAndSalesInLeague(winningBidder.getId(), league.getId()));
            Pair<Integer, List<HmAwardDescriptionDO>> moneyByBuyerAwardsPair = awardService.getMoneyByTransferMarketBuyerAwards(transferMarketInfo.id(), league, winningBidder, new AwardCheckingDo(countUserBuysAndSalesInLeague, winningBid.getBid(), (buyersTeamSize + 1), maxTeamSize, Util.getLocaleByLanguageTag(winningBidder.getAppLanguage())));
            int moneyByBuyerAwards = moneyByBuyerAwardsPair.getKey();
            List<HmAwardDescriptionDO> awardDescriptionsByBuyer = moneyByBuyerAwardsPair.getValue();
            if (!awardDescriptionsByBuyer.isEmpty()) {
                awardsByUserId.put(winningBidder.getId(), awardDescriptionsByBuyer);
            }

            // remove money from buyer's league based balance
            if (!leagueService.addMoneyToUserBalance(league.getId(), winningBidder.getId(), (-1 * winningBid.getBid()) + moneyByBuyerAwards)) {
                throw new InvalidOperationException(operation, userId, "Could not remove balance " + winningBid.getBid() + " from user " + winningBidder.getId() + " in league " + league.getId());
            }
        }

        // delete transaction from transfer market
        if (deleteTransferMarket) {
            transferMarketRepository.deleteById(transferMarketInfo.id());
            transferMarketBidRepository.deleteAllByOfferId(transferMarketInfo.id());

            if (!isStart7Seller) {
                schedulingHandler.removeSystemUserSchedules(transferMarketInfo.id());
            }
        }

        // build push notifications
        List<Pair<UserDto, TransferMarketNotificationDo>> notifications = new ArrayList<>();
        String playerPicture = player.getHblImageId();
        String playerName = player.getFirstName() + " " + player.getLastName();

        // send out winning message only if winner is real user
        if (!isStart7Buyer) {
            notifications.add(
                    Pair.of(userProfileService.getUserDto(winningBidder.getId().toString()),
                            new TransferMarketNotificationDo(
                                    transferMarketInfo.id().toString(),
                                    playerPicture,
                                    winningBid.getId().toString(),
                                    league.getId().toString(),
                                    player.getId().toString(),
                                    winningBid.getBid(),
                                    ACCEPT_BID)));
        }

        rejectedBids.stream().map(rejectedBid ->
                {
                    try {
                        return Pair.of(userProfileService.getUserDto(transferMarketBidRepository.findBidderIdById(rejectedBid.getId()).toString()),
                                new TransferMarketNotificationDo(
                                        transferMarketInfo.id().toString(),
                                        playerPicture,
                                        rejectedBid.getId().toString(),
                                        league.getId().toString(),
                                        player.getId().toString(),
                                        rejectedBid.getBid(),
                                        REJECT_BID));
                    } catch (Exception e) {
                        return null;
                    }
                }).filter(Objects::nonNull)
                .forEach(notifications::add);


        return new AcceptBidNotificationDo(playerName, owner.getUsername(), league.getName(), notifications, awardsByUserId, systemUserId);
    }

    private void checkUserRoundScore(@NotNull UUID userId, @NotNull UUID leagueId, @NotNull LocalDateTime now) throws EntityNotExistException {
        HmRound currentRound = seasonService.getCurrentRound();
        if (now.isAfter(currentRound.getClosing()) && now.isBefore(currentRound.getTo())) {
            if (!userRoundScoreRepository.existsByUserIdAndLeagueIdAndRoundId(userId, leagueId, currentRound.getId())) {
                HmLeague league = leagueService.getByIdInNewTransaction(leagueId);
                boolean isLeagueCreatedBeforeClosing = league.getCreatedAt().isBefore(currentRound.getClosing());
                // leagues that were created after closing are not evaluated. That's why the start balance will not be saved
                if (isLeagueCreatedBeforeClosing) {
                    int balance = leagueService.getBalance(leagueId, userId);
                    HmUserProfile user = userProfileService.getByIdInNewTransaction(userId);
                    userRoundScoreRepository.save(new HmUserRoundScore(user, league, currentRound, balance));
                }
            }
        }
    }

    public void sendAcceptBidNotifications(AcceptBidNotificationDo acceptBidNotificationDo) {

        // send notification to the bidder, if the bidder is not the system
        String playerName = acceptBidNotificationDo.getPlayerName();
        String ownerUsername = acceptBidNotificationDo.getOwnerUserName();
        String leagueName = acceptBidNotificationDo.getLeagueName();
        UUID systemUserId = acceptBidNotificationDo.getSystemUserId();

        acceptBidNotificationDo.getNotificationByBidder()
                .stream()
                .filter(n -> !Objects.equals(n.getLeft().getId(), systemUserId.toString()))
                .forEach(pair -> {
                    UserDto receiver = pair.getLeft();
                    TransferMarketNotificationDo notification = pair.getRight();
                    Map<NotificationBodyKeyword, String> keywords = userNotificationHandler.getNotificationBodyKeywords(receiver.getUsername(), ownerUsername, String.valueOf(notification.getAmount()), leagueName, playerName);
                    Locale locale = Util.getLocaleByLanguageTagOrDefault(receiver.getAppLanguage());
                    messagingService.sendNotification(
                            userNotificationHandler.getNotificationTitle(notification.getNotificationEvent(), locale),
                            userNotificationHandler.getNotificationBody(notification.getNotificationEvent(), keywords, locale),
                            receiver.getId(),
                            notification);

                });
        acceptBidNotificationDo.getAwardsByUserId().entrySet()
                .stream()
                .filter(entry -> !Objects.equals(entry.getKey(), systemUserId))
                .forEach(entry -> {
                    UUID userId = entry.getKey();
                    entry.getValue().forEach(hmAwardDescriptionDO -> {
                        Map<NotificationBodyKeyword, String> keywords = userNotificationHandler.getNotificationBodyKeywords(leagueName, hmAwardDescriptionDO.getDescription());
                        Locale locale = hmAwardDescriptionDO.getLocale();
                        UUID awardPicture = hmAwardDescriptionDO.getAwardPicture();
                        messagingService.sendNotification(
                                userNotificationHandler.getNotificationTitle(USER_LEAGUE_AWARD, locale),
                                userNotificationHandler.getNotificationBody(USER_LEAGUE_AWARD, keywords, locale),
                                userId.toString(),
                                new AwardNotificationDo(USER_LEAGUE_AWARD, nonNull(awardPicture)? awardPicture.toString() : null));
                    });
                });
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public void removeTransferItemBid(String userId, String id) throws FormatException, InvalidOperationException, EntityNotExistException {
        if (!transferMarketBidRepository.existsById(Util.convertId(id))) {
            // in case bid got cleaned up another way
            return;
        }
        TransferMarketBidInfoDo bidInfo = transferMarketInfoHandler.getBidInfoById(Util.convertId(id));
        if (bidInfo == null) {
            return;
        }
        // if the user is already deleted, ignore the check and just delete
        if (!userProfileService.exists(bidInfo.bidderId())) {
            validateUserIsBidOwner("Remove player bid id[" + id + "]", userId, bidInfo.bidderId().toString());
        }
        try {
            transferMarketBidRepository.deleteById(Util.convertId(id));
        } catch (EmptyResultDataAccessException e) {
            String msg = "Object which existence was checked at beginning of transaction is now gone. Please investigate.";
            log.warn(msg, e);
            logMessageService.logWarning("TransferMarketBidHandler::RemoveTransferItemBid", msg, e);
        }
    }

    public TransferMarketBidDto rejectTransferItemBid(String userId, String id) throws EntityNotExistException, FormatException, InvalidOperationException {
        if (!transferMarketBidRepository.existsById(Util.convertId(id))) {
            // in case bid got cleaned up another way
            throw new EntityNotExistException(HmTransferMarketBid.class, id);
        }
        // transfer market bid
        HmTransferMarketBid transferMarketBid = getById(id);
        TransferMarketBidInfoDo bidInfo = transferMarketInfoHandler.getBidInfoById(Util.convertId(id));
        TransferMarketInfoDo transferMarketInfo = transferMarketInfoHandler.getTransferMarketInfoByBidId(Util.convertId(id));

        // transfer market item
        UUID offerId = bidInfo.offerId();
        // bidderDto
        UserDto bidderDto = userProfileService.getUserDto(bidInfo.bidderId().toString());
        //owner
        HmUserProfile owner = userProfileService.getByIdInNewTransaction(transferMarketInfo.ownerId());
        //league
        HmLeague league = leagueService.getByIdInNewTransaction(transferMarketInfo.leagueId());
        // player
        HmPlayer player = playerService.getByIdInNewTransaction(transferMarketInfo.playerId());
        // bid value
        int bidValue = bidInfo.bid();

        // validate that the bidder is not the owner of the transfer item
        validateUserIsTransferItemOwner("Reject player bid id[" + id + "]", userId,
                owner.getId().toString(), "The user ist not authorized to reject a bid owned by another manager");

        // reject the bid
        transferMarketBid.setStatus(REJECTED);

        // send notification to the bidder, if the bidder is not the system
        if (!bidderDto.getId().equals(userProfileService.getSystemUserId().toString())) {
            String playerName = player.getFirstName() + " " + player.getLastName();
            Map<NotificationBodyKeyword, String> keywords = userNotificationHandler.getNotificationBodyKeywords(bidderDto.getUsername(), owner.getUsername(), Integer.toString(bidValue), league.getName(), playerName);
            Locale locale = Util.getLocaleByLanguageTagOrDefault(bidderDto.getAppLanguage());
            String notificationId = messagingService.sendNotification(userNotificationHandler.getNotificationTitle(REJECT_BID, locale),
                    userNotificationHandler.getNotificationBody(REJECT_BID, keywords, locale)
                    , bidderDto.getId(), new TransferMarketNotificationDo(offerId.toString(), player.getHblImageId(), id,
                            league.getId().toString(), REJECT_BID));
            log.info("Firebase message with id [" + notificationId + "] sent. Event: " + REJECT_BID);
        }

        return new TransferMarketBidDto(id, bidderDto, owner.getId().toString(), league.getId().toString()
                , player.getId().toString(), bidValue, REJECTED);
    }

    public void rejectBidBySystem(TransferMarketBidInfoDo bidInfo, UUID playerId, UUID leagueId) throws EntityNotExistException, FormatException {
        HmUserProfile bidder = userProfileService.getByIdInNewTransaction(bidInfo.bidderId());
        HmPlayer player = playerService.getByIdInNewTransaction(playerId);
        HmLeague league = leagueService.getByIdInNewTransaction(leagueId);

        HmTransferMarketBid transferMarketBid = getById(bidInfo.id());
        transferMarketBid.setStatus(REJECTED);
        String playerName = player.getFirstName() + " " + player.getLastName();
        String playerPicture = player.getHblImageId();

        Locale locale = Util.getLocaleByLanguageTagOrDefault(bidder.getAppLanguage());
        String notificationTitle = userNotificationHandler.getNotificationTitle(REJECT_BID, locale);
        Map<NotificationBodyKeyword, String> keywords = userNotificationHandler.getNotificationBodyKeywords(bidder.getUsername(), SYSTEM_USERNAME, Integer.toString(transferMarketBid.getBid()), league.getName(), playerName);
        String notificationBody = userNotificationHandler.getNotificationBody(REJECT_BID, keywords, locale);
        TransferMarketNotificationDo notificationDo = new TransferMarketNotificationDo(SYSTEM_USERNAME, playerPicture, bidInfo.id().toString(), league.getId().toString(), REJECT_BID);
        String notificationId = messagingService.sendNotification(notificationTitle, notificationBody, bidder.getId().toString(), notificationDo);

        log.info("Firebase message with id [" + notificationId + "] sent. Event: " + REJECT_BID);
        transferMarketBidRepository.deleteAndRejectById(transferMarketBid.getId());
    }

    private int getLowestPlayerBid(String userId) throws FormatException, InvalidOperationException {
        return this.parameterService.getAsInteger(ParameterDefaults.PARAM_LOWEST_BID_VALUE, userId);
    }
}
