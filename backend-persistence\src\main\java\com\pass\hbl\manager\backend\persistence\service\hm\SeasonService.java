package com.pass.hbl.manager.backend.persistence.service.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.*;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmMatch;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmRound;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmSeason;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.exception.InvalidOperationException;
import com.pass.hbl.manager.backend.persistence.mapper.hm.HmRoundMapper;
import com.pass.hbl.manager.backend.persistence.mapper.hm.HmSeasonMapper;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmRoundRepository;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmSeasonRepository;
import com.pass.hbl.manager.backend.persistence.service.AbstractService;
import com.pass.hbl.manager.backend.persistence.service.admin.LogMessageService;
import com.pass.hbl.manager.backend.persistence.service.admin.ParameterService;
import com.pass.hbl.manager.backend.persistence.service.hm.handlers.RoundSetupHandler;
import com.pass.hbl.manager.backend.persistence.service.shared.TransactionHandler;
import com.pass.hbl.manager.backend.persistence.util.Constants;
import com.pass.hbl.manager.backend.persistence.util.ParameterDefaults;
import com.pass.hbl.manager.backend.persistence.util.Util;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.EventListener;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.LockModeType;
import javax.validation.constraints.NotNull;
import java.time.*;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.IntStream;

import static com.pass.hbl.manager.backend.persistence.dto.hm.NotificationEvent.ROUND_END;
import static com.pass.hbl.manager.backend.persistence.dto.hm.NotificationTopic.*;
import static com.pass.hbl.manager.backend.persistence.service.hm.helpers.SeasonHelper.*;
import static com.pass.hbl.manager.backend.persistence.util.Constants.*;
import static com.pass.hbl.manager.backend.persistence.util.ParameterDefaults.*;
import static com.pass.hbl.manager.backend.persistence.util.Util.toLocalDateTime;
import static java.util.Collections.emptyList;
import static java.util.Objects.isNull;
import static org.springframework.transaction.annotation.Propagation.REQUIRES_NEW;


@Slf4j
@Service
@Transactional
public class SeasonService extends AbstractService<HmSeason, SeasonDto> {

    private final HmSeasonMapper mapper;
    private final HmSeasonRepository repository;
    private final HmRoundRepository roundRepository;

    private final HmRoundMapper roundMapper;

    private final HmSchedulerService schedulerService;
    private final LeagueService leagueService;
    private final MatchService matchService;
    private final MessagingService messagingService;

    private final RoundSetupHandler roundSetupHandler;
    private final TransactionHandler transactionHandler;

    private final ParameterService parameterService;

    private final LogMessageService logMessageService;

    @Getter
    private LocalDateTime startDateSummerBreak = DEFAULT_START_DATE_SUMMER_BREAK;

    private LocalDateTime endDateSummerBreak = DEFAULT_END_DATE_SUMMER_BREAK;

    private HmSeason currentSeason;

    private HmSeason previousSeason;

    public SeasonService(HmSeasonMapper mapper,
                         HmSeasonRepository repository,
                         HmRoundRepository roundRepository,
                         HmRoundMapper roundMapper,
                         HmSchedulerService schedulerService,
                         @Lazy LeagueService leagueService,
                         MatchService matchService,
                         MessagingService messagingService,
                         RoundSetupHandler roundSetupHandler,
                         TransactionHandler transactionHandler,
                         ParameterService parameterService,
                         LogMessageService logMessageService) {
        super(repository, mapper, HmSeason.class);
        this.mapper = mapper;
        this.repository = repository;
        this.roundRepository = roundRepository;
        this.roundMapper = roundMapper;
        this.schedulerService = schedulerService;
        this.leagueService = leagueService;
        this.matchService = matchService;
        this.messagingService = messagingService;
        this.roundSetupHandler = roundSetupHandler;
        this.transactionHandler = transactionHandler;
        this.parameterService = parameterService;
        this.logMessageService = logMessageService;
    }

    @EventListener(ApplicationReadyEvent.class)
    @Transactional(readOnly = true)
    public void init() throws InvalidOperationException, FormatException, EntityNotExistException {
        startDateSummerBreak = parameterService.getAsDateTime(PARAM_START_DATE_SUMMER_BREAK, DEFAULT_START_DATE_SUMMER_BREAK, SYSTEM_USERNAME);
        endDateSummerBreak = parameterService.getAsDateTime(PARAM_END_DATE_SUMMER_BREAK, DEFAULT_END_DATE_SUMMER_BREAK, SYSTEM_USERNAME);
    }

    @Transactional(propagation = REQUIRES_NEW, readOnly = true)
    public void initializeCurrentSeason() throws EntityNotExistException {
        currentSeason = repository.findCurrentSeason().orElseThrow(() -> new EntityNotExistException(HmSeason.class, "<current>"));
        log.info("current season initialized");
        Optional<HmSeason> previousSeasonOptional = repository.findPreviousSeason();
        if (previousSeasonOptional.isPresent()) {
            previousSeason = previousSeasonOptional.get();
            log.info("previous season initialized");
        } else {
            log.info("previous season not found");
        }
    }

    public void checkAndRolloutSeason() throws EntityNotExistException {
        //Optional<HmSeason> currentSeasonOptional = transactionHandler.runInNewTransactionReadOnly(repository::findCurrentSeason);
        //currentSeasonOptional.ifPresent(this::checkAndRolloutSeason);
        this.checkAndRolloutSeason(getCurrentSeason());
        try {
            HmRound currentRound = transactionHandler.runInNewTransactionReadOnly(() -> {
                try {
                    return getCurrentRound();
                } catch (Exception e) {
                    return null;
                }
            });
            if (isNull(currentRound)) {
                throw new EntityNotExistException(HmRound.class, "<current>");
            }
            //TODO hbs initRoundResultSchedule temporary removed to avoid unwanted job starts that were replaced by manual starts
            //schedulerService.initRoundResultSchedule(currentRound);
        } catch (EntityNotExistException e) {
            log.info("Current round could not be retrieved. Skipping..");
        }
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true)
    public SeasonDto getCurrentSeasonAsDto() throws EntityNotExistException {
        return mapper.mapToDto(getCurrentSeason());
    }

    public List<RoundDto> getRoundsAsDto(@NotNull String seasonId) throws FormatException {
        return Util.toStream(roundRepository.findBySeasonId(Util.convertId(seasonId))).map(roundMapper::mapToDto).toList();
    }

    public List<HmRound> getRounds(@NotNull String seasonId) throws FormatException {
        return Util.toStream(roundRepository.findBySeasonId(Util.convertId(seasonId))).toList();
    }

    @Transactional(propagation = REQUIRES_NEW, readOnly = true)
    public RoundDto getRoundAsDto(@NotNull String roundId) throws FormatException, EntityNotExistException {
        return roundMapper.mapToDto(getRound(roundId));
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true, propagation = REQUIRES_NEW)
    public HmRound getRound(@NotNull String roundId) throws FormatException, EntityNotExistException {
        return roundRepository.findById(Util.convertId(roundId)).orElseThrow(() -> new EntityNotExistException(HmRound.class, roundId));
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public HmRound getCurrentRound() throws EntityNotExistException {
        return roundRepository.findCurrentRound(getCurrentSeason().getId()).orElseThrow(() -> new EntityNotExistException(HmRound.class, "<current>"));
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public HmRound getLatestRound() throws EntityNotExistException {
        return roundRepository.findLatestRound(getCurrentSeason().getId()).orElseThrow(() -> new EntityNotExistException(HmRound.class, "<current>"));
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public Optional<HmRound> getLatestClosedRound() throws EntityNotExistException {
        //return roundRepository.findLatestClosedRound(getCurrentSeason().getId()).orElseThrow(() -> new EntityNotExistException(HmRound.class, "<current>"));
        return roundRepository.findLatestClosedRound(getCurrentSeason().getId());
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public RoundDto getLatestEndedRoundAsDto() throws EntityNotExistException {
        HmRound latestEndedRound = roundRepository.findLatestEndedRound(getCurrentSeason().getId()).orElse(null);
        return isNull(latestEndedRound)? null: roundMapper.mapToDto(latestEndedRound);
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public HmRound getRoundAt(LocalDateTime timestamp) throws EntityNotExistException {
        return roundRepository.findRoundAt(timestamp).orElseThrow(() -> new EntityNotExistException(HmRound.class, "<at " + timestamp + ">"));
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public boolean isCurrentRound(String roundId) throws EntityNotExistException {
        return getCurrentRound().getId().toString().equals(roundId);
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true)
    public RoundDto getCurrentRoundAsDto() throws EntityNotExistException {
        return roundMapper.mapToDto(getCurrentRound());
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true, propagation = REQUIRES_NEW)
    public Optional<HmRound> getPreviousRound(String roundId) throws EntityNotExistException, FormatException {
        List<HmRound> sortedRounds = getSortedRoundsCurrentSeason();
        int index = sortedRounds.indexOf(getRound(roundId));
        return index > 0 ? Optional.of(sortedRounds.get(index - 1)) : Optional.empty();
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true, propagation = REQUIRES_NEW)
    public List<HmRound> getLatestRoundsSortedByFromDate(int count) throws EntityNotExistException, FormatException {
        List<HmRound> sortedRounds = getSortedRoundsCurrentSeason();
        int index = sortedRounds.indexOf(getCurrentRound());
        return index >= 2 ? new ArrayList<>(sortedRounds.subList(index - 2, index + 1)) : emptyList();
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true, propagation = REQUIRES_NEW)
    public Optional<HmRound> getPreviousRound(UUID currentRoundId) throws EntityNotExistException, FormatException {
        if (isNull(currentRoundId)) return Optional.empty();
        List<HmRound> sortedRounds = getSortedRoundsCurrentSeason();
        int index = IntStream.range(0, sortedRounds.size())
                .filter(i -> sortedRounds.get(i).getId().equals(currentRoundId))
                .findFirst()
                .orElse(-1); // Return -1 if the element is not found
        return index > 0 ? Optional.of(sortedRounds.get(index - 1)) : Optional.empty();
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true, propagation = REQUIRES_NEW)
    public HmSeason getCurrentSeason() throws EntityNotExistException {
        if (Objects.nonNull(currentSeason)) {
            return currentSeason;
        } else {
            log.info("Cached current season is null. will be retrieved from the database");
            return repository.findCurrentSeason().orElseThrow(() -> new EntityNotExistException(HmSeason.class, "<current>"));
        }
    }

    public String getSeasonLabel(HmSeason currentSeason) {
        return currentSeason.getStartDate().getYear() + "/" + (currentSeason.getEndDate().getYear() % 100);
    }

    @Transactional(readOnly = true)
    public String getSeasonLabel(String roundId) throws EntityNotExistException, FormatException {
        return getSeasonLabel(getRound(roundId).getSeason());
    }

    @Transactional(propagation = REQUIRES_NEW, readOnly = true)
    public List<HmRound> getSortedRoundsCurrentSeason() throws FormatException, EntityNotExistException {
        return getSortedRounds(getCurrentSeason());
    }

    @Transactional(propagation = REQUIRES_NEW, readOnly = true)
    public List<RoundDto> getSortedRoundsCurrentSeasonAsDtos() throws FormatException, EntityNotExistException {
        return getSortedRoundsCurrentSeason().stream().map(roundMapper::mapToDto).toList();
    }

    @Transactional(propagation = REQUIRES_NEW, readOnly = true)
    public int getRoundOrder(HmRound round) throws EntityNotExistException, FormatException {
        List<HmRound> sortedRounds = getSortedRoundsCurrentSeason();
        return sortedRounds.stream().map(HmRound::getId).toList().indexOf(round.getId()) + 1;
    }

    @Transactional(propagation = REQUIRES_NEW, readOnly = true)
    public Optional<HmSeason> getPreviousSeason() {
        if (Objects.nonNull(previousSeason)) {
            return Optional.of(previousSeason);
        } else {
            log.info("Cached previous season is null. will be retrieved from the database");
            return repository.findCurrentSeason();
        }
    }

    @Transactional(propagation = REQUIRES_NEW, readOnly = true)
    public List<HmSeason> getCurrentAndPreviousSeason() throws EntityNotExistException {
        HmSeason currentSeason = getCurrentSeason();
        List<HmSeason> seasons = new ArrayList<>();
        seasons.add(currentSeason);
        Optional<HmSeason> previousSeasonOptional = getPreviousSeason();
        previousSeasonOptional.ifPresent(seasons::add);
        return seasons;
    }

    public List<HmRound> getSortedRounds(HmSeason season) throws FormatException {
        List<HmRound> rounds = new ArrayList<>(getRounds(season.getId().toString()));
        rounds.sort(Comparator.comparing(HmRound::getFrom));
        return rounds;
    }

    public void connectSeasons() {
        List<HmSeason> seasons = Util.toStream(repository.findAll()).sorted(Comparator.comparing(HmSeason::getStartDate)).toList();
        for (int i = 0; i < seasons.size(); i++) {
            HmSeason season = seasons.get(i);
            HmSeason previousSeason = i == 0 ? null : seasons.get(i - 1);

            season.setEndDate(season.getEndDate().plusWeeks(2));
            if (previousSeason == null) {
                season.setStartDate(season.getStartDate().minusWeeks(4));
            } else {
                season.setStartDate(previousSeason.getEndDate().plusDays(1));
            }
        }
    }

    public void checkAndRolloutSeason(HmSeason season) {
        //season = initializeRounds(season);
        //if (season.getRounds().isEmpty())
        if (roundRepository.countRoundsBySeason(season.getId()) == 0) {
            saveRounds(roundSetupHandler.setupInitialRounds(season, LocalDate.now()), season, "Rolled out season");
        }
    }

    // Simulates the setup of rounds to the matches in the next season weeks
    public List<RoundDto> setupSeasonRounds(String seasonId, String currentDateStr) throws FormatException, EntityNotExistException {
        List<RoundDto> resultRounds = new ArrayList<>();
        HmSeason season = getById(seasonId);
        LocalDateTime currentDate = LocalDateTime.of(toLocalDateTime(currentDateStr).toLocalDate().with(DayOfWeek.MONDAY),
                LocalTime.MIN);
        // setup next week rounds every monday
        while (currentDate.toLocalDate().isBefore(season.getEndDate())) {
            resultRounds.addAll(setupNextWeekRounds(season, currentDate));
            currentDate = currentDate.plusDays(7);
        }
        return resultRounds;
    }

    // Simulates the setup of rounds to the matches occurring next week
    public List<RoundDto> getNextWeekRounds(String seasonId, String currentDateStr) throws EntityNotExistException, FormatException {
        HmSeason season = getById(seasonId);
        LocalDateTime currentDate = toLocalDateTime(currentDateStr);
        return setupNextWeekRounds(season, currentDate);
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public List<RoundDto> setupNextWeekRounds(HmSeason season, LocalDateTime currentDate) throws EntityNotExistException, FormatException {
        //season = initializeRounds(season);
        if (roundRepository.countRoundsBySeason(season.getId()) == 0) {
            List<RoundDto> firstWeekRounds = saveRounds(roundSetupHandler.setupInitialRounds(season, currentDate.toLocalDate()), season, "Rolled out season");
            Optional<RoundDto> firstRoundOpt = firstWeekRounds.stream().findFirst();
            if (firstRoundOpt.isPresent()) {
                schedulerService.scheduleRoundResultJob(firstRoundOpt.get().getId());
                schedulerService.scheduleRoundResultUpdateJob(firstRoundOpt.get().getId());
            }
            return firstWeekRounds;
        } else {
            Optional<HmRound> currentRound = roundRepository.findLastRoundAfterDate(season.getId(), currentDate);
            if (currentRound.isPresent()) {
                LocalDateTime currentRoundClosing = currentRound.get().getClosing();
                // check if the current date in the closing week of the latest round [Monday - Thursday]
                if (roundSetupHandler.isDateInRoundClosingWeek(currentDate, currentRoundClosing)) {
                    return saveRounds(roundSetupHandler.setupNextWeekRounds(season, currentDate.toLocalDate(), currentRound.get().getClosing().plusMinutes(10)), season, "Filled up season");
                } else {
                    log.info("Setup of next round of season [" + season.getName() + "] is disabled at current date: " + currentDate
                            + ". Last existing round order: " + currentRound.get().getRoundNumber() + ". Earliest setup date for next round is: " + currentRoundClosing + ". Skipping");
                }
            } else {
                log.info("Current round in season [" + season.getId() + "] not available. Next round will start from current date " +
                        currentDate + ". Rounds setup starting");
                return saveRounds(roundSetupHandler.setupNextWeekRounds(season, currentDate.toLocalDate(), currentDate), season, "Filled up season");
            }
        }
        return emptyList();
    }

    private HmSeason initializeRounds(HmSeason season) {
        season.setRounds(season.getRounds() == null ? new ArrayList<>() : season.getRounds());
        return save(season);
    }

    private List<RoundDto> saveRounds(Map<HmRound, List<HmMatch>> matchesByRound, HmSeason season, String template) {
        List<RoundDto> resultRounds = new ArrayList<>();
        if (!matchesByRound.isEmpty()) {
            matchesByRound.forEach((key, value) -> {
                HmRound round = roundRepository.save(key);
                resultRounds.add(roundMapper.mapToDto(round));
                value.forEach(match -> {
                    match.setRound(round);
                    matchService.saveMatch(match);
                });
            });
            log.info(template + " " + season.getName());
            resultRounds.sort(Comparator.comparing(RoundDto::getFrom));
            return resultRounds;
        } else {
            return emptyList();
        }
    }

    public void handleRoundResults(String roundId, boolean isAutoRerun) throws InvalidOperationException, FormatException, EntityNotExistException {
        HmRound round = getReadOnlyRound(roundId);
        // recalculate next round
        try {
            if (isNextRoundPresent()) {
                HmRound nextRound = getNextRoundOptReadOnly();
                if (Objects.equals(nextRound.getId(), UUID.fromString(roundId))) {
                    log.info("Disable rescheduling RoundResultJob for next round. Reason: next round id = round id = " + roundId);
                } else if (schedulerService.existsByRoundResultJobNameAndRoundId(ROUND_RESULT_JOB, nextRound.getId(), false)) {
                    log.info("Disable rescheduling RoundResultJob for next round. Reason: next round id =" + roundId + " is already scheduled.");
                } else {
                    log.info("Rescheduling RoundResultJob for next round id[" + nextRound.getId() + "]");
                    schedulerService.scheduleRoundResultJob(nextRound.getId(), nextRound.getTo(), false);
                }
            }
        } catch (Exception e) {
            log.error("Rescheduling RoundResultJob failed. Skipping..", e);
        }
        log.info("Calculating results for round id[" + roundId + "]");
        leagueService.updateManagerScores(round, true);

        // sending round result notifications is disabled fo automatic RoundResultJob reruns
        if (isAutoRerun) {
            log.info("Sending round result notifications for round id[" + roundId + "] is disabled fo automatic RoundResultJob reruns");
            return;
        }

        // The premium users and users with English App-language should receive the round end notification at first
        List<Pair<Locale, NotificationTopic>> topicByLocale = Arrays.asList(Pair.of(DEFAULT_LOCALE, ROUND_END_TOPIC_PREMIUM), Pair.of(EN_LOCALE, ROUND_END_TOPIC_EN));

        if (parameterService.getAsBoolean(ParameterDefaults.PARAM_SEND_ROUND_RESULT_MESSAGE, ParameterDefaults.DEFAULT_SEND_ROUND_RESULT_MESSAGE, Constants.SYSTEM_USERNAME)) {
            topicByLocale.forEach(topicLocalePair -> {
                try {
                    log.info("Firebase message with id [" + messagingService.sendTopicNotification(getNotificationTitle(ROUND_END, topicLocalePair.getKey()),
                            getNotificationBody(ROUND_END, topicLocalePair.getKey()), topicLocalePair.getValue(), topicLocalePair.getKey().getLanguage(), true) + "] sent. Event: " + ROUND_END);
                } catch (Exception e) {
                    log.error("Error sending firebase message.", e);
                    logMessageService.logException("handleRoundResults: Error sending firebase message.", e);
                    log.error("Please send firebase message manually:\n" +
                            "Title: " + getNotificationTitle(ROUND_END, topicLocalePair.getKey()) + "\n" +
                            "Data: " + getNotificationBody(ROUND_END, topicLocalePair.getKey()));
                }
            });
            // Schedule delay round result notification as follows:
            // - Premium users and users with "english" App language receive notifications first
            // - Basic users receive notifications subsequently with a delay of 30 minutes
            List<Pair<Locale, NotificationTopic>> delayedTopicByLocale = Arrays.asList(
                    Pair.of(DEFAULT_LOCALE, ROUND_END_TOPIC_0), Pair.of(DEFAULT_LOCALE, ROUND_END_TOPIC_1)
                    , Pair.of(DEFAULT_LOCALE, ROUND_END_TOPIC_2), Pair.of(DEFAULT_LOCALE, ROUND_END_TOPIC_3)
                    , Pair.of(DEFAULT_LOCALE, ROUND_END_TOPIC_4), Pair.of(DEFAULT_LOCALE, ROUND_END_TOPIC_5)
                    , Pair.of(DEFAULT_LOCALE, ROUND_END_TOPIC_6), Pair.of(DEFAULT_LOCALE, ROUND_END_TOPIC_7)
                    , Pair.of(DEFAULT_LOCALE, ROUND_END_TOPIC_8), Pair.of(DEFAULT_LOCALE, ROUND_END_TOPIC_9));
            scheduleDelayedNotifications(round.getTo(), delayedTopicByLocale);
        } else {
            topicByLocale.forEach(topicLocalePair ->
                    {
                        messagingService.saveTopicNotification(getNotificationTitle(ROUND_END, topicLocalePair.getKey()),
                                getNotificationBody(ROUND_END, topicLocalePair.getKey()), topicLocalePair.getKey().getLanguage());
                        log.info("Please send firebase message manually:\n" +
                                "Title: " + getNotificationTitle(ROUND_END, topicLocalePair.getKey()) + "\n" +
                                "Data: " + getNotificationBody(ROUND_END, topicLocalePair.getKey()));
                    }
            );
        }
    }

    /**
     * Method to send round result notification for a given topic.
     *
     * @param topic topic to which the round result notification will be sent
     * @throws InvalidOperationException thrown, if an invalid operation is performed
     * @throws FormatException thrown, if the given formatting doesn't match the expected one
     */
    public void sendRoundResultNotification(String topic) throws InvalidOperationException, FormatException {
        // Check if the parameter to send round result messages is enabled.
        if (parameterService.getAsBoolean(ParameterDefaults.PARAM_SEND_ROUND_RESULT_MESSAGE, ParameterDefaults.DEFAULT_SEND_ROUND_RESULT_MESSAGE, Constants.SYSTEM_USERNAME)) {
            try {
                // Log and send the Firebase notification.
                log.info("Firebase message with id [" + messagingService.sendTopicNotification(getNotificationTitle(ROUND_END, DEFAULT_LOCALE),
                        getNotificationBody(ROUND_END, DEFAULT_LOCALE), NotificationTopic.valueOf(topic), DEFAULT_LOCALE.getLanguage(), false) + "] sent. Event: " + ROUND_END);
            } catch (Exception e) {
                log.error("Error sending firebase message.", e);
                logMessageService.logException("sendRoundResultNotification: Error sending firebase message.", e);
                log.error("Please send firebase message manually:\n" +
                        "Title: " + getNotificationTitle(ROUND_END, DEFAULT_LOCALE) + "\n" +
                        "Data: " + getNotificationBody(ROUND_END, DEFAULT_LOCALE));
            }
        } else {
            log.info("sendRoundResultNotification: sending round result notification is disabled. Skipping..");
        }
    }

    private void scheduleDelayedNotifications(LocalDateTime roundEnd, List<Pair<Locale, NotificationTopic>> topics) {
        // AtomicInteger is used to count topics and create incremental delays.
        AtomicInteger counter = new AtomicInteger(1);
        topics.forEach(topicPair -> {
            NotificationTopic topic = topicPair.getValue();
            // Calculate the delay for this notification.
            int delay = ROUND_RESULT_NOTIFICATION_DELAY * counter.getAndIncrement();
            // Schedule the notification job with the current topic and delayed notification time.
            schedulerService.scheduleRoundResultNotificationJob(topic, roundEnd.plusMinutes(delay));
        });
    }

    public void updateRoundResults(String roundId) throws EntityNotExistException {
        HmRound round = getReadOnlyRound(roundId);
        // recalculate next round
        try {
            if (isNextRoundPresent()) {
                HmRound nextRound = getNextRoundOptReadOnly();
                if (Objects.equals(nextRound.getId(), UUID.fromString(roundId))) {
                    log.info("Disable rescheduling RoundResultUpdateJob for next round. Reason: next round id = round id = " + roundId);
                } else if (schedulerService.existsByRoundResultJobNameAndRoundId(ROUND_RESULT_UPDATE_JOB, nextRound.getId(), false)) {
                    log.info("Disable rescheduling RoundResultUpdateJob for next round. Reason: next round id =" + roundId + " is already scheduled.");
                } else {
                    log.info("Rescheduling RoundResultUpdateJob for next round id[" + nextRound.getId() + "]");
                    schedulerService.scheduleRoundResultUpdateJob(nextRound.getId(), nextRound.getTo());
                }
            }
        } catch (Exception e) {
            log.error("Rescheduling RoundResultUpdateJob failed. Skipping..", e);
        }
        log.info("Recalculating results for round id[" + roundId + "]");
        leagueService.correctManagerScores(round);
    }

    public HmRound getNextRoundOptReadOnly() {
        return transactionHandler.runInNewTransactionReadOnly(() -> {
            try {
                Optional<HmRound> nextRoundOpt = roundRepository.findNextRound(getCurrentSeason().getId());
                return nextRoundOpt.orElse(null);
            } catch (Exception e) {
                return null;
            }
        });
    }

    @Lock(LockModeType.READ)
    @Transactional(propagation = REQUIRES_NEW, readOnly = true)
    public Optional<HmRound> getNextRoundAfterDate(LocalDateTime referenceDate) throws EntityNotExistException {
        return roundRepository.findNextRoundAfterDate(getCurrentSeason().getId(), referenceDate);
    }

    public boolean isNextRoundPresent() {
        return transactionHandler.runInNewTransactionReadOnly(() -> {
            try {
                return roundRepository.findNextRound(getCurrentSeason().getId()).isPresent();
            } catch (Exception e) {
                return false;
            }
        });
    }

    private HmRound getReadOnlyRound(String roundId) {
        return transactionHandler.runInNewTransactionReadOnly(() -> {
            try {
                return getRound(roundId);
            } catch (Exception e) {
                return null;
            }
        });
    }

    public List<HmRound> getAlreadyPlayedRounds() throws EntityNotExistException, FormatException {
        //get already played rounds of the current season
        List<HmRound> resultRounds = emptyList();
        List<HmRound> roundsCurrentSeason = getSortedRoundsCurrentSeason();
        //get already played rounds of the current season
        HmRound currentRound = getCurrentRound();
        int currentRoundIndex = roundsCurrentSeason.indexOf(currentRound);
        if (currentRoundIndex > 0) {
            resultRounds = new ArrayList<>(roundsCurrentSeason.subList(0, currentRoundIndex).stream().toList());
            // include the last round in the statistics if now is after the last round end.
            // This is used in case the round is reached but the round end is extended for technical reasons
            if (isSummerBreak()) {
                resultRounds.add(currentRound);
            }
        }

        return resultRounds;
    }

    @Transactional(readOnly = true)
    @Lock(LockModeType.READ)
    public MatchResultDetailsDto getMatchResults() throws EntityNotExistException, FormatException, InvalidOperationException {
        HmRound currentRound = getCurrentRound();
        // use this one to hardly disable streaming on mobile
        // return new MatchResultDetailsDto(false, null, null, null, emptyList());

        LocalDateTime now = LocalDateTime.now();
        if (now.isBefore(currentRound.getClosing().minusDays(1))) {
            return new MatchResultDetailsDto(false, null, null, null, emptyList(), false);
        }
        ZonedDateTime nearestGameStart = matchService.getNearestGameStart(currentRound, Constants.SYSTEM_USERNAME);
        // in combination with special round, it specifies the round display name i.e. special round 1
        int roundOrder = currentRound.getRoundNumber();
        String seasonLabel = getSeasonLabel(currentRound.getId().toString());

        List<MatchDto> matchDtos = matchService.getAllByRoundAsDto(currentRound.getId());
        return new MatchResultDetailsDto(true, nearestGameStart, roundOrder, seasonLabel, matchDtos, currentRound.isSpecialRound());
    }

    //ONLY FOR INTERNAL TESTS
    public void sendRoundResultTestNotification() throws InvalidOperationException, FormatException {
        // send message to topic in the available locale
        List<Pair<Locale, NotificationTopic>> topicByLocale = Arrays.asList(Pair.of(DEFAULT_LOCALE, ROUND_END_TOPIC_PREMIUM), Pair.of(EN_LOCALE, ROUND_END_TOPIC_EN));
        if (parameterService.getAsBoolean(ParameterDefaults.PARAM_SEND_ROUND_RESULT_MESSAGE, ParameterDefaults.DEFAULT_SEND_ROUND_RESULT_MESSAGE, Constants.SYSTEM_USERNAME)) {
            topicByLocale.forEach(topicLocalePair -> {
                try {
                    log.info("Firebase message with id [" + messagingService.sendTopicNotification(getTestNotificationTitle(ROUND_END, topicLocalePair.getKey()),
                            getNotificationBody(ROUND_END, topicLocalePair.getKey()), topicLocalePair.getValue(), topicLocalePair.getKey().getLanguage(), true) + "] sent. Event: " + ROUND_END);
                } catch (Exception e) {
                    log.error("Error sending firebase message.", e);
                    logMessageService.logException("handleRoundResults: Error sending firebase message.", e);
                    log.error("Please send firebase message manually:\n" +
                            "Title: " + getTestNotificationTitle(ROUND_END, topicLocalePair.getKey()) + "\n" +
                            "Data: " + getNotificationBody(ROUND_END, topicLocalePair.getKey()));
                }
            });
        } else {
            topicByLocale.forEach(topicLocalePair -> log.info("Please send firebase message manually:\n" +
                    "Title: " + getTestNotificationTitle(ROUND_END, topicLocalePair.getKey()) + "\n" +
                    "Data: " + getNotificationBody(ROUND_END, topicLocalePair.getKey())));
        }
    }

    @Transactional(readOnly = true)
    public boolean isSummerBreak() {
        LocalDateTime now = LocalDateTime.now();
        try {
            return now.isAfter(startDateSummerBreak)
                    && now.isBefore(endDateSummerBreak);
        } catch (Exception e) {
            log.error("Check is summer break failed", e);
            logMessageService.logException("Check isSummerBreak", e);
            return false;
        }
    }

    /**
     * return the extended round end for displaying live results. Example 5 hours after round end (last match whistle)
     *
     * @param roundEnd given end date of round
     * @return extended end date of round
     */
    @Transactional(readOnly = true)
    public LocalDateTime getLiveResultsDisplayEndDate(LocalDateTime roundEnd) {
        return roundEnd.plusHours(LIVE_RESULTS_DISPLAY_HOURS);
    }

    @Transactional(readOnly = true)
    public boolean seasonContainsRounds(UUID seasonId) throws EntityNotExistException, FormatException {
        HmSeason season = getById(seasonId);
        return season.getRounds().size() > 0;
    }

    @Transactional(propagation = REQUIRES_NEW, readOnly = true)
    public HmRound getByRoundNumber(int roundNumber, Boolean specialRound) throws EntityNotExistException {
        HmSeason currentSeason = getCurrentSeason();
        Optional<HmRound> roundOptional;
        if (Objects.isNull(specialRound)) {
            roundOptional = roundRepository.findByRoundNumberAndSeasonId(roundNumber, currentSeason.getId()).stream().findFirst();
        } else {
            roundOptional = roundRepository.findByRoundNumberAndSeasonIdAndSpecialRound(roundNumber, currentSeason.getId(), specialRound).stream().findFirst();
        }
        return roundOptional.orElseThrow(() -> new EntityNotExistException(HmRound.class, "round number", Integer.toString(roundNumber)));
    }

    @Transactional(propagation = REQUIRES_NEW, readOnly = true)
    public HmRound getFirstRoundInCurrentSeason() throws EntityNotExistException {
        HmSeason currentSeason = getCurrentSeason();
        Optional<HmRound> roundOptional;
        List<HmRound> rounds = new ArrayList<>(roundRepository.findByRoundNumberAndSeasonId(1, currentSeason.getId()));
        rounds.sort(Comparator.comparing(HmRound::getFrom));
        roundOptional = rounds.stream().findFirst();
        return roundOptional.orElseThrow(() -> new EntityNotExistException(HmRound.class, "round number", Integer.toString(1)));
    }

    public int extendRoundEnd(String roundId, LocalDateTime newEndDate) {
        return roundRepository.extendRoundEnd(UUID.fromString(roundId), newEndDate);
    }
}
