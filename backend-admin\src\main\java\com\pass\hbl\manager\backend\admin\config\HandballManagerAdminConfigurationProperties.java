package com.pass.hbl.manager.backend.admin.config;

import com.pass.hbl.manager.backend.persistence.config.AbstractHandballManagerConfigurationProperties;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "handball-manager")
public class HandballManagerAdminConfigurationProperties extends AbstractHandballManagerConfigurationProperties {

    private Security security;

    @Getter
    @Setter
    public static class Security {

        /**
         * Username for admin login
         */
        private String adminUser;

        /**
         * Password for admin login
         */
        private String adminPassword;

        /**
         * Username for admin login with write access
         */
        private String adminWriteUser;

        /**
         * Password for admin login with write access
         */
        private String adminWritePassword;
    }

}
