package com.pass.hbl.manager.backend.persistence.service.hm;

import com.pass.hbl.manager.backend.persistence.domain.hm.*;
import com.pass.hbl.manager.backend.persistence.dto.hm.*;
import com.pass.hbl.manager.backend.persistence.dto.shared.EntityType;
import com.pass.hbl.manager.backend.persistence.dto.shared.ImageDomain;
import com.pass.hbl.manager.backend.persistence.entity.hm.*;
import com.pass.hbl.manager.backend.persistence.entity.shared.SharedLocalization;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.exception.InvalidOperationException;
import com.pass.hbl.manager.backend.persistence.mapper.hm.HmAwardMapper;
import com.pass.hbl.manager.backend.persistence.mapper.hm.HmUserAwardInfoMapper;
import com.pass.hbl.manager.backend.persistence.mapper.hm.HmUserAwardMapper;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmAwardRepository;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmUserAwardRepository;
import com.pass.hbl.manager.backend.persistence.service.AbstractService;
import com.pass.hbl.manager.backend.persistence.service.admin.LogMessageService;
import com.pass.hbl.manager.backend.persistence.service.hm.handlers.AwardAssignmentHandler;
import com.pass.hbl.manager.backend.persistence.service.hm.handlers.AwardCheckingHandler;
import com.pass.hbl.manager.backend.persistence.service.hm.handlers.LeagueScoreAwardsHandler;
import com.pass.hbl.manager.backend.persistence.service.hm.handlers.UserNotificationHandler;
import com.pass.hbl.manager.backend.persistence.service.hm.helpers.CacheHandler;
import com.pass.hbl.manager.backend.persistence.service.shared.ImageService;
import com.pass.hbl.manager.backend.persistence.service.shared.LocalizationService;
import com.pass.hbl.manager.backend.persistence.service.shared.TransactionHandler;
import com.pass.hbl.manager.backend.persistence.util.LocalizationConstants;
import com.pass.hbl.manager.backend.persistence.util.PageableResult;
import com.pass.hbl.manager.backend.persistence.util.Util;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.EventListener;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.persistence.LockModeType;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.pass.hbl.manager.backend.persistence.dto.hm.AwardCode.*;
import static com.pass.hbl.manager.backend.persistence.dto.hm.NotificationEvent.*;
import static com.pass.hbl.manager.backend.persistence.dto.hm.SessionAttribute.CURRENT_LEAGUE;
import static com.pass.hbl.manager.backend.persistence.util.Constants.*;
import static java.util.Collections.emptyList;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static java.util.Optional.empty;
import static org.apache.commons.lang3.StringUtils.isNotEmpty;

@Slf4j
@Service
@Transactional
public class AwardService extends AbstractService<HmAward, AwardDto> {
    private final HmAwardRepository hmAwardRepository;
    private final HmAwardMapper hmAwardMapper;
    private final HmUserAwardInfoMapper userAwardInfoMapper;

    private final HmUserAwardRepository hmUserAwardRepository;
    private final HmUserAwardMapper hmUserAwardMapper;

    private final ImageService imageService;
    private final LocalizationService localizationService;
    private final LeagueService leagueService;
    private final SeasonService seasonService;
    private final HmSchedulerService schedulerService;
    private final MessagingService messagingService;
    private final UserProfileService userProfileService;
    private final LogMessageService logMessageService;

    private final AwardAssignmentHandler awardAssignmentHandler;
    private final TransactionHandler transactionHandler;
    private final AwardCheckingHandler awardCheckingHandler;
    private final LeagueScoreAwardsHandler leagueScoreAwardsHandler;
    private final UserNotificationHandler userNotificationHandler;
    private final CacheManager cacheManager;
    private final CacheHandler cacheHandler;


    @Getter
    List<HmAward> allAwards = new ArrayList<>();

    @Getter
    List<AwardDto> allAwardDtos = new ArrayList<>();

    public AwardService(CacheManager cacheManager, HmAwardRepository hmAwardRepository,
                        HmAwardMapper hmAwardMapper,
                        HmUserAwardInfoMapper userAwardInfoMapper, ImageService imageService,
                        LocalizationService localizationService,
                        HmUserAwardRepository hmUserAwardRepository,
                        HmUserAwardMapper hmUserAwardMapper, LeagueService leagueService, @Lazy SeasonService seasonService, @Lazy HmSchedulerService schedulerService, @Lazy MessagingService messagingService, @Lazy UserProfileService userProfileService, @Lazy LogMessageService logMessageService, AwardAssignmentHandler awardAssignmentHandler, TransactionHandler transactionHandler, AwardCheckingHandler awardCheckingHandler, LeagueScoreAwardsHandler leagueScoreAwardsHandler, @Lazy UserNotificationHandler userNotificationHandler, CacheHandler cacheHandler) {
        super(hmAwardRepository, hmAwardMapper, HmAward.class);
        this.cacheManager = cacheManager;
        this.hmAwardRepository = hmAwardRepository;
        this.hmAwardMapper = hmAwardMapper;
        this.userAwardInfoMapper = userAwardInfoMapper;
        this.imageService = imageService;
        this.localizationService = localizationService;
        this.hmUserAwardRepository = hmUserAwardRepository;
        this.hmUserAwardMapper = hmUserAwardMapper;
        this.leagueService = leagueService;
        this.seasonService = seasonService;
        this.schedulerService = schedulerService;
        this.messagingService = messagingService;
        this.userProfileService = userProfileService;
        this.logMessageService = logMessageService;
        this.awardAssignmentHandler = awardAssignmentHandler;
        this.transactionHandler = transactionHandler;
        this.awardCheckingHandler = awardCheckingHandler;
        this.leagueScoreAwardsHandler = leagueScoreAwardsHandler;
        this.userNotificationHandler = userNotificationHandler;
        this.cacheHandler = cacheHandler;
    }

    @EventListener(ApplicationReadyEvent.class)
    @Transactional(readOnly = true)
    public void init() throws InvalidOperationException, FormatException {
        allAwards = Util.toStream(hmAwardRepository.findAll()).toList();
        log.info("AwardService: all hmAwards initialized. count = " + allAwards.size());
        allAwardDtos = getAllAsDto();
        log.info("AwardService: all awardDtos initialized. count = " + allAwards.size());
    }

    private Optional<HmAward> getAwardByCode(AwardCode code) {
        Optional<HmAward> awardOptional = allAwards.stream().filter(hmAward -> Objects.equals(hmAward.getCode().name(), code.name())).findFirst();
        // try to get the award from cache
        if (awardOptional.isPresent()) {
            return awardOptional;
        } else {
            // if not found, search it from the database
            log.info("AwardService: award with code = " + code + " not found in cache, it will be retrieved from the database.");
            return transactionHandler.runInNewTransactionReadOnly(() -> hmAwardRepository.findByCode(code).stream().findFirst());
        }
    }

    private Optional<HmAward> getAwardById(UUID id) {
        Optional<HmAward> awardOptional = allAwards.stream().filter(hmAward -> Objects.equals(hmAward.getId(), id)).findFirst();
        // try to get the award from cache
        if (awardOptional.isPresent()) {
            return awardOptional;
        } else {
            // if not found, search it from the database
            log.info("AwardService: award with id = " + id + " not found in cache, it will be retrieved from the database.");
            return transactionHandler.runInNewTransactionReadOnly(() -> hmAwardRepository.findById(id).stream().findFirst());
        }
    }

    public AwardDto createAward(@NotNull AwardDto awardRequestDto, MultipartFile file) throws IOException {
        int displayOrder = updateDisplayOrder(awardRequestDto);
        HmAward hmAward = hmAwardRepository.save(new HmAward(awardRequestDto.isStackable(), awardRequestDto.getCode(),
                displayOrder, awardRequestDto.getExperiencePoints(), awardRequestDto.getMoney(), savePicture(file)));
        hmAward.setDescriptions(localizationService.saveAll(convertAwardLocalizations(hmAward.getId(), awardRequestDto.getLocalizations())));
        return hmAwardMapper.mapToDto(hmAward);
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true)
    public PageableResult<AwardDto> getAllAsDto(Pageable pageable, String language) throws FormatException {
        // Define the cache key based on Pageable and language parameters
        String cacheKey = cacheHandler.buildCacheKey(AWARDS_CACHE, pageable.getPageNumber(), language);
        PageableResult<AwardDto> awardDtos = null;
        // Try to get the cached value first
        awardDtos = getAwardDtoPageableResultFromCache(cacheKey, awardDtos);
        // If not found in cache, fetch the data from the DB or service method
        if (isNull(awardDtos) || awardDtos.content().isEmpty()) {
            log.info("Award dtos list get from cache is empty for cacheKey [" + cacheKey + "], data should be fetched from the database");
            // Fetch the data from the DB or service method
            awardDtos = filterAwardsLanguage(getAllAsDto(pageable), language);
            // Try to put the result into the cache for future requests
            // Synchronize the cache update for this key to handle concurrent access
            synchronized (cacheHandler.getLockForKey(cacheKey)) {
                cacheHandler.addToCache(awardDtos, cacheKey, AWARDS_CACHE);
            }
        }
        return awardDtos;
    }

    private PageableResult<AwardDto> getAwardDtoPageableResultFromCache(String cacheKey, PageableResult<AwardDto> awardDtos) {
        try {
            Cache cache = cacheManager.getCache(AWARDS_CACHE);
            if (cache != null) {
                awardDtos = cache.get(cacheKey, PageableResult.class);
            }
        } catch (Exception e) {
            // Handle Redis connection issues and log a warning
            log.warn("getAwardDtoPageableResultFromCache: Redis cache access failed for cache " + AWARDS_CACHE + " , Falling back to database. Error: " + e.getMessage());
        }
        return awardDtos;
    }

    public AwardDto getByIdAsDto(@NotNull String id, String language) throws FormatException, EntityNotExistException {
        return filterAwardLanguage(hmAwardMapper.mapToDto(getById(id)), language);
    }

    public AwardDto update(@NotNull AwardDto dto, MultipartFile file) throws FormatException, EntityNotExistException, IOException {
        final String imageId;
        if (file == null) {
            if (isNotEmpty(dto.getPicture())) {
                imageService.delete(dto.getPicture());
            }
            imageId = null;
        } else {
            if (isNotEmpty(dto.getPicture())) {
                imageService.delete(dto.getPicture());
            }
            imageId = savePicture(file).toString();
        }
        dto.setPicture(imageId);
        HmAward hmAward = hmAwardMapper.mapToEntity(dto, getById(dto.getId()));
        return hmAwardMapper.mapToDto(hmAwardRepository.save(hmAward));
    }

    public void deleteAward(String awardId) throws FormatException {
        hmAwardRepository.deleteById(Util.convertId(awardId));
        hmUserAwardRepository.deleteByAward(Util.convertId(awardId));
    }

    public UserAwardDto assignUserAward(String userId, String awardId, String leagueId, String language) throws InvalidOperationException, EntityNotExistException, FormatException {
        Optional<HmAward> awardOptional = getAwardById(UUID.fromString(awardId));
        if (awardOptional.isPresent()) {
            HmUserAward hmUserAward = awardAssignmentHandler.assignUserAward(UUID.fromString(userId), UUID.fromString(leagueId), awardOptional.get());
            UserAwardDto userAwardDto = new UserAwardDto(hmUserAward.getId().toString(), hmUserAward.getNumberOfAchievements(), hmUserAward.getCurrentSeries(),
                    hmUserAward.getLongestSeries(), transactionHandler.runInNewTransactionReadOnly(() -> hmAwardMapper.mapToDto(awardOptional.get())),
                    userId, leagueId);
            //leagueService.getLeagueAsDto(UUID.fromString(leagueId)));
            return filterUserAwardLanguage(userAwardDto, language);
        } else {
            throw new InvalidOperationException("assignUserAward", SYSTEM_USERNAME, "award with id = " + awardId + " not found");
        }
    }

    public Optional<HmLeagueMemberAwardsDO> assignLeagueWinnerAward(UUID leagueId, HmAward award, HmUserProfile user, HmAwardDescriptionDO awardDescription) throws EntityNotExistException, InvalidOperationException {
        return awardAssignmentHandler.assignLeagueWinnerAward(leagueId, award, user, awardDescription);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true)
    public Optional<HmAward> getLeagueWinnerAward() {
        // first try to find the league winner with localizations, otherwise return empty optional
        return hmAwardRepository.findByCode(AwardCode.LEAGUE_WINNER).stream().filter(a -> !a.getDescriptions().isEmpty()).findFirst();
    }

    public PageableResult<UserAwardDto> getAllUserAwards(Pageable pageable, String language) throws FormatException {
        return this.filterUserAwardsLanguage(mapEntityPageToDtoPageableResult(hmUserAwardRepository.findAll(pageable), hmUserAwardMapper, pageable), language);
    }

    public UserAwardDto getUserAwardById(String userAwardId, String language) throws FormatException {
        return filterUserAwardLanguage(hmUserAwardMapper.mapToDto(hmUserAwardRepository.findById(Util.convertId(userAwardId)).orElse(null)), language);
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true)
    public PageableResult<UserAwardDto> getAllAwardsByUser(String userId, Pageable pageable, String language) throws FormatException, EntityNotExistException {
        // WIP: Endpoint temporary inactive until awards feature completion. Uncomment to deactivate it
        //<return new PageableResult<>(emptyList(), 0, 0, 0);

        HmUserProfile user = userProfileService.getByIdInNewTransaction(userId);
        String currentLeagueId = user.getSessionAttribute(CURRENT_LEAGUE);
        if (nonNull(currentLeagueId)) {
            UUID leagueId = UUID.fromString(currentLeagueId);

            // Get league winner award of previous leagues
            List<UUID> previousLeagueIds = new ArrayList<>();
            UUID previousLeagueId = leagueService.getPreviousLeagueId(leagueId);
            while (nonNull(previousLeagueId)) {
                previousLeagueIds.add(previousLeagueId);
                UUID tempLeagueId = previousLeagueId;
                previousLeagueId = leagueService.getPreviousLeagueId(tempLeagueId);
            }
            List<HmUserAward> leagueWinnerAwards = hmUserAwardRepository.findAwardByUserInPreviousLeagues(UUID.fromString(userId), previousLeagueIds, LEAGUE_WINNER);
            int size = leagueWinnerAwards.size();
            // get all user awards by league (including league winner award)
            List<HmUserAward> userAwards = hmUserAwardRepository.findByUserIdAndLeagueId(Util.convertId(userId), leagueId);

            // add all these previous league winner awards to the list and update numberOfAchievements for each league winner award
            if (size >= 1) {
                userAwards.addAll(leagueWinnerAwards);
                long countLeagueWinnerAwards = userAwards.stream().filter(ua -> ua.getAward().getCode().equals(LEAGUE_WINNER)).count();
                userAwards.stream().filter(ua -> ua.getAward().getCode().equals(LEAGUE_WINNER))
                        .forEach(ua -> ua.setNumberOfAchievements(Long.valueOf(countLeagueWinnerAwards).intValue()));
            }

            // 1- First, group by award id (or award code)
            Map<UUID, List<HmUserAward>> awardsByCodeMap = userAwards.stream()
                    .collect(Collectors.groupingBy(
                            hmUserAward -> hmUserAward.getAward().getId()));

            // 2- get unique user award by league and code (maximum 16)
            // Filter duplicate user awards that could be created in case two transaction are committed simultaneously.
            // Awards could be duplicate for transaction reasons (two auction-end-jobs in the same schedules)
            List<HmUserAward> userAwardsWithoutDuplicates =
                    awardsByCodeMap.values().stream().map(userAwardsByCodeList -> {
                                Optional<HmUserAward> lastCreated = new ArrayList<>(userAwardsByCodeList).stream().filter(userAward -> nonNull(userAward.getCreatedAt())).max(Comparator.comparing(HmUserAward::getCreatedAt));
                                return lastCreated.orElse(null);
                            }
                    ).filter(Objects::nonNull).toList();

            // 3- sort by award order
            ArrayList<HmUserAward> sortedUserAwards = new ArrayList<>(userAwardsWithoutDuplicates);
            sortedUserAwards.sort(Comparator.comparingInt(ua -> ua.getAward().getDisplayOrder()));

            Page<HmUserAward> userAwardsWithoutDuplicatesPage = new PageImpl<>(sortedUserAwards, pageable, sortedUserAwards.size());
            PageableResult<UserAwardDto> result = this.filterUserAwardsLanguage(mapEntityPageToDtoPageableResult(userAwardsWithoutDuplicatesPage, hmUserAwardMapper, pageable), language);
            // 4- make sure that the league winner award has the ID of the requested league: in case the user won the award in a previous league
            result.content().forEach(dto -> dto.setLeagueId(leagueId.toString()));
            return result;
        } else {
            log.info("getAllAwardsByUser: current league by user id [" + userId + "] is not set. Empty awards list returned. Skipping..");
            return new PageableResult<>(emptyList(), 0, 0, 0);
        }
    }

    public List<Pair<HmUserAwardCreatedDO, HmAward>> getUserAwardsSinceDate(UUID userId, UUID leagueId, LocalDateTime sinceDate) {
        List<HmUserAwardCreatedDO> userAwardDOS = hmUserAwardRepository.findByUserIdAndLeagueIdSinceDate(userId, leagueId, sinceDate);
        return userAwardDOS.stream().map(DO -> {
            Optional<HmAward> awardOptional = getAwardById(UUID.fromString(DO.getAwardId()));
            return awardOptional.map(award -> Pair.of(DO, award)).orElse(null);
        }).filter(Objects::nonNull).toList();
    }

    public void deleteUserAward(String userAwardId) throws FormatException {
        hmUserAwardRepository.deleteById(Util.convertId(userAwardId));
    }

    public void deleteByUserId(UUID userId) {
        hmUserAwardRepository.deleteByUserId(userId);
    }

    public void activateAwardsByUserAndLeagues(UUID userId, List<UUID> leagueIds) {
        int rows = hmUserAwardRepository.activateAwardsByUserIdAndLeagueIdsIn(userId, leagueIds);
        log.info("activateAwardsByUserAndLeagues: [" + rows + "] user awards activated for user id[" + userId + "] and league ids[" + leagueIds.toString() + "]");
    }

    private int updateDisplayOrder(AwardDto awardRequestDto) {
        int displayOrder = awardRequestDto.getDisplayOrder();
        /* Example: If you have 3 awards with orders (a1,1), (a2,2), (a3,3) and you want to insert an award a4 with order 2
         you will get (a1,1), (a4,2), (a2,3), (a3,4) */
        Iterable<HmAward> allAwards = hmAwardRepository.findAll();
        List<HmAward> awardsToAdjust = Util.toStream(allAwards)
                .filter(a -> a.getDisplayOrder() >= displayOrder).peek(a1 -> a1.setDisplayOrder(a1.getDisplayOrder() + 1)).toList();
        return awardsToAdjust.isEmpty() ? Long.valueOf(Util.toStream(allAwards).count()).intValue() + 1 : displayOrder;
    }

    private UUID savePicture(MultipartFile file) throws IOException {
        return file == null ? null : imageService.saveAsUUID(file, ImageDomain.AWARD_IMAGE);
    }

    private List<SharedLocalization> convertAwardLocalizations(UUID entityId, Map<String, AwardLocalizationDto> awardLocalizations) {
        List<SharedLocalization> descriptions = new ArrayList<>();
        awardLocalizations.forEach((k, v) -> {
            try {
                Locale locale = Util.getLocaleByLanguageTag(k);
                descriptions.add(new SharedLocalization(EntityType.AWARD, entityId, LocalizationConstants.NAME, locale, v.getName()));
                descriptions.add(new SharedLocalization(EntityType.AWARD, entityId, LocalizationConstants.DESCRIPTION, locale, v.getDescription()));
            } catch (FormatException e) {
                Util.sneakyThrow(e);
            }
        });
        return descriptions;
    }

    private UserAwardDto filterUserAwardLanguage(UserAwardDto userAwardDto, String language) throws FormatException {
        if (nonNull(language))
            return doFilterUserAwardLanguage(userAwardDto, Util.getLanguageTag(Util.getLocaleByLanguageTag(language)));
        return userAwardDto;
    }

    private PageableResult<UserAwardDto> filterUserAwardsLanguage(PageableResult<UserAwardDto> list, String language) throws FormatException {
        if (nonNull(language)) {
            String locale = Util.getLanguageTag(Util.getLocaleByLanguageTag(language));
            list.content().forEach(dto -> doFilterUserAwardLanguage(dto, locale));
        }
        return list;
    }

    private PageableResult<AwardDto> filterAwardsLanguage(PageableResult<AwardDto> list, String language) throws FormatException {
        if (nonNull(language)) {
            String locale = Util.getLanguageTag(Util.getLocaleByLanguageTag(language));
            list.content().forEach(dto -> doFilterAwardLanguage(dto, locale));
        }
        return list;
    }

    private AwardDto filterAwardLanguage(AwardDto awardDto, String language) throws FormatException {
        if (nonNull(language))
            return doFilterAwardLanguage(awardDto, Util.getLanguageTag(Util.getLocaleByLanguageTag(language)));
        return awardDto;
    }

    private UserAwardDto doFilterUserAwardLanguage(UserAwardDto entity, String language) {
        entity.setAward(doFilterAwardLanguage(entity.getAward(), language));
        return entity;
    }

    private AwardDto doFilterAwardLanguage(AwardDto awardDto, String language) {
        awardDto.setLocalizations(awardDto.getLocalizations().entrySet().stream()
                .filter(e -> Objects.equals(e.getKey(), language))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));
        return awardDto;
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true)
    public List<UserAwardInfoDto> getLeagueAwards(UUID leagueId) {
        List<HmUserAward> leagueAwards;
        if (seasonService.isSummerBreak()) {
            // League winner award of previous league should not be returned in summer break to avoid confusion
            leagueAwards = hmUserAwardRepository.findLeagueAwards(leagueId, List.of(AwardCode.LEAGUE_WINNER));
        } else {
            UUID previousLeagueId = leagueService.getPreviousLeagueId(leagueId);
            // League winner award of previous league should be returned
            leagueAwards = hmUserAwardRepository.findLeagueAwards(leagueId, previousLeagueId, List.of(AwardCode.LEAGUE_WINNER));
        }
        return leagueAwards.stream().filter(userAward -> Objects.nonNull(userAward.getLeague())).map(userAwardInfoMapper::mapToDto).toList();
    }

    /* =================================================================================================================
     * USER AWARDS CHECKS
     * ============================================================================================================== */


    public Pair<Integer, List<HmAwardDescriptionDO>> getMoneyByTransferMarketBuyerAwards(UUID transferMarketId, HmLeague league, HmUserProfile user, AwardCheckingDo awardCheckingDo) {
        try {
            return awardCheckingHandler.getMoneyByTransferMarketBuyerAwards(transferMarketId, league, user, awardCheckingDo);
        } catch (Exception e) {
            log.error("getMoneyByTransferMarketBuyerAwards for transfer market id [" + transferMarketId + "] failed. Reason: " + e.getMessage() + ". Skipping..");
            return Pair.of(0, emptyList());
        }
    }

    public Pair<Integer, List<HmAwardDescriptionDO>> getMoneyByTransferMarketSellerAwards(UUID transferMarketId, HmLeague league, HmUserProfile user, AwardCheckingDo awardCheckingDo) {
        try {
            return awardCheckingHandler.getMoneyByTransferMarketSellerAwards(transferMarketId, league, user, awardCheckingDo);
        } catch (Exception e) {
            log.error("getMoneyByTransferMarketSellerAwards for transfer market id [" + transferMarketId + "] failed. Reason: " + e.getMessage() + ". Skipping..");
            return Pair.of(0, emptyList());
        }
    }

    public Optional<Pair<UUID, HmAwardDescriptionDO>> checkFullHouseAward(HmLeague league, int leagueSize) {
        try {
            return awardCheckingHandler.checkFullHouseAward(league, leagueSize);
        } catch (Exception e) {
            log.error("checkFullHouseAward for league id [" + league.getId() + "] failed. Reason: " + e.getMessage() + ". Skipping..");
            return empty();
        }
    }

    public Optional<Pair<UUID, HmAwardDescriptionDO>> checkStart7Award(UUID userId, UUID leagueId, int lineupSize) {
        try {
            return awardCheckingHandler.checkStart7Award(userId, leagueId, lineupSize);
        } catch (Exception e) {
            log.error("checkStart7Award for user id [" + userId + "]" + leagueId + "] failed. Reason: " + e.getMessage() + ". Skipping..");
            return empty();
        }
    }

    /**
     * Check if an image right award exists. If not assign it to the user
     *
     * @param user user to assign the award to
     * @return award description if it was assigned to the given user
     */
    public Optional<HmAwardDescriptionDO> checkImageRightAward(HmUserProfile user) {
        try {
            return awardCheckingHandler.checkImageRightAward(user);
        } catch (Exception e) {
            log.error("checkImageRightAward for user id [" + user.getId() + "] failed. Reason: " + e.getMessage() + ". Skipping..");
            return empty();
        }
    }

    public int getImageRightAwardExperiencePoints() {
        try {
            Optional<HmAward> awardOptional = getAwardByCode(IMAGE_RIGHT);
            if (awardOptional.isEmpty()) {
                log.info("Award with code = IMAGE_RIGHT does not exist. Skipping..");
            } else {
                return awardOptional.get().getExperiencePoints();
            }
        } catch (Exception e) {
            log.error("getImageRightAwardExperiencePoints failed. Reason: " + e.getMessage() + ". Skipping..");
        }
        return 0;
    }

    public boolean addImageRightUserAward(HmUserProfile user) {
        try {
            if (Objects.nonNull(user.getPicture())) {
                Optional<HmAward> awardOptional = getAwardByCode(IMAGE_RIGHT);
                if (awardOptional.isEmpty()) {
                    log.info("Award with code = IMAGE_RIGHT does not exist. Skipping..");
                } else {
                    HmAward award = awardOptional.get();
                    HmUserAward userAward = hmUserAwardRepository.save(new HmUserAward(award, user));
                    log.info("user award with code [IMAGE_RIGHT] and id[" + userAward.getId() + "] assigned to user [" + user.getId() + "]");
                    Locale locale = Util.getLocaleByLanguageTag(user.getAppLanguage());
                    Optional<SharedLocalization> localizationOptional = award.getDescriptions().stream().filter(localization ->
                            Objects.equals(localization.getLocale().toString(), locale.toString()) && Objects.equals(localization.getKey(), AWARD_KEY_NAME)).findFirst();
                    String awardName = localizationOptional.isPresent() ? localizationOptional.get().getValue() : IMAGE_RIGHT.name();
                    Map<NotificationBodyKeyword, String> keywords = userNotificationHandler.getNotificationBodyKeywords(null, awardName);
                    UUID picture = award.getPicture();
                    messagingService.saveUserNotification(
                            userNotificationHandler.getNotificationTitle(USER_AWARD, locale),
                            userNotificationHandler.getNotificationBody(USER_AWARD, keywords, locale),
                            user, new AwardNotificationDo(USER_AWARD, nonNull(picture) ? picture.toString() : null));
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("addImageRightUserAward for user id [" + user.getId() + "] failed. Reason: " + e.getMessage() + ". Skipping..");
        }
        return false;
    }

    public void assignAllFullHouseAwards() throws EntityNotExistException {
        Optional<HmAward> awardOptional = getAwardByCode(FULL_HOUSE);
        if (awardOptional.isPresent()) {
            Pair<List<HmUserProfile>, List<HmLeagueMemberAwardsDO>> userCacheMembershipAwardsPair = awardAssignmentHandler.assignAllFullHouseAwards(awardOptional.get());
            List<HmUserProfile> userCache = userCacheMembershipAwardsPair.getKey();
            List<HmLeagueMemberAwardsDO> leagueMemberAwardsResult = userCacheMembershipAwardsPair.getValue();
            // send notifications to users who have earned awards
            Map<UUID, List<HmLeagueMemberAwardsDO>> awardsByUserMap = leagueMemberAwardsResult.stream().filter(t -> nonNull(t.getUserId())).collect(Collectors.groupingBy(HmLeagueMemberAwardsDO::getUserId));
            sendUserNotifications("assignAllFullHouseAwards", awardsByUserMap, userCache, true);

        } else {
            log.info("assignAllFullHouseAwards: award with code:" + FULL_HOUSE + " not found.");
        }
    }

    public void assignMatchDayAwards(String roundId) throws EntityNotExistException, FormatException {
        HmRound round = seasonService.getRound(roundId);
        HmRound currentRound = seasonService.getCurrentRound();
        // recalculate next round
        try {
            if (seasonService.isNextRoundPresent()) {
                HmRound nextRound = seasonService.getNextRoundOptReadOnly();
                if (Objects.equals(nextRound.getId(), UUID.fromString(roundId))) {
                    log.info("Disable rescheduling LeagueScoreAwardsJob for next round. Reason: next round id = round id = " + roundId);
                } else if (schedulerService.existsByRoundResultJobNameAndRoundId(LEAGUE_SCORE_AWARDS_JOB, nextRound.getId(), false)) {
                    log.info("Disable rescheduling LeagueScoreAwardsJob for next round. Reason: next round id =" + roundId + " is already scheduled.");
                } else {
                    log.info("Rescheduling LeagueScoreAwardsJob for next round id[" + nextRound.getId() + "]");
                    schedulerService.scheduleLeagueScoreAwardsJob(nextRound.getId(), nextRound.getTo());
                }
            }
        } catch (Exception e) {
            log.error("Rescheduling RoundResultUpdateJob failed. Skipping..", e);
        }
        leagueScoreAwardsHandler.assignMatchDayAwards(round, currentRound);
    }

    public void assignMatchDayAwardsAsAdmin(String roundId) throws EntityNotExistException, FormatException {
        HmRound round = seasonService.getRound(roundId);
        HmRound currentRound = seasonService.getCurrentRound();
        leagueScoreAwardsHandler.assignMatchDayAwards(round, currentRound);
    }

    public void addExperiencePointsByLeagueScoreAwards(UUID roundId) {
        leagueScoreAwardsHandler.addExperiencePointsByLeagueScoreAwards(roundId);
    }

    /* =================================================================================================================
     * USER AWARDS NOTIFICATIONS
     * ============================================================================================================== */

    public void sendNotificationByLeagueAward(String leagueName, UUID userId, HmAwardDescriptionDO awardDescriptionDO) {
        Map<NotificationBodyKeyword, String> keywords = userNotificationHandler.getNotificationBodyKeywords(leagueName, awardDescriptionDO.getDescription(), awardDescriptionDO.getMoney());
        doSendNotificationByAward(awardDescriptionDO, USER_LEAGUE_AWARD, keywords, userId);
    }

    public void sendNotificationByLeagueScoreAward(String leagueName, HmUserProfile user, String token, HmAwardDescriptionDO awardDescriptionDO) {
        if (nonNull(token)) {
            Map<NotificationBodyKeyword, String> keywords = userNotificationHandler.getNotificationBodyKeywords(leagueName, awardDescriptionDO.getDescription(), awardDescriptionDO.getMoney());
            doSendNotificationByAward(awardDescriptionDO, USER_LEAGUE_AWARD, keywords, user, token);
        } else {
            log.info("sendNotificationByLeagueScoreAward for user [" + user.getId() + "] in league [" + leagueName + "] failed. Token is null. Skipping..");
        }
    }

    public void sendNotificationByLeagueWinnerAward(String leagueName, HmUserProfile user, String token, HmAwardDescriptionDO awardDescriptionDO) {
        if (nonNull(token)) {
            Map<NotificationBodyKeyword, String> keywords = userNotificationHandler.getNotificationBodyKeywords(leagueName, null);
            doSendNotificationByAward(awardDescriptionDO, LEAGUE_WINNER_AWARD, keywords, user, token);
        } else {
            log.info("sendNotificationByLeagueWinnerAward for user [" + user.getId() + "] in league [" + leagueName + "] failed. Token is null. Skipping..");
        }
    }

    private void doSendNotificationByAward(HmAwardDescriptionDO awardDescriptionDO, NotificationEvent event, Map<NotificationBodyKeyword, String> keywords, HmUserProfile user, String token) {
        Locale locale = awardDescriptionDO.getLocale();
        UUID awardPicture = awardDescriptionDO.getAwardPicture();
        log.info("Firebase message with id [" + messagingService.sendNotification(
                userNotificationHandler.getNotificationTitle(event, locale),
                userNotificationHandler.getNotificationBody(event, keywords, locale),
                user, token, new AwardNotificationDo(event, nonNull(awardPicture) ? awardPicture.toString() : null)) + "sent. Event: " + event);
    }

    public void saveNotificationByLeagueScoreAward(String leagueName, HmUserProfile user, HmAwardDescriptionDO awardDescriptionDO) {
        Map<NotificationBodyKeyword, String> keywords = userNotificationHandler.getNotificationBodyKeywords(leagueName, awardDescriptionDO.getDescription(), awardDescriptionDO.getMoney());
        doSaveNotificationByAward(awardDescriptionDO, USER_LEAGUE_AWARD, keywords, user);
    }

    public void saveNotificationByLeagueWinnerAward(String leagueName, HmUserProfile user, HmAwardDescriptionDO awardDescriptionDO) {
        Map<NotificationBodyKeyword, String> keywords = userNotificationHandler.getNotificationBodyKeywords(leagueName, null);
        doSaveNotificationByAward(awardDescriptionDO, LEAGUE_WINNER_AWARD, keywords, user);

    }

    private void doSaveNotificationByAward(HmAwardDescriptionDO awardDescriptionDO, NotificationEvent event, Map<NotificationBodyKeyword, String> keywords, HmUserProfile user) {
        Locale locale = awardDescriptionDO.getLocale();
        UUID awardPicture = awardDescriptionDO.getAwardPicture();
        messagingService.saveAwardUserNotification(
                userNotificationHandler.getNotificationTitle(event, locale),
                userNotificationHandler.getNotificationBody(event, keywords, locale),
                user, new AwardNotificationDo(event, nonNull(awardPicture) ? awardPicture.toString() : null));
    }

    public void sendNotificationByUserAward(UUID userId, HmAwardDescriptionDO awardDescriptionDO) {
        Map<NotificationBodyKeyword, String> keywords = userNotificationHandler.getNotificationBodyKeywords(null, awardDescriptionDO.getDescription());
        doSendNotificationByAward(awardDescriptionDO, USER_AWARD, keywords, userId);
    }

    private void doSendNotificationByAward(HmAwardDescriptionDO awardDescriptionDO, NotificationEvent event, Map<NotificationBodyKeyword, String> keywords, UUID userId) {
        Locale locale = awardDescriptionDO.getLocale();
        UUID awardPicture = awardDescriptionDO.getAwardPicture();
        log.info("Firebase message with id [" + messagingService.sendNotification(
                userNotificationHandler.getNotificationTitle(event, locale),
                userNotificationHandler.getNotificationBody(event, keywords, locale),
                userId.toString(),
                new AwardNotificationDo(event, nonNull(awardPicture) ? awardPicture.toString() : null)) + "sent. Event: " + event);
    }

    public void sendUserNotifications(String process, Map<UUID, List<HmLeagueMemberAwardsDO>> awardsByUserMap, List<HmUserProfile> userCache, boolean sendFirebaseNotification) {
        // 1- If firebase notifications should be sent, get all tokens in bulk in order to reduce transactions
        List<HmUserTokenDO> userTokenList = sendFirebaseNotification ?
                messagingService.getFcmTokens(awardsByUserMap.keySet()) : emptyList();
        if (sendFirebaseNotification && userTokenList.isEmpty()) {
            log.info(process + ":sendUserNotifications skipped. Reason: user tokens list is empty.");
            return;
        }

        // 2- send notification to the users
        AtomicInteger counter = new AtomicInteger(1);
        int size = awardsByUserMap.size();
        log.info(process + ": sendUserNotifications, count leagues to process = " + size);

        awardsByUserMap.entrySet().stream().parallel().forEach(entry -> {
            UUID userId = entry.getKey();
            try {
                transactionHandler.runInNewTransaction(() -> {
                    sendNotificationByUser(process, userCache, userTokenList, entry, userId, sendFirebaseNotification);
                    return null;
                });
                log.info(process + ": sendUserNotifications for user [" + userId + "] done. (" + counter + " / " + size + ")");
            } catch (Exception e) {
                log.info(process + ": sendUserNotifications for user [" + userId + "] failed. Reason: " + e.getMessage() + e.getCause() + ". (" + counter + " / " + size + ")");
                logMessageService.logException(process, "sendUserNotifications for user [" + userId + "] failed", e);
            }
            counter.getAndIncrement();
        });
    }

    private void sendNotificationByUser(String process, List<HmUserProfile> userCache, List<HmUserTokenDO> userTokenList, Map.Entry<UUID, List<HmLeagueMemberAwardsDO>> entry, UUID userId, boolean sendFirebaseNotification) {
        List<HmLeagueMemberAwardsDO> awardsByUser = entry.getValue();
        // get user from cache
        Optional<HmUserProfile> userProfileOptional = awardCheckingHandler.getFromUserCache(userCache, userId);
        if (userProfileOptional.isPresent()) {
            HmUserProfile user = userProfileOptional.get();
            if (sendFirebaseNotification) {
                Optional<HmUserTokenDO> userTokenOptional = userTokenList.stream().filter(hmUserTokenDO -> Objects.equals(hmUserTokenDO.getUserId(), userId)).findFirst();
                if (userTokenOptional.isPresent()) {
                    String token = userTokenOptional.get().getToken();
                    awardsByUser.forEach(
                            leagueMemberAwardsDO ->
                                    leagueMemberAwardsDO.getAwardDescriptions().forEach(awardDescriptionDO ->
                                    {
                                        if (Objects.equals(awardDescriptionDO.getAwardCode().name(), LEAGUE_WINNER.name())) {
                                            sendNotificationByLeagueWinnerAward(leagueMemberAwardsDO.getLeagueName(), user, token, awardDescriptionDO);
                                        } else {
                                            sendNotificationByLeagueScoreAward(leagueMemberAwardsDO.getLeagueName(), user, token, awardDescriptionDO);
                                        }
                                    }));
                }
            } else {
                awardsByUser.forEach(
                        leagueMemberAwardsDO ->
                                leagueMemberAwardsDO.getAwardDescriptions().forEach(awardDescriptionDO ->
                                {
                                    if (Objects.equals(awardDescriptionDO.getAwardCode().name(), LEAGUE_WINNER.name())) {
                                        saveNotificationByLeagueWinnerAward(leagueMemberAwardsDO.getLeagueName(), user, awardDescriptionDO);
                                    } else {
                                        saveNotificationByLeagueScoreAward(leagueMemberAwardsDO.getLeagueName(), user, awardDescriptionDO);
                                    }
                                }));
            }
        } else {
            String sendUserNotificationsMsg = sendFirebaseNotification ? "sendUserNotifications" : "saveUserNotifications";
            log.info(process + ": " + sendUserNotificationsMsg + " for user [" + userId + "] skipped. Reason: [HmUserProfile not found].");
        }
    }
}