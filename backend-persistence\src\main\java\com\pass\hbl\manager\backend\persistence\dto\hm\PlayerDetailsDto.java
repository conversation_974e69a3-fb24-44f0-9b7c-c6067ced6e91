package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonRootName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDate;


@JsonRootName("PlayerDetails")
@Getter
@Setter
@Schema(description = "All Player info")
public class PlayerDetailsDto extends AbstractPictureDto<PlayerDetailsDto, String> {

    @Schema(description = "Player id", example = "005aa59d-81d9-41eb-9df7-94867a1f7542", required = true)
    @NotBlank
    @Size(min = 36, max = 36)
    private String id;

    @NotBlank
    @Schema(description = "First name", example = "John", required = true)
    private String firstName;

    @NotBlank
    @Schema(description = "Last name", example = "Doe", required = true)
    private String lastName;

    @Size(min = 3, max = 3)
    @Schema(description = "Nationality in ISO 3166-A3 format")
    private String nationality;

    @Schema(description = "An optional status if player could not play")
    private PlayerStatus status;

    @Schema(description = "Height of the player")
    private Integer height;

    @Schema(description = "Weight of the player")
    private Integer weight;

    @NotNull
    @Schema(description = "Gender of the player", required = true)
    private Gender gender;

    @NotNull
    @Schema(description = "Position of the player", required = true)
    private Position position;

    @Size(max = 32)
    @Schema(description = "Optional nickname of the player")
    private String nickname;

    @Schema(description = "Optional date of birth")
    private LocalDate dateOfBirth;

    @Schema(description = "Optional jersey number")
    private Integer jerseyNumber;

    @Schema(description = "Current market value")
    private Integer marketValue;

    @Schema(description = "Current club of the player")
    private ClubDto club;

    @Schema(description = "Statistics of the played rounds")
    PlayerStatisticsDto playedRoundsStatistics;

    @Schema(description = "Hbl URL of the image", example = "663678a4b6344926a8ac580bc0398884")
    private String hblImageId;
}
