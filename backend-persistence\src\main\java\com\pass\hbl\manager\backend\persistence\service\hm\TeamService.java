package com.pass.hbl.manager.backend.persistence.service.hm;

import com.pass.hbl.manager.backend.persistence.domain.hm.HmPlayerDO;
import com.pass.hbl.manager.backend.persistence.domain.hm.HmPlayerMarketValueDO;
import com.pass.hbl.manager.backend.persistence.dto.hm.*;
import com.pass.hbl.manager.backend.persistence.entity.hm.*;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.exception.ForbiddenOperationException;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.exception.InvalidOperationException;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmLineupRepository;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmTeamRepository;
import com.pass.hbl.manager.backend.persistence.service.admin.ParameterService;
import com.pass.hbl.manager.backend.persistence.service.hm.handlers.TeamAdminHandler;
import com.pass.hbl.manager.backend.persistence.service.hm.handlers.TeamLineUpHandler;
import com.pass.hbl.manager.backend.persistence.service.hm.handlers.TeamSetupHandler;
import com.pass.hbl.manager.backend.persistence.util.ParameterDefaults;
import com.pass.hbl.manager.backend.persistence.util.Util;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.EventListener;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.LockModeType;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.*;

import static com.pass.hbl.manager.backend.persistence.util.Constants.*;
import static java.time.LocalDateTime.now;
import static java.util.Collections.emptyList;
import static java.util.Collections.emptyMap;

@Slf4j
@Service
@Transactional
public class TeamService {

    private final HmTeamRepository teamRepository;
    private final HmLineupRepository lineupRepository;

    private final UserProfileService userProfileService;
    private final SeasonService seasonService;
    private final TransferMarketService transferMarketService;
    private final LeagueService leagueService;
    private final PlayerService playerService;
    private final MatchService matchService;
    private final ParameterService parameterService;

    private final TeamLineUpHandler lineUpHandler;
    private final TeamSetupHandler teamSetupHandler;
    private final TeamAdminHandler teamAdminHandler;

    @Getter
    private int maxTeamSize = ParameterDefaults.DEFAULT_MAX_TEAM_SIZE;

    public TeamService(HmTeamRepository teamRepository,
                       HmLineupRepository lineupRepository,
                       @Lazy UserProfileService userProfileService,
                       @Lazy SeasonService seasonService,
                       @Lazy LeagueService leagueService,
                       @Lazy TransferMarketService transferMarketService,
                       @Lazy PlayerService playerService, MatchService matchService, ParameterService parameterService, TeamLineUpHandler lineUpHandler, TeamSetupHandler teamSetupHandler, TeamAdminHandler teamAdminHandler) {
        this.userProfileService = userProfileService;
        this.teamRepository = teamRepository;
        this.lineupRepository = lineupRepository;
        this.seasonService = seasonService;
        this.leagueService = leagueService;
        this.transferMarketService = transferMarketService;
        this.playerService = playerService;
        this.matchService = matchService;
        this.parameterService = parameterService;
        this.lineUpHandler = lineUpHandler;
        this.teamSetupHandler = teamSetupHandler;
        this.teamAdminHandler = teamAdminHandler;
    }

    @EventListener(ApplicationReadyEvent.class)
    @Transactional(readOnly = true)
    public void init() throws InvalidOperationException, FormatException {
        maxTeamSize = parameterService.getAsInteger(ParameterDefaults.PARAM_MAX_TEAM_SIZE, SYSTEM_USERNAME);
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true)
    public TeamDto getTeamByLeagueAsDto(@NotNull String userId, @NotNull String leagueId) throws EntityNotExistException, FormatException, ForbiddenOperationException {
        HmUserProfile userProfile = userProfileService.getById(userId);
        return createTeam(userId, leagueId, userProfile);
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true)
    public TeamDto getTeamAsDto(@NotNull String currentUserId, @NotNull String ownerId) throws EntityNotExistException, FormatException {
        // In order to get the team of the requested user (ownerId), this method determines the current league
        // based on the requester league (current user)
        HmUserProfile currentUser = userProfileService.getById(currentUserId);
        String leagueId = currentUser.getSessionAttribute(SessionAttribute.CURRENT_LEAGUE);
        if (ownerId.equals(ANONYMOUS_ID)) {
            // required fields should be set in LineupDto otherwise the Frontend could not process the response
            return new TeamDto(leagueService.getByIdAsDto(leagueId), UserDto.getAnonymous(), emptyList(), 0);
        }
        log.info(" get current team by user is called by user: "+ currentUserId + " for user: " + ownerId + "and league: " + leagueId);
        //WIP: get team for latest closed round in order to get the round scores of another manager displayed
        Optional<HmRound> latestClosedRoundOptional = seasonService.getLatestClosedRound();
        // if latest closed round is not present, for example during the first round in season, the team of the
        // current round is returned
        HmRound round = latestClosedRoundOptional.orElse(seasonService.getCurrentRound());
        boolean includePlayerScore = includePlayerScore(currentUser, round);
        if (currentUserId.equals(ownerId)) {
            return doGetTeamByRoundAsDto(round, currentUser, leagueService.getById(leagueId), includePlayerScore);
        } else {
            HmUserProfile owner = userProfileService.getById(ownerId);
            return doGetTeamByRoundAsDto(round, owner, leagueService.getById(leagueId), includePlayerScore);
        }
    }

    private TeamDto createTeam(String userId, String leagueId, HmUserProfile userProfile) throws EntityNotExistException, FormatException, ForbiddenOperationException {
        HmLeague league = leagueService.getLeague(userId, leagueId);
        HmRound round;
        try {
            round = seasonService.getCurrentRound();
        } catch (Exception e) {
            log.info("Current round not found, latest closed round is used instead");
            Optional<HmRound> roundOptional = seasonService.getLatestClosedRound();
            if (roundOptional.isPresent()) {
                round = roundOptional.get();
            } else {
                return new TeamDto(leagueService.mapToDto(league), userProfileService.mapToReducedDto(userProfile), emptyList(), 0);
            }
        }
        // player score is not returned if the lineup is updatable
        boolean includePlayerScore = includePlayerScore(userProfile, round);
        HmRound finalRound = round;
        List<PlayerDto> players = Util.toStream(getTeam(userProfile, league))
                .map(HmTeam::getPlayer)
                .map(p-> (includePlayerScore? playerService.mapToDto(p, Map.of(ROUND_ID, finalRound.getId().toString())): playerService.mapToDto(p, null)))
                .toList();
        int marketValue = players.stream().mapToInt(PlayerDto::getMarketValue).sum();
        return new TeamDto(leagueService.mapToDto(league), userProfileService.mapToReducedDto(userProfile), players, marketValue);
    }

    @Transactional(readOnly = true)
    public TeamDetailsDto getTeamDetailsAsDto(@NotNull String userId, @NotNull String leagueId) throws EntityNotExistException, FormatException, ForbiddenOperationException {
        HmUserProfile userProfile = userProfileService.getById(userId);
        HmLeague league = leagueService.getLeague(userId, leagueId);
        List<PlayerDetailsDto> players = Util.toStream(getTeam(userProfile, league))
                .map(HmTeam::getPlayer)
                .map(playerService::mapToPlayerDetailsDto)
                .toList();
        int marketValue = players.stream().mapToInt(PlayerDetailsDto::getMarketValue).sum();
        return new TeamDetailsDto(leagueService.mapToDto(league), userProfileService.mapToReducedDto(userProfile), players, marketValue);
    }

    @Transactional(readOnly = true)
    public List<HmTeam> getTeam(@NotNull HmUserProfile userProfile, @NotNull HmLeague league) {
        return teamRepository.findByOwnerIdAndLeagueIdAndLeftIsNull(userProfile.getId(), league.getId());
    }

    @Transactional(readOnly = true)
    public List<HmTeam> getTeamInRound(UUID userId, UUID leagueId, LocalDateTime roundClosing) {
        return teamRepository.findByOwnerIdAndLeagueIdAndRound(userId, leagueId, roundClosing);
    }

    @Transactional(readOnly = true)
    public List<HmTeam> getTeamByUserIdAndLeagueId(@NotNull UUID userId, @NotNull UUID leagueId) {
        return teamRepository.findByOwnerIdAndLeagueIdAndLeftIsNull(userId, leagueId);
    }

    public Optional<HmTeam> getTeamMember(@NotNull UUID userId, @NotNull UUID leagueId, @NotNull UUID playerId) {
        return teamRepository.findFirstByOwnerIdAndLeagueIdAndPlayerIdAndLeftIsNull(userId, leagueId, playerId);
    }

    @Transactional(readOnly = true)
    public Optional<HmTeam> getTeam(@NotNull UUID leagueId, @NotNull UUID playerId) throws EntityNotExistException, FormatException {
        return teamRepository.findFirstByLeagueAndPlayerIdAndLeftIsNull(leagueService.getById(leagueId.toString()),
                playerId);
    }

    @Transactional(readOnly = true)
    public int getTeamSize(@NotNull UUID leagueId, @NotNull UUID ownerId) {
        return teamRepository.countByOwnerIdAndLeagueIdAndLeftIsNull(ownerId, leagueId);
    }

    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public boolean isPlayerOwnedByUser(@NotNull HmUserProfile userProfile, @NotNull HmLeague league, @NotNull UUID playerId) {
        return getTeam(userProfile, league).stream().map(HmTeam::getPlayer).map(HmPlayer::getId).anyMatch(id -> id.equals(playerId));
    }

    @Transactional(readOnly = true, propagation = Propagation.REQUIRED)
    public boolean isPlayerOwnedByUser(@NotNull UUID ownerId, @NotNull UUID leagueId, @NotNull UUID playerId) {
        return teamRepository.existsByOwnerIdAndLeagueIdAndPlayerIdAndLeftIsNull(ownerId, leagueId, playerId);
    }

    @Transactional(readOnly = true, propagation = Propagation.REQUIRED)
    public boolean playerExistsInLeague(@NotNull UUID playerId, @NotNull UUID leagueId) {
        return teamRepository.existsByLeagueIdAndPlayerIdAndLeftIsNull(leagueId, playerId);
    }

    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public boolean isPlayerInLatestLineup(HmUserProfile user, String leagueId, UUID playerId) throws EntityNotExistException, FormatException {
        return isPlayerInLatestLineup(user.getId().toString(), leagueId, playerId);
    }

    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public boolean isPlayerInLatestLineup(String userId, String leagueId, UUID playerId) throws EntityNotExistException, FormatException {
        HmRound currentRound = seasonService.getLatestRound();
        List<HmLineup> lineupList = lineUpHandler.getLineup(userId, leagueId, currentRound.getId().toString(), false);
        return lineupList.
                stream().map(HmLineup::getTeam).map(HmTeam::getPlayer).anyMatch(player -> player.getId().equals(playerId));
    }

    public void deleteTeamsByLeague(@NotNull UUID leagueId) {
        transferMarketService.deleteAppearancesAndBidsByLeague(leagueId);
        lineupRepository.deleteByLeague(leagueId);
        teamRepository.deleteByLeague(leagueId);
    }

    @Transactional(readOnly = true)
    public long getFreePlayersForTransferMarket(UUID leagueId) {
        return teamRepository.countByLeagueIdNotAndLeftIsNull(leagueId);
    }

    @Transactional(propagation = Propagation.MANDATORY)
    public void deleteTeamById(UUID teamId) {
        lineupRepository.deleteByTeamMember(teamId);
        teamRepository.deleteById(teamId);
    }

    public void deleteTeamByLeagueAndOwner(UUID leagueId, UUID ownerId) {
        lineupRepository.deleteByLeagueAndOwner(leagueId, ownerId);
        teamRepository.deleteByLeagueAndOwner(leagueId, ownerId);
    }

    @Transactional(readOnly = true)
    public LineupDto getCurrentLineupByLeagueAsDto(@NotNull String userId, @NotNull String leagueId) throws EntityNotExistException, FormatException, ForbiddenOperationException {
        log.info(" get current lineup by user and league is called for user: " + userId + "league: " + leagueId);
        return lineUpHandler.getLineupByLeagueAsDto(userId, leagueId, seasonService.getCurrentRound().getId().toString());
    }

    public List<HmLineup> getLineupByLeague(HmUserProfile user, @NotNull String leagueId, @NotNull String roundId) throws EntityNotExistException, FormatException {
        return lineUpHandler.getLineup(user, leagueId, roundId, true);
    }

    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public int getLineupMarketValueAtDate(String userId, String leagueId, UUID roundId, List<HmPlayerMarketValueDO> allPlayerMarketValuesAtDate) {
        int lineupMarketValue = lineUpHandler.getLineupMarketValueAtDate(userId, leagueId, roundId, allPlayerMarketValuesAtDate);
        log.info("LineupMarketValue = " + lineupMarketValue +" for user: " + userId + " and league: " + leagueId + " and round: " + roundId);
        return lineupMarketValue;
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true)
    public List<UUID> getPlayersInLineupByLeague(HmUserProfile user, @NotNull String leagueId, @NotNull String roundId) throws EntityNotExistException, FormatException {
        return getLineupByLeague(user, leagueId, roundId).stream().map(lineup -> lineup.getTeam().getPlayer().getId()).toList();
    }

    //ONLY FOR TEST
    @Transactional(readOnly = true)
    public List<Pair<UUID, String>> getLineupByUserAndLeagueAndRound(String userId, @NotNull String leagueId, @NotNull String roundId) throws EntityNotExistException, FormatException {
        HmUserProfile user = userProfileService.getById(userId);
        return getLineupByLeague(user, leagueId, roundId).stream().map(lineup -> {
            HmPlayer player = lineup.getTeam().getPlayer();
            String label = player.getFirstName() + " " + player.getLastName() + " " + player.getPosition().name();
            return Pair.of(player.getId(), label);
        }).toList();
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true)
    public LineupDto getCurrentLineupAsDto(@NotNull String currentUserId, @NotNull String lineupOwnerId) throws EntityNotExistException, FormatException, ForbiddenOperationException {
        // In order to get the lineup of the requested user (lineupOwnerId), this method determines the current league
        // based on the requester (current user) league
        HmUserProfile currentUser = userProfileService.getById(currentUserId);
        String leagueId = currentUser.getSessionAttribute(SessionAttribute.CURRENT_LEAGUE);
        if (lineupOwnerId.equals(ANONYMOUS_ID)) {
            // required fields should be set in LineupDto otherwise the Frontend could not process the response
            return new LineupDto(emptyMap(), seasonService.getCurrentRoundAsDto(), leagueService.getByIdAsDto(leagueId), UserDto.getAnonymous(), false, false);
        }
        log.info(" get current lineup by user is called by user: "+ currentUserId + " for user: " + lineupOwnerId + " and league: " + leagueId);
        // WIP: get lineup of current round or latest closed round so that the round score could be displayed.
        // Current round ist switched to upcoming round starting from i.e. monday, so that user doesn't have the possibility to see
        // the final results of another manger.
        //HmRound currentRound = seasonService.getCurrentRound();
        Optional<HmRound> latestClosedRoundOptional = seasonService.getLatestClosedRound();
        if (latestClosedRoundOptional.isPresent()) {
            HmRound latestClosedRound = latestClosedRoundOptional.get();
            return lineUpHandler.getLineupByLeagueAsDto(lineupOwnerId, leagueId, latestClosedRound.getId().toString());
        } else {
            // if latest closed round is not present, for example during the first round in season, an empty lineup of
            // current round is returned
            RoundDto currentRoundAsDto = seasonService.getCurrentRoundAsDto();
            HmUserProfile user = userProfileService.getById(lineupOwnerId);
            LeagueInfoDto leagueDto = leagueService.getLeagueAsDto(currentUserId, leagueId);
            boolean isModifiable = currentRoundAsDto.getClosing().toLocalDateTime().isBefore(now());
            boolean isHistoric = currentRoundAsDto.getTo().toLocalDateTime().isBefore(now());
            return new LineupDto(emptyMap(), currentRoundAsDto, leagueDto, userProfileService.mapToReducedDto(user), isModifiable, isHistoric);
        }
    }

    public LineupDto createRandomLineup(@NotNull String userId, @NotNull String leagueId, String roundId, Boolean checkClosing) throws EntityNotExistException, FormatException, ForbiddenOperationException, InvalidOperationException {
        return lineUpHandler.createRandomLineup(userId, leagueId, roundId, checkClosing);
    }

    public LineupDto updateLineup(@NotNull String userId, @NotNull String leagueId, @NotNull Map<Position, String> lineup) throws EntityNotExistException, FormatException, ForbiddenOperationException, InvalidOperationException {
        return lineUpHandler.updateLineup(userId, leagueId, seasonService.getCurrentRound().getId().toString(), lineup);
    }

    public LineupDto updateLineup(@NotNull String userId, @NotNull String leagueId, @NotNull String roundId, @NotNull Map<Position, String> lineup) throws EntityNotExistException, FormatException, ForbiddenOperationException, InvalidOperationException {
        return lineUpHandler.updateLineup(userId, leagueId, roundId, lineup);
    }

    public LineupDto updateLineup(@NotNull String userId, @NotNull LineupDto dto) throws ForbiddenOperationException, InvalidOperationException {
        //TODO hbs remove
        log.info("updateLineup is called for round id: " + dto.getRound().getId());
        return lineUpHandler.updateLineup(userId, dto);
    }

    @Transactional(readOnly = true)
    public LineupDto getLineupAsDto(@NotNull String userId, @NotNull String roundId) throws EntityNotExistException, FormatException, ForbiddenOperationException {
        //TODO hbs remove
        log.info("get lineup by round is called for round id: " + roundId);
        return lineUpHandler.getLineupAsDto(userId, roundId);
    }

    public Pair<Integer, List<HmPlayer>> drawInitialTeam(@NotNull HmUserProfile user, @NotNull HmLeague league) throws InvalidOperationException, FormatException, EntityNotExistException {
        return teamSetupHandler.drawInitialTeam(user, league);
    }

    public List<Pair<HmPlayer, Integer>> getInitialTransferMarketTeam(@NotNull HmUserProfile user, @NotNull HmLeague league, List<HmPlayer> excludedPlayers) throws InvalidOperationException, FormatException, EntityNotExistException {
        return teamSetupHandler.getInitialTransferMarketTeam(user, league, excludedPlayers);
    }

    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public List<HmPlayer> getMissingTransferMarketPlayers(@NotNull HmUserProfile user, @NotNull HmLeague league, List<Position> existingPlayerPositions, List<Integer> existingSystemPlayerPrices, List<HmPlayerDO> allValidPlayers) throws InvalidOperationException, FormatException, EntityNotExistException {
        return teamSetupHandler.getMissingTransferMarketPlayers(user, league, existingPlayerPositions, existingSystemPlayerPrices, allValidPlayers);
    }

    @Transactional(readOnly = true)
    public PlayerDto getRandomTeamPlayer(String userId, String leagueId) throws EntityNotExistException, FormatException, ForbiddenOperationException {
        return teamAdminHandler.getRandomTeamPlayer(userProfileService.getById(userId), leagueService.getLeague(userId, leagueId));
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public int getSquadValue(UUID ownerId, UUID leagueId) {
        return getTeamByUserIdAndLeagueId(ownerId, leagueId).stream().map(HmTeam::getPlayer).mapToInt(HmPlayer::getMarketValue).sum();
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public int getSquadValueAtDate (UUID ownerId, UUID leagueId, LocalDateTime referenceDate) {
        int squadValue = Util.toStream(getTeamInRound(ownerId, leagueId, referenceDate))
                .map(HmTeam::getPlayer).mapToInt(HmPlayer::getMarketValue).sum();
        log.info("Squad value of user[" + ownerId + "] and league[" + leagueId + "] = [" + squadValue + "]");
        return squadValue;
    }

    @Transactional(propagation = Propagation.MANDATORY)
    public HmTeam save(HmTeam hmTeam) {
        return teamRepository.save(hmTeam);
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true)
    public LineupRoundScoreDetailsDto getLineupScoreDetailsAsDto(String userId, String leagueId, String language) throws EntityNotExistException, FormatException, InvalidOperationException {

        // use this one to hardly disable streaming on mobile
        // return new LineupRoundScoreDetailsDto(false, null, null, null, emptyList(), false);

        // Live Streaming disabled in Summer-Break
        if (seasonService.isSummerBreak()) {
            return new LineupRoundScoreDetailsDto(false, null, null, null, emptyList(), false, false);
        }

        LocalDateTime now = LocalDateTime.now();
        HmRound currentRound;
        Optional<HmRound> previousRoundOptional;
        try {
            currentRound = seasonService.getCurrentRound();
        } catch (Exception e) {
            log.info("current round not found: isMatchDayRunning = false. Skipping..");
            return new LineupRoundScoreDetailsDto(false, null, null, null, emptyList(), false, false);
        }

        // The LIVE tab should not be locked immediately with the final whistle of the last game
        // (we are outside the game day...), but remain for another 5 hours
        try {
            previousRoundOptional = seasonService.getPreviousRound(currentRound.getId());
        } catch (Exception e) {
            log.info("previous round not found: isMatchDayRunning = false. Skipping..");
            return new LineupRoundScoreDetailsDto(false, null, null, null, emptyList(), false, false);
        }

        // Used to display the results of the previous round after its closing
        HmRound previousRound = null;
        LocalDateTime previousMatchDayLiveEnd = null;

        // disable streaming
        if (previousRoundOptional.isEmpty()) {
            if (now.isBefore(currentRound.getClosing())) {
                // No need to return the score details because the result is not displayed in this case
                return new LineupRoundScoreDetailsDto(false, null, null, null, emptyList(), false, false);
            }
        } else {
            // get the end date until which live results should be displayed
            previousRound = previousRoundOptional.get();
            previousMatchDayLiveEnd = seasonService.getLiveResultsDisplayEndDate(previousRound.getTo());

            if (now.isBefore(currentRound.getClosing()) && now.isAfter(previousMatchDayLiveEnd)) {
                // No need to return the score details because the result is not displayed in this case
                return new LineupRoundScoreDetailsDto(false, null, null, null, emptyList(), false, false);
            }
        }

        Locale locale = Util.getLocaleByLanguageTag(language);
        Optional<HmLeagueMembership> leagueMembershipOpt = leagueService.getMembership(Util.convertId(userId), Util.convertId(leagueId));
        if (leagueMembershipOpt.isEmpty()) {
            throw new InvalidOperationException("Get team score details for user id[" + userId + "] and league id[" + leagueId + "]", userId, "The user ist not member of the league");
        }

        //HmRound round = Objects.isNull(previousRound) || now.isBefore(previousMatchDayLiveEnd)? previousRound: currentRound;
        HmRound round = Objects.nonNull(previousMatchDayLiveEnd) && now.isBefore(previousMatchDayLiveEnd)? previousRound: currentRound;

        Boolean isScoringEnabled = leagueService.getMemberBalanceAtRoundClosing(leagueMembershipOpt.get(), round) >= 0;
        List<UUID> playerIds = lineUpHandler.getLineupPlayerIds(userId, leagueId, round.getId(), round.getClosing()).stream().toList();
        List<PlayerRoundScoreDetailsDto> playerRoundScoreDetailsDtos = playerService.getLineupScoreDetails(playerIds, round.getId(), locale);
        ZonedDateTime nearestGameStart = matchService.getNearestGameStart(round, userId);
        // in combination with special round, it specifies the round display name i.e. special round 1
        int roundOrder = round.getRoundNumber();
        String seasonLabel = seasonService.getSeasonLabel(round.getId().toString());

        return new LineupRoundScoreDetailsDto(true, nearestGameStart, roundOrder, seasonLabel, playerRoundScoreDetailsDtos, isScoringEnabled, currentRound.isSpecialRound());
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true)
    public TeamDto getTeamByRoundAsDto(String userId, String roundId) throws EntityNotExistException, FormatException, ForbiddenOperationException {
        HmUserProfile userProfile = userProfileService.getById(userId);
        final String currentLeagueId = userProfile.getSessionAttribute(SessionAttribute.CURRENT_LEAGUE);
        HmLeague league = leagueService.getLeague(userId, currentLeagueId);
        HmRound round = seasonService.getRound(roundId);
        includePlayerScore(userProfile, round);
        return doGetTeamByRoundAsDto(round, userProfile, league, includePlayerScore(userProfile, round));
    }

    @org.jetbrains.annotations.NotNull
    private TeamDto doGetTeamByRoundAsDto(HmRound round, HmUserProfile userProfile, HmLeague league, boolean includePlayerScore) {
        List<PlayerDto> players = Util.toStream(getTeamInRound(userProfile.getId(), league.getId(), round.getClosing()))
                .map(HmTeam::getPlayer)
                .map(p-> includePlayerScore? playerService.mapToDto(p, Map.of(ROUND_ID, round.getId().toString())) : playerService.mapToDto(p, null))
                .toList();
        int marketValue = players.stream().mapToInt(PlayerDto::getMarketValue).sum();
        return new TeamDto(leagueService.mapToDto(league), userProfileService.mapToReducedDto(userProfile), players, marketValue);
    }

    private boolean includePlayerScore(HmUserProfile user, HmRound round) {
        // WIP:  player score is not returned if the given round is current round and user is basic since the player score is not published yet or
        // if the lineup is updatable
        LocalDateTime now = now();
        boolean lineupUpdateEnabled = now.isAfter(round.getFrom()) && now.isBefore(round.getClosing());
        if (!lineupUpdateEnabled && seasonService.isSummerBreak()) {
            return true;
        }
        boolean isCurrentRound;
        try {
            isCurrentRound = Objects.equals(round.getId(), seasonService.getCurrentRound().getId());
        } catch (Exception e) {
            isCurrentRound = false;
        }
        return isCurrentRound? (!lineupUpdateEnabled && user.isPremium()): !lineupUpdateEnabled;
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public List<UUID> getLeaguesWithoutLineupsInRounds(List<UUID> roundIds, LocalDateTime maxLeagueCreatedAt) throws EntityNotExistException {
        UUID currentSeasonId = seasonService.getCurrentSeason().getId();
        List<String> leagueWithoutLineupsInRounds = lineupRepository.getLeagueWithoutLineupsInRounds(currentSeasonId, roundIds, maxLeagueCreatedAt);
        return leagueWithoutLineupsInRounds.stream().map(UUID::fromString).toList();
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true)
    public List<Pair<UUID, String>> getLineupByUsernameAndLeagueNameAndRoundNumber(String username, String leagueName, String roundNumber, @NotNull Boolean specialRound) throws EntityNotExistException, FormatException {
        return teamAdminHandler.getLineupByUsernameAndLeagueNameAndRoundNumber(username, leagueName, seasonService.getByRoundNumber(Integer.parseInt(roundNumber), specialRound));
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true)
    public Pair<String, List<PlayerDto>> getTeamByUsernameAndLeagueNameAndRoundNumber(String userName, String leagueName, String roundNumber, @NotNull Boolean specialRound) throws EntityNotExistException {
        HmUserProfile user = userProfileService.getByUserNameOrEmailAddress(userName);
        HmRound round = seasonService.getByRoundNumber(Integer.parseInt(roundNumber), specialRound);
        return teamAdminHandler.getTeamByUsernameAndLeagueNameAndRoundNumber(user, leagueName, round, includePlayerScore(user, round));
    }
}
