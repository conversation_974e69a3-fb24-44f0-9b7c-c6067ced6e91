package com.pass.hbl.manager.backend.persistence.service.hm;

import com.pass.hbl.manager.backend.persistence.domain.hm.HmMatchDO;
import com.pass.hbl.manager.backend.persistence.dto.hm.MatchDto;
import com.pass.hbl.manager.backend.persistence.dto.hm.MatchStatus;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmMatch;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmRound;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.exception.InvalidOperationException;
import com.pass.hbl.manager.backend.persistence.mapper.hm.HmMatchMapper;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmMatchRepository;
import com.pass.hbl.manager.backend.persistence.service.AbstractService;
import com.pass.hbl.manager.backend.persistence.util.Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Stream;

import static java.util.Objects.nonNull;

@Slf4j
@Service
@Transactional
public class MatchService extends AbstractService<HmMatch, MatchDto> {

    private final HmMatchRepository repository;

    private final HmMatchMapper matchMapper;

    private final SeasonService seasonService;

    public MatchService(HmMatchRepository repository, HmMatchMapper mapper, @Lazy SeasonService seasonService) {
        super(repository, mapper, HmMatch.class);
        this.repository = repository;
        this.seasonService = seasonService;
        this.matchMapper = mapper;
    }

    public List<HmMatch> getByTimeInterval(LocalDateTime from, LocalDateTime to, UUID seasonId) {
        return repository.findAllByTimeInterval(from, to, seasonId);
    }

    public List<HmMatch> sortMatchesByStartTime(List<HmMatch> matches) {
        List<HmMatch> sortedMatches = new ArrayList<>(matches);
        sortedMatches.sort(Comparator.comparing(HmMatch::getStartTime));
        return sortedMatches;
    }

    @Transactional(readOnly = true)
    public List<HmMatch> getEndedMatchesByClubAndSeason(UUID clubId, UUID seasonId) throws EntityNotExistException {
        UUID currentRoundId = seasonService.getCurrentRound().getId();
        List<HmMatch> allMatchesByClubAndSeason = repository.findAllBySeasonId(seasonId).stream()
                .filter(m -> m.getHome().getId().equals(clubId) || m.getAway().getId().equals(clubId))
                // matches that were not assigned to rounds are ignored
                .filter(m -> nonNull(m.getRound()))
                .filter(m -> m.getMatchStatus().name().equals(MatchStatus.ENDED.name()))
                // match duration is maximum 2 hours
                .filter(m -> LocalDateTime.now().isAfter(m.getStartTime().plusHours(2)))
                .filter(m -> !m.getRound().getId().equals(currentRoundId))
                .toList();
        return sortMatchesByStartTime(allMatchesByClubAndSeason);
    }

    @Transactional(readOnly = true)
    public List<HmMatch> getEndedMatchesByClubAndDateInterval(UUID clubId, LocalDateTime startTime, LocalDateTime endTime) throws EntityNotExistException {
        UUID currentRoundId = seasonService.getCurrentRound().getId();
        boolean includeCurrentRound = seasonService.isSummerBreak();
        List<HmMatch> matches = repository.findAllByTimeIntervalAndClubId(startTime, endTime, clubId).stream()
                // matches that were not assigned to rounds are ignored
                .filter(m -> nonNull(m.getRound()))
                .filter(m -> m.getMatchStatus().name().equals(MatchStatus.ENDED.name()))
                // match duration is maximum 2 hours
                .filter(m -> LocalDateTime.now().isAfter(m.getStartTime().plusHours(2)))
                .filter(m -> includeCurrentRound || !m.getRound().getId().equals(currentRoundId))
                .toList();
        return sortMatchesByStartTime(matches);
    }

    @Transactional(readOnly = true)
    public Optional<HmMatch> getRunningOrUpcomingMatchByClub(UUID clubId) throws EntityNotExistException {
        UUID currentRoundId = seasonService.getCurrentRound().getId();
        List<HmMatch> allMatchesByRoundId = repository.findAllByRoundId(currentRoundId);
        Stream<HmMatch> allMatchesByClub = allMatchesByRoundId.stream()
                .filter(m -> m.getHome().getId().equals(clubId) || m.getAway().getId().equals(clubId));
        List<HmMatch> allMatchesByClubList = allMatchesByClub.toList();
        if (allMatchesByClubList.size() == 1) {
            return Optional.of(allMatchesByClubList.get(0));
        }

        Optional<HmMatch> runningMatchByClub = Util.toStream(allMatchesByClubList)
                // matches that were not assigned to rounds are ignored
                .filter(m -> nonNull(m.getRound()))
                .filter(m -> !(m.getMatchStatus().name().equals(MatchStatus.ENDED.name())))
                // match duration is maximum 2 hours
                .filter(m -> LocalDateTime.now().isBefore(m.getStartTime().plusHours(2)))
                .findFirst();
        if (runningMatchByClub.isEmpty()) {
            List<HmMatch> upcomingMatchesByClubAndSeason = Util.toStream(allMatchesByClubList)
                    // matches that were not assigned to rounds are ignored
                    .filter(m -> nonNull(m.getRound()))
                    .filter(m -> !(m.getMatchStatus().name().equals(MatchStatus.ENDED.name())))
                    .filter(m -> m.getStartTime().isAfter(LocalDateTime.now()))
                    .toList();
            return sortMatchesByStartTime(upcomingMatchesByClubAndSeason).stream().findFirst();
        }
        return runningMatchByClub;
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true)
    public List<HmMatch> getRunningOrUpcomingMatchesInCurrentSeason() throws EntityNotExistException {
        List<HmMatch> allMatchesInSeason = getAllMatchesInCurrentSeason();
        LocalDateTime now = LocalDateTime.now();
        return allMatchesInSeason.stream()
                // Get running match knowing that match duration is maximum 2 hours or upcoming match
                .filter(m -> now.isBefore(m.getStartTime().plusHours(2)) || m.getStartTime().isAfter(now))
                .filter(m -> !(m.getMatchStatus().name().equals(MatchStatus.ENDED.name())))
                .toList();
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true)
    public List<HmMatch> getRunningOrUpcomingMatchesInCurrentRound() throws EntityNotExistException {
        List<HmMatch> allMatchesInRound = getMatchesSortedByStartTime(seasonService.getCurrentRound());
        LocalDateTime now = LocalDateTime.now();
        return allMatchesInRound.stream()
                // Get running match knowing that match duration is maximum 2 hours or upcoming match
                .filter(m -> now.isBefore(m.getStartTime().plusHours(2)) || m.getStartTime().isAfter(now))
                .filter(m -> !(m.getMatchStatus().name().equals(MatchStatus.ENDED.name())))
                .toList();
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true)
    public List<HmMatchDO> getMatchesForDataCoreStreaming(LocalDateTime now, int maxStreamingHours, int minStreamingMinutes) throws EntityNotExistException {
        List<HmMatchDO> allMatchesInCurrentSeason = repository.findAllMatchDos(seasonService.getCurrentSeason().getId());
        return allMatchesInCurrentSeason.stream()
                // Knowing that match duration is maximum 2 hours, get all matches relevant for streaming:
                // Matches where now is in this interval [matchStart - 30 minutes --> matchEnd + 48h]
                .filter(m -> {
                    LocalDateTime matchEnd = m.getStartTime().plusHours(2);
                    return !(now.isBefore(m.getStartTime().minusMinutes(minStreamingMinutes))
                            || now.isAfter(matchEnd.plusHours(maxStreamingHours)));
                })
                .toList();
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true)
    public List<HmMatch> getAllMatchesInCurrentSeason() throws EntityNotExistException {
        UUID seasonId = seasonService.getCurrentSeason().getId();
        List<HmMatch> allMatchesInSeason = repository.findAllBySeasonIdOrderByStartTimeAsc(seasonId);
        return allMatchesInSeason;
    }

    @SuppressWarnings("UnusedReturnValue")
    public HmMatch saveMatch(HmMatch match) {
        return save(match);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true)
    public List<HmMatch> getCurrentMatches() throws EntityNotExistException {
        List<HmMatch> allMatches = getMatchesSortedByStartTime(seasonService.getCurrentRound());
        LocalDateTime now = LocalDateTime.now();
        return allMatches.stream()
                .filter(m -> (now.isEqual(m.getStartTime()) || now.isAfter(m.getStartTime())) &&
                (now.isEqual(m.getStartTime().plusHours(2)) || now.isBefore(m.getStartTime().plusHours(2)))).toList();
    }

    public boolean isMatchRunning() {
        LocalDateTime now = LocalDateTime.now();
        return repository.existsByStartTimeGreaterThanEqualAndStartTimeLessThanEqual(now, now.minusHours(2));
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true)
    public Optional<HmMatch> getFirstMatchByStartTime(LocalDateTime startTime) {
        return repository.findFirstByStartTime(startTime);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true)
    public ZonedDateTime getNearestGameStart(HmRound round, String userId) throws InvalidOperationException {
        List<HmMatch> matches = getMatchesSortedByStartTime(round);
        if (matches.isEmpty()) {
            UUID roundId = round.getId();
            throw new InvalidOperationException("get nearest game start", userId,
                    "round id["+ roundId +"] with empty matches detected. Minimum match count by round = 6");
        }
        List<LocalDateTime> matchTimes = matches.stream().map(HmMatch::getStartTime).toList();
        List<LocalDateTime> uniqueMatchTimes = new ArrayList<>(new LinkedHashSet<>(matchTimes));
        // the index of the search key, if it is contained in the list; otherwise, (-(insertion point) - 1)
        int index = Collections.binarySearch(uniqueMatchTimes, LocalDateTime.now());
        // the current time exactly corresponds to a match time from the list
        if (index >= 0) return ZonedDateTime.of(uniqueMatchTimes.get(index), TimeZone.getDefault().toZoneId());
        int insertPoint = - index - 1;
        // current time is before the first match of the round
        if (insertPoint == 0) {
            //return the first match of the round
            return ZonedDateTime.of(uniqueMatchTimes.get(insertPoint), TimeZone.getDefault().toZoneId());
            // the round doesn't have next matches
        } else if (insertPoint == uniqueMatchTimes.size()) {
            LocalDateTime lastMatchStart = uniqueMatchTimes.get(uniqueMatchTimes.size() - 1);
            LocalDateTime lastMatchEnd = lastMatchStart.plusHours(2);
            if (LocalDateTime.now().isBefore(lastMatchEnd)) return ZonedDateTime.of(lastMatchStart, TimeZone.getDefault().toZoneId());
            return null;
        } else {
            return getCurrentOrNextMatchStart(uniqueMatchTimes, insertPoint);
        }
    }

    private ZonedDateTime getCurrentOrNextMatchStart(List<LocalDateTime> uniqueMatchTimes, int insertPoint) {
        LocalDateTime previousMatchStart = uniqueMatchTimes.get(insertPoint - 1);
        LocalDateTime nextMatchStart = uniqueMatchTimes.get(insertPoint);
        if (LocalDateTime.now().isBefore(previousMatchStart.plusHours(2))) {
            return ZonedDateTime.of(previousMatchStart, TimeZone.getDefault().toZoneId());
        } else {
            return ZonedDateTime.of(nextMatchStart, TimeZone.getDefault().toZoneId());
        }
    }

    public List<HmMatch> getMatchesSortedByStartTime(@NotNull HmRound round) {
        return getMatchesSortedByStartTime(round.getId());
    }

    public List<HmMatch> getMatchesSortedByStartTime(@NotNull UUID roundId) {
        return repository.findAllByRoundIdOrderByStartTimeAsc(roundId);
    }

    public List<MatchDto> getAllByRoundAsDto(UUID roundId) {
        return repository.findAllByRoundIdOrderByStartTimeAsc(roundId).stream().map(matchMapper::mapToDto).toList();
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true)
    public Pair<UUID, UUID> getClubIdsByMatchId(UUID matchId) throws EntityNotExistException, FormatException {
        HmMatch match = getById(matchId);
        // returns a pair of home club (left) and away club (right)
        return Pair.of(match.getHome().getId(), match.getAway().getId());
    }
}
