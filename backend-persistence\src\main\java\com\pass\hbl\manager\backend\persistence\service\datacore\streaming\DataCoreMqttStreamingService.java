package com.pass.hbl.manager.backend.persistence.service.datacore.streaming;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pass.hbl.manager.backend.persistence.config.AbstractHandballManagerConfigurationProperties;
import com.pass.hbl.manager.backend.persistence.domain.datacore.DataCoreIdMappingDo;
import com.pass.hbl.manager.backend.persistence.dto.datacore.DataCoreStreamingAccessResponseDto;
import com.pass.hbl.manager.backend.persistence.dto.datacore.DataCoreStreamingRequestDto;
import com.pass.hbl.manager.backend.persistence.entity.admin.AdminExternalDataMapping;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmMatch;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmDataCoreHpiCalculationRepository;
import com.pass.hbl.manager.backend.persistence.service.admin.ExternalDataMappingService;
import com.pass.hbl.manager.backend.persistence.service.admin.ParameterService;
import com.pass.hbl.manager.backend.persistence.service.datacore.streaming.handlers.rest.LiveMatchPlayByPlayHandler;
import com.pass.hbl.manager.backend.persistence.service.datacore.streaming.handlers.rest.LiveMatchSummaryHandler;
import com.pass.hbl.manager.backend.persistence.service.datacore.streaming.handlers.rest.LivePersonMatchStatisticsHandler;
import com.pass.hbl.manager.backend.persistence.service.hm.MatchService;
import com.pass.hbl.manager.backend.persistence.util.ParameterDefaults;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.core.env.Environment;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

import static com.pass.hbl.manager.backend.persistence.util.Constants.*;
import static com.pass.hbl.manager.backend.persistence.util.Util.isLocalEnvironment;
import static java.util.Collections.emptyList;
import static java.util.Objects.nonNull;

@Service
@Slf4j
@Getter
@Setter
/*
  Manages the live-streaming using the DataCore Api
 */
public class DataCoreMqttStreamingService {

    private final AbstractHandballManagerConfigurationProperties properties;
    private final Environment environment;

    private final MqttListener mqttListener;

    private final MatchService matchService;
    private final ExternalDataMappingService mappingService;
    private final DataCoreMappingService dataCoreMappingService;
    private final ParameterService parameterService;

    private final LiveMatchSummaryHandler liveMatchSummaryHandler;
    private final LivePersonMatchStatisticsHandler livePersonMatchStatisticsHandler;
    private final LiveMatchPlayByPlayHandler liveMatchPlayByPlayHandler;

    private final HmDataCoreHpiCalculationRepository hpiCalculationRepository;

    private final String MATCH_STATUS = "match_status";
    private final String MATCH_STATISTICS= "match_statistics";
    private final String MATCH_PLAY_BY_PLAY = "match_play_by_play";

    private final List<String> relevantTopics = List.of(READ_STREAM_STATUS, READ_STREAM_PLAY_PLAY, READ_STREAM_PLAY_PLAY_CATCHUP, READ_STREAM_STATISTICS, READ_STREAM_STATISTICS_CATCHUP);

    private String url;
    private String clientId;
    private String expirationDate;
    private List<DataCoreStreamingAccessResponseDto.StreamingTopicDto> topics;

    private String organizationId;
    private List<String> fixtureIds = new CopyOnWriteArrayList<>();
    private Map<String, Pair<String, String>> fixtureIdHomeAwayEntityMap = new HashMap<>();

    public DataCoreMqttStreamingService(AbstractHandballManagerConfigurationProperties properties, Environment environment, MqttListener mqttListener, MatchService matchService, ExternalDataMappingService mappingService, DataCoreMappingService dataCoreMappingService, ParameterService parameterService, LiveMatchSummaryHandler liveMatchSummaryHandler, LivePersonMatchStatisticsHandler livePersonMatchStatisticsHandler, LiveMatchPlayByPlayHandler liveMatchPlayByPlayHandler, HmDataCoreHpiCalculationRepository hpiCalculationRepository) {
        this.properties = properties;
        this.environment = environment;
        this.mqttListener = mqttListener;
        this.matchService = matchService;
        this.mappingService = mappingService;
        this.dataCoreMappingService = dataCoreMappingService;
        this.parameterService = parameterService;
        this.liveMatchSummaryHandler = liveMatchSummaryHandler;
        this.livePersonMatchStatisticsHandler = livePersonMatchStatisticsHandler;
        this.liveMatchPlayByPlayHandler = liveMatchPlayByPlayHandler;
        this.hpiCalculationRepository = hpiCalculationRepository;
    }

    public List<String> initializeSubscriptions(boolean mqttActive) {
        List<String> fixtureIds = new ArrayList<>();

        try {
            // initialise the datacore mapping data
            //TODO HBLMAN-565 not needed in the final version
            dataCoreMappingService.init();

            // get the list of all running or upcoming matches
            Set<UUID> runningOrUpcomingMatchIds = matchService.getRunningOrUpcomingMatchesInCurrentRound().stream().map(HmMatch::getId).collect(Collectors.toSet());

            // get the list of mappings between DataCore fixture-Id and SportRadar-ID
            List<DataCoreIdMappingDo> dcToSrIdsAllMatches = dataCoreMappingService.getDcToSrIdsAllMatches();

            // get the list of mappings between HM-Id and SportRadar-ID
            List<AdminExternalDataMapping> srToHmIdsAllMatches = dataCoreMappingService.getSrToHmIdsAllMatches();

            // filter the relevant target matches to be subscribed to
            List<AdminExternalDataMapping> srToHmIdsByMatches = srToHmIdsAllMatches.stream().filter(externalDataMapping -> runningOrUpcomingMatchIds.contains(externalDataMapping.getHmId())).toList();
            srToHmIdsByMatches.forEach(externalDataMapping -> {
                String sportRadarId = externalDataMapping.getSourceId();
                Optional<DataCoreIdMappingDo> dataCoreIdMappingDoOptional = dcToSrIdsAllMatches.stream().filter(dataCoreIdMappingDo -> nonNull(dataCoreIdMappingDo.getSportRadarId())
                        && dataCoreIdMappingDo.getSportRadarId().equalsIgnoreCase(sportRadarId)).findFirst();
                if (dataCoreIdMappingDoOptional.isPresent()) {
                    String fixtureId = dataCoreIdMappingDoOptional.get().getDataCoreId();
                    if (mqttActive) {
                        connectAndSubscribeToTopics(false, fixtureId);
                    }
                    fixtureIds.add(fixtureId);
                } else {
                    log.info("DataCore Mapping not found for externalDataMapping with sourceId =" + externalDataMapping.getSourceId() + " and  hmId=" + externalDataMapping.getHmId());
                }
            });

        } catch (Exception e) {
            log.error("initializeMqttSubscriptions failed. Skipping ..", e);
        }
        return fixtureIds;
    }

    private synchronized void connectAndSubscribeToTopics(boolean refresh, String fixtureId) {
        List<String> activeProfiles = List.of(environment.getActiveProfiles());
        if (isLocalEnvironment(environment.getActiveProfiles())) {
            log.info("connectAndSubscribeToTopics: connectToMqttBroker for fixtureId [" + fixtureId + "] disabled for local environment");
            return;
        }

        try {
            DataCoreStreamingAccessResponseDto.DataObject accessTokenData = getDataCoreStreamingAccess(activeProfiles, properties, fixtureId);
            if (Objects.nonNull(accessTokenData)) {
                String url = accessTokenData.getUrl();
                String clientId = accessTokenData.getClientId();
                String expirationDate = accessTokenData.getExpiry();
                List<DataCoreStreamingAccessResponseDto.StreamingTopicDto> topics = accessTokenData.getTopics();
                String template = refresh ? "refreshed" : "initialized";
                log.info("Access data for DataCore Streaming API with fixtureId [" + fixtureId + "] successfully " + template + ". token = " + url + ", clientId = " + clientId + ", expiration date = " + expirationDate);
                List<DataCoreStreamingAccessResponseDto.StreamingTopicDto> topicData = topics.stream().filter(topic -> nonNull(topic) && relevantTopics.contains(topic.getScope())).toList();
                List<String> topicNames = topics.stream().filter(topic -> nonNull(topic) && relevantTopics.contains(topic.getScope())).map(DataCoreStreamingAccessResponseDto.StreamingTopicDto::getTopic).toList();

                log.info("Topics for DataCore Streaming API with fixtureId [" + fixtureId + "] are: " + topicNames);
                // Connecting to broker skipped for local environment
                if (!isLocalEnvironment(environment.getActiveProfiles())) {
                    mqttListener.connectToMqttBroker(url, clientId, topicData, fixtureId);
                } else {
                    log.info("connectAndSubscribeToTopics: connectToMqttBroker disabled for local environment");
                }
            }
        } catch (Exception e) {
            String template = refresh ? "Refreshing" : "Initialization";
            log.error(template + " of DataCore Access Token failed. Skipping..", e);
        }
    }

    private DataCoreStreamingAccessResponseDto.DataObject getDataCoreStreamingAccess(List<String> activeProfiles, AbstractHandballManagerConfigurationProperties properties, String fixtureId) throws JsonProcessingException {
        String sport = ParameterDefaults.DEFAULT_DATACORE_SPORT;
        String authUrl;
        String clientId;
        String clientSecret;

        //List<String> scopes = List.of("read:stream_status", "read:stream_events", "read:stream_play_by_play", "read:stream_statistics");
        List<String> scopes = emptyList();
        DataCoreStreamingRequestDto dataCoreStreamingRequestDto;

        if (activeProfiles.contains(PROD_PROFILE) || activeProfiles.contains(PROD2_PROFILE) || activeProfiles.contains(PROD3_PROFILE)
                || activeProfiles.contains(PROD4_PROFILE) || activeProfiles.contains(PROD5_PROFILE)) {
            authUrl = properties.getDataCore().getProdStreamingAuthUrl();
            //TODO HBLMAN-565 prod credentials should be included here
            clientId = properties.getDataCore().getProdClientId();
            clientSecret = properties.getDataCore().getProdClientSecret();
            // User the non-prod URL for Local & DEV environment
        } else {
            authUrl = getProperties().getDataCore().getStagingStreamingAuthUrl();
            clientId = getProperties().getDataCore().getStagingClientId();
            clientSecret = getProperties().getDataCore().getStagingClientSecret();
        }
        dataCoreStreamingRequestDto = new DataCoreStreamingRequestDto(clientId, clientSecret, fixtureId, sport, scopes);

        DataCoreStreamingAccessResponseDto streamingAccess = getDataCoreStreamingAccessCall(authUrl, dataCoreStreamingRequestDto);
        return streamingAccess.getData();
    }

    private DataCoreStreamingAccessResponseDto getDataCoreStreamingAccessCall(String authUrl, DataCoreStreamingRequestDto dataCoreTokenRequestDto) throws JsonProcessingException {
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(List.of(MediaType.APPLICATION_JSON));
        String jsonBody = new ObjectMapper().writeValueAsString(dataCoreTokenRequestDto);
        HttpEntity<String> entity = new HttpEntity<>(jsonBody, headers);
        ResponseEntity<DataCoreStreamingAccessResponseDto> response = restTemplate.exchange(authUrl, HttpMethod.POST, entity, DataCoreStreamingAccessResponseDto.class);
        return response.getBody();
    }
}
