package com.pass.hbl.manager.backend.persistence.service.hm.handlers;

import com.pass.hbl.manager.backend.persistence.domain.admin.AdminPlayerInfoDO;
import com.pass.hbl.manager.backend.persistence.dto.hm.PlayerOfMonthDto;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmPlayer;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmPlayerMarketValue;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.exception.InvalidOperationException;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmPlayerMarketValueRepository;
import com.pass.hbl.manager.backend.persistence.service.admin.LogMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
@Transactional
/*
  Handles player operations performed by the administrator (system)
 */
public class PlayerAdminHandler {

    private final PlayerScoreHandler playerScoreHandler;
    private final PlayerOfMonthHandler playerOfMonthHandler;

    private final HmPlayerMarketValueRepository playerMarketValueRepository;

    private final LogMessageService logMessageService;

    public PlayerAdminHandler(@Lazy PlayerScoreHandler playerScoreHandler, @Lazy PlayerOfMonthHandler playerOfMonthHandler, @Lazy HmPlayerMarketValueRepository playerMarketValueRepository, @Lazy LogMessageService logMessageService) {
        this.playerScoreHandler = playerScoreHandler;
        this.playerOfMonthHandler = playerOfMonthHandler;
        this.playerMarketValueRepository = playerMarketValueRepository;
        this.logMessageService = logMessageService;
    }

    public PlayerOfMonthDto setPlayerOfMonth(HmPlayer player, @NotNull Integer month, Integer totalScore) throws EntityNotExistException {
        // set the player of month over all positions
        return playerOfMonthHandler.setPlayerOfMonth(player, month, totalScore);
    }

    public List<AdminPlayerInfoDO> getAllAdminPlayerInfoDOs(List<HmPlayer> players) {
        //return players.stream().map(p -> p.getFirstName() +";" + p.getLastName() + "; id=" + p.getId()).toList();
        return players.stream().map(p -> new AdminPlayerInfoDO(p.getId(), p.getFirstName(), p.getLastName(), p.getPosition(), p.getMarketValue(), p.getActive())).toList();
    }

    public String getAllPlayerScoresByRound(UUID roundId) {
        return playerScoreHandler.getAllPlayerScoresByRound(roundId);
    }

    public void addPlayerMarketValue(HmPlayer player, int currentMarketValue, String currentRole) throws InvalidOperationException {
        UUID playerId = player.getId();
        if (playerMarketValueRepository.existsByPlayerIdAndCurrentValue(playerId, true)) {
            throw new InvalidOperationException("addPlayerMarketValue", currentRole, "Player id [" + playerId + "] has already a current player market value");
        }
        playerMarketValueRepository.save(new HmPlayerMarketValue(player, LocalDateTime.of(LocalDate.now(), LocalTime.MIN), currentMarketValue, null, true));
        String message = "Current market value [" + currentMarketValue + "] was added to player [" + playerId + "] by Admin with Role [" + currentRole + "]";
        log.info(message);
        logMessageService.logInfo("addPlayerMarketValue", message);
    }
}
