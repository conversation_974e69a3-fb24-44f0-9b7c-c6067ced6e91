package com.pass.hbl.manager.backend.persistence.service.datacore.streaming.handlers.rest;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.pass.hbl.manager.backend.persistence.domain.datacore.FixturePbpEventModelDo;
import com.pass.hbl.manager.backend.persistence.entity.datacore.DcLiveMatchPlayByPlay;
import com.pass.hbl.manager.backend.persistence.mapper.datacore.live.DoToDcFixturePbpEventModelMapper;
import com.pass.hbl.manager.backend.persistence.mapper.datacore.live.FixturePbpEventModelMapper;
import com.pass.hbl.manager.backend.persistence.service.datacore.masterdata.DataCoreTokenHandler;
import com.pass.hbl.manager.backend.persistence.service.datacore.streaming.handlers.DefaultLivePlayByPlayHandler;
import com.pass.hbl.manager.backend.persistence.util.DataCoreUtils;
import com.sportradar.datacore.rest.ApiException;
import com.sportradar.datacore.rest.api.MatchPlayByPlayApi;
import com.sportradar.datacore.rest.model.FixturePbpEventModel;
import com.sportradar.datacore.rest.model.FixturePbpEventResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

import static com.pass.hbl.manager.backend.persistence.mapper.datacore.live.DoToDcLiveMatchSummaryMapper.DC_HOME_CLUB_ID_PAIR;
import static com.pass.hbl.manager.backend.persistence.util.Constants.API_EXCEPTION_CODE;
import static com.pass.hbl.manager.backend.persistence.util.Constants.X_RATE_LIMIT_RESET;
import static java.util.Objects.nonNull;

@Service
@Transactional
@Slf4j
/*
 Handles the live match play by play data
 */
public class LiveMatchPlayByPlayHandler {

    private final MatchPlayByPlayApi matchPlayByPlayApi;

    private final DataCoreTokenHandler dataCoreTokenHandler;
    private final DefaultLivePlayByPlayHandler defaultLivePlayByPlayHandler;

    private final ObjectMapper objectMapper = DataCoreUtils.getStreamingObjectMapper();

    private final FixturePbpEventModelMapper fixturePbpEventModelMapper;
    private final DoToDcFixturePbpEventModelMapper doToDcFixturePbpEventModelMapper;

    private final Map<String, LocalDateTime> rateLimitResetTimeByFixture = new ConcurrentHashMap<>();

    public LiveMatchPlayByPlayHandler(@Lazy MatchPlayByPlayApi matchPlayByPlayApi, @Lazy DataCoreTokenHandler dataCoreTokenHandler, DefaultLivePlayByPlayHandler defaultLivePlayByPlayHandler, FixturePbpEventModelMapper fixturePbpEventModelMapper, DoToDcFixturePbpEventModelMapper doToDcFixturePbpEventModelMapper) {
        this.matchPlayByPlayApi = matchPlayByPlayApi;
        this.dataCoreTokenHandler = dataCoreTokenHandler;
        this.defaultLivePlayByPlayHandler = defaultLivePlayByPlayHandler;
        this.fixturePbpEventModelMapper = fixturePbpEventModelMapper;
        this.doToDcFixturePbpEventModelMapper = doToDcFixturePbpEventModelMapper;
    }

    public void updateLiveMatchPlayByPlay(String fixtureId, String organizationId, List<String> relevantEventTypes, Pair<String, String> homeAwayClubIdPair) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime recentRateLimitResetTime = rateLimitResetTimeByFixture.get(fixtureId);
        if (nonNull(recentRateLimitResetTime) && (now.isEqual(recentRateLimitResetTime) || now.isBefore(recentRateLimitResetTime))) {
            log.info("updateLiveMatchPlayByPlay: fixturePbpListLive endpoint for fixtureId [" + fixtureId
                    + "] skipped. Reason now[" + now + "] is before or equal rateLimitResetTime[" + recentRateLimitResetTime + "]");
            return;
        }

        boolean isAccessTokenValid = checkDataCoreTokenValidity();
        log.info("updateLiveMatchPlayByPlay: Ensure that access token exist and valid: isAccessTokenValid = [" + isAccessTokenValid + "]");

        if (!isAccessTokenValid) {
            String token = dataCoreTokenHandler.getToken();
            matchPlayByPlayApi.getApiClient().setAccessToken(token);
            log.info("updateLiveMatchPlayByPlay: New DataCore access token = " + token + " set for updateLiveMatchPlayByPlay");
        }
        FixturePbpEventResponse fixturePbpEventResponse;
        log.info("updateLiveMatchPlayByPlay: fixturePbpListLive endpoint for fixtureId [" + fixtureId + "] call started");
        try {
            fixturePbpEventResponse = matchPlayByPlayApi.fixturePbpListLive(UUID.fromString(fixtureId), organizationId,
                    null, null, null, true, null, 2000, 0,
                    null, null);
            log.info("updateLiveMatchPlayByPlay: fixturePbpListLive endpoint for fixtureId [" + fixtureId + "] called successfully");
        } catch (ApiException e) {
            handleApiException(fixtureId, e);
            return;
        }

        List<FixturePbpEventModel> data = fixturePbpEventResponse.getData();
        if (nonNull(data)) {
            log.info("updateLiveMatchPlayByPlay: count = [" + data.size() + "] data received from fixturePbpList Call of fixture-Id [" + fixtureId + "]");
            List<DcLiveMatchPlayByPlay> dcLivePlayByPlayDos = new ArrayList<>();

            //filter only sport events with relevant event types
            List<FixturePbpEventModel> sportEvents = data.stream().filter(pbpEventModel ->
            {
                FixturePbpEventModel.PropertyClassEnum propertyClass = pbpEventModel.getPropertyClass();
                boolean isSportEvent = nonNull(propertyClass) && Objects.equals(propertyClass.getValue(), "sport");
                boolean isRelevantEvent = nonNull(pbpEventModel.getEventType()) && relevantEventTypes.contains(pbpEventModel.getEventType());
                return isSportEvent && isRelevantEvent;
            }).toList();
            AtomicInteger pbpWithoutPersonIdCounter = new AtomicInteger();
            sportEvents.forEach(sportEvent -> {
                try {
                    // map to domain objects
                    FixturePbpEventModelDo pbpEventModelDo = fixturePbpEventModelMapper.mapToDto(sportEvent);
                    if (nonNull(pbpEventModelDo)) {
                        FixturePbpEventModelDo.Options options = null;
                        if (nonNull(sportEvent.getOptions())) {
                            options = objectMapper.readValue(objectMapper.writeValueAsString(sportEvent.getOptions()), FixturePbpEventModelDo.Options.class);
                        }
                        pbpEventModelDo.setOptions(options);
                        // map domain object to entity
                        Map<String, Object> mappingContext = nonNull(homeAwayClubIdPair) ? Map.of(DC_HOME_CLUB_ID_PAIR, homeAwayClubIdPair) : Collections.emptyMap();
                        DcLiveMatchPlayByPlay dcLiveMatchPlayByPlay = doToDcFixturePbpEventModelMapper.mapToDto(pbpEventModelDo, mappingContext);
                        if (nonNull(dcLiveMatchPlayByPlay)) {
                            String playerId = dcLiveMatchPlayByPlay.getPlayerId();
                            if (nonNull(playerId) && !Objects.equals(playerId, "")) {
                                // Explicitly set the id since every DataCore entity should have its ID not automatically generated,
                                // in this case the play by play doesn't have an ID
                                dcLiveMatchPlayByPlay.setId(UUID.randomUUID().toString());
                                dcLiveMatchPlayByPlay.setMatchId(fixtureId);
                                dcLivePlayByPlayDos.add(dcLiveMatchPlayByPlay);
                            } else {
                                pbpWithoutPersonIdCounter.getAndIncrement();
                                log.info("updateLiveMatchPlayByPlay: playerId is null in " + dcLiveMatchPlayByPlay + " domain object in fixture-Id [" + fixtureId + "]. Skipping ..");
                            }
                        } else {
                            log.info("updateLiveMatchPlayByPlay: null DcLiveMatchPlayByPlay domain object in fixture-Id [" + fixtureId + "] found. Skipping ..");
                        }
                    }
                } catch (Exception e) {
                    log.error("updateLiveMatchPlayByPlay: get FixturePbpEventModelDos failed. Skipping ..", e);
                }
            });
            int countPbpWithoutPersonId = pbpWithoutPersonIdCounter.get();
            if (countPbpWithoutPersonId > 0) {
                log.info("updateLiveMatchPlayByPlay: count [" + countPbpWithoutPersonId + "/" + sportEvents.size() + "] sport events with relevant types missing personId by fixture-Id [" + fixtureId + "]");
            }
            log.info("updateLiveMatchPlayByPlay: [" + sportEvents.size() + "] FixturePbpEventModels of fixture-Id [" + fixtureId + "] mapped to [" + dcLivePlayByPlayDos.size() + "] FixturePbpEventModelDos domain objects!");
            defaultLivePlayByPlayHandler.updateLiveMatchPlayByPlay(fixtureId, dcLivePlayByPlayDos);
        }
        log.info("updateLiveMatchPlayByPlay: finished for fixture [" + fixtureId + "]");
    }

    private void handleApiException(String fixtureId, ApiException e) {
        List<String> strings = e.getResponseHeaders().get(X_RATE_LIMIT_RESET);
        LocalDateTime rateLimitResetTime = null;
        String rateLimitResetEpoc = null;
        if (nonNull(strings) && !strings.isEmpty()) {
            rateLimitResetEpoc = strings.get(0);
            rateLimitResetTime = LocalDateTime.ofInstant(Instant.ofEpochSecond(Long.parseLong(rateLimitResetEpoc)), ZoneId.systemDefault());
        }
        if (e.getCode() == API_EXCEPTION_CODE && nonNull(rateLimitResetTime)) {
            rateLimitResetTimeByFixture.put(fixtureId, rateLimitResetTime);
        }
        log.error("updateLiveMatchPlayByPlay: fixturePbpListLive endpoint for fixtureId [" + fixtureId + "] failed: " + e.getResponseBody() + ", rateLimitResetAsEpoch: " + rateLimitResetEpoc + " rateLimitResetTime: " + rateLimitResetTime);
    }

    public boolean checkDataCoreTokenValidity() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime expirationDate = dataCoreTokenHandler.getExpirationDate();
        String token = dataCoreTokenHandler.getToken();
        if (nonNull(expirationDate) && nonNull(token)) {
            if (expirationDate.isAfter(now)) {
                log.info("DataCore token is still valid expiration date = [" + expirationDate + "] is after now = [" + now + "]");
                return true;
            } else {
                dataCoreTokenHandler.updateDataCoreTokenInfo(true);
                log.info("DataCore token is expired expiration date = [" + expirationDate + "] is before or equal now = [" + now + "]");
                return false;
            }
        } else {
            dataCoreTokenHandler.updateDataCoreTokenInfo(true);
            log.info("checkAccessToken: token expiration date not exist. Datacore token updated");
            return false;
        }
    }
}
