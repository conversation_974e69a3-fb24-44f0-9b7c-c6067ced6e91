package com.pass.hbl.manager.backend.admin.controller;

import com.pass.hbl.manager.backend.admin.util.ApiConstants;
import com.pass.hbl.manager.backend.persistence.domain.admin.AdminUsersStatisticsDO;
import com.pass.hbl.manager.backend.persistence.dto.hm.UserProfileDto;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmUserProfile;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.service.hm.UserProfileService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.security.RolesAllowed;
import javax.validation.constraints.NotNull;
import java.util.List;

import static com.pass.hbl.manager.backend.admin.util.ApiConstants.ROLE_ADMIN;
import static com.pass.hbl.manager.backend.admin.util.ApiConstants.ROLE_ADMIN_WRITE;

@RestController
@RequestMapping(ApiConstants.USER_API)
@Validated
@Tag(name = "user", description = "Admin API for user management")
public class UserController {

    private final UserProfileService service;

    public UserController(UserProfileService service) {
        this.service = service;
    }

    @Operation(summary = "Get all available usernames")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "All available usernames found", content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = List.class))}),
            @ApiResponse(responseCode = "404", description = "Available usernames could not be found")
    })
    @RolesAllowed({ROLE_ADMIN, ROLE_ADMIN_WRITE})
    @GetMapping(value = "/username/all", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<String> getAllUsernames() {
        return service.getAll().stream().map(HmUserProfile::getUsername).toList();
    }


    @Operation(summary = "Get user by name or email address")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Found user", content = { @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = UserProfileDto.class))}),
            @ApiResponse(responseCode = "404", description = "User not found", content = { @Content})
    })
    @RolesAllowed({ROLE_ADMIN, ROLE_ADMIN_WRITE})
    @GetMapping(value = "/{user}", produces = MediaType.APPLICATION_JSON_VALUE)
    public UserProfileDto getUser(@Parameter(name = "user") @PathVariable(name = "user") @NotNull String usernameOrEmailAddress) throws EntityNotExistException {
        return service.getByUserDtoByNameOrEmailAddress(usernameOrEmailAddress);
    }


    @Operation(summary = "Get user current app version by name or email address")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Found user", content = { @Content(mediaType = MediaType.TEXT_PLAIN_VALUE, schema = @Schema(implementation = String.class))}),
            @ApiResponse(responseCode = "404", description = "User not found", content = { @Content})
    })
    @RolesAllowed({ROLE_ADMIN, ROLE_ADMIN_WRITE})
    @GetMapping(value = "/{user}/appVersion", produces = MediaType.TEXT_PLAIN_VALUE)
    public String getUserAppVersion(@Parameter(name = "user") @PathVariable(name = "user") @NotNull String usernameOrEmailAddress) throws EntityNotExistException {
        return service.getByUserNameOrEmailAddress(usernameOrEmailAddress).getAppVersion();
    }

    @Operation(summary = "Get statistics of premium subscriptions")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Normal behavior", content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = List.class))}),
            @ApiResponse(responseCode = "404", description = "Error case, contact support")
    })
    @RolesAllowed({ROLE_ADMIN, ROLE_ADMIN_WRITE})
    @GetMapping(value = "/statistics", produces = MediaType.APPLICATION_JSON_VALUE)
    public AdminUsersStatisticsDO getUserStatistics() {
        return service.getUserStatistics();
    }

    /* =================================================================================================================
     * Write endpoints
     * ============================================================================================================== */

    @Operation(summary = "Set a system profile image")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "The system user profile including the image"),
            @ApiResponse(responseCode = "404", description = "Exception case")
    })
    @RolesAllowed(ROLE_ADMIN_WRITE)
    @PostMapping(value = "/picture", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public void uploadPicture(@Parameter(name = "file", description = "the image itself") @RequestPart MultipartFile file)
            throws Exception {
        service.setSystemPicture(file);
    }


    @Operation(summary = "Delete user by email address")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Normal behavior", content = { @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = UserProfileDto.class))}),
            @ApiResponse(responseCode = "400", description = "User already exists", content = { @Content}),
            @ApiResponse(responseCode = "403", description = "Authorization denied", content = { @Content}),
            @ApiResponse(responseCode = "404", description = "Error case, contact support", content = { @Content})
    })
    @RolesAllowed(ROLE_ADMIN_WRITE)
    @DeleteMapping(value = "/{email}/delete")
    public void deleteCurrentUser(@Parameter(name = "email") @PathVariable(name = "email")
                                      @NotNull String emailAddress) throws EntityNotExistException {
        service.deleteByEmailAddress(emailAddress);
    }
}
