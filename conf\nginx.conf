worker_processes 4;

events {
    worker_connections 8192;
}

http {
    client_max_body_size    100M;

    include                 mime.types;
    default_type            application/octet-stream;

    fastcgi_read_timeout    600;
    proxy_read_timeout      600;
    proxy_http_version      1.1;
    resolver                127.0.0.11;

    chunked_transfer_encoding off;
    proxy_set_header Connection '';
    proxy_buffering off;
    proxy_cache off;

    proxy_set_header    Host               $host;
    proxy_set_header    X-Real-IP          $remote_addr;
    proxy_set_header    X-Forwarded-For    $proxy_add_x_forwarded_for;
    proxy_set_header    X-Forwarded-Host   $host;
    proxy_set_header    X-Forwarded-Server $host;
    proxy_set_header    X-Forwarded-Port   $server_port;
    proxy_set_header    X-Forwarded-Proto  $scheme;

    error_page   500 502 503 504  /50x.html;

    server {
        listen 80;
        listen  [::]:80;
        server_name _;

        location = /50x.html {
            root /usr/share/nginx/html;
            internal;
        }

        location /api/swagger-ui {
            deny all;
        }

        location /api {
            proxy_pass http://api:8081/api;
        }

        location / {
            default_type text/html;
            return 200 "<!DOCTYPE html><html><body><p>ok</p></body></html>";
        }
    }

    server {
        listen 443 ssl http2;
        listen  [::]:443 ssl http2;
        server_name _;

        ssl_certificate      /etc/ssl/certs/STAR_pass-consulting_com.nginx.crt;
        ssl_certificate_key  /etc/ssl/certs/STAR_pass-consulting_com.key;

        location = /50x.html {
            root /usr/share/nginx/html;
            internal;
        }

        location /api/swagger-ui {
            deny all;
        }

        location /api {
            proxy_pass http://api:8081/api;
        }

        location / {
            default_type text/html;
            return 200 "<!DOCTYPE html><html><body><p>ok</p></body></html>";
        }
    }

    server {
        listen 4000;
        listen  [::]:4000;
        server_name _;

        location = /50x.html {
            root /usr/share/nginx/html;
            internal;
        }

        location /api {
            proxy_pass       http://api:8081/api;
            proxy_set_header X-Script-Name /api;
        }

        location /apimgmt {
            proxy_pass       http://api:8082/actuator;
            proxy_set_header X-Script-Name /apimgmt;
        }

        location /adminapi {
            proxy_pass       http://admin-api:8181/adminapi;
            proxy_set_header X-Script-Name /adminapi;
        }

        location /adminapimgmt {
            proxy_pass       http://admin-api:8182/actuator;
            proxy_set_header X-Script-Name /adminapimgmt;
        }

#         location /pgadmin {
#             proxy_set_header X-Script-Name /pgadmin;
#             proxy_pass       http://pg-admin:9999;
#             proxy_redirect   off;
#         }
    }
}
