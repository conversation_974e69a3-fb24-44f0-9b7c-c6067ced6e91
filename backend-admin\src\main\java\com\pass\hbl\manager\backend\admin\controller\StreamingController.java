package com.pass.hbl.manager.backend.admin.controller;

import com.pass.hbl.manager.backend.admin.util.ApiConstants;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.exception.InvalidOperationException;
import com.pass.hbl.manager.backend.persistence.service.datacore.streaming.DataCoreRestStreamingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

import static com.pass.hbl.manager.backend.persistence.service.datacore.streaming.DataCoreRestStreamingService.MATCH_PLAY_BY_PLAY;
import static com.pass.hbl.manager.backend.persistence.service.datacore.streaming.DataCoreRestStreamingService.MATCH_STATISTICS;
import static java.util.Collections.emptyList;

@RestController
@RequestMapping(ApiConstants.LIVE_API)
@Validated
@Tag(name = "team", description = "API for admin streaming management")
public class StreamingController {

    private final DataCoreRestStreamingService service;

    public StreamingController(DataCoreRestStreamingService service) {
        this.service = service;
    }

    @Operation(summary = "Adds a match to fixture id list used for datacore live streaming")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Normal behavior", content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = List.class))}),
            @ApiResponse(responseCode = "404", description = "Error case, contact support")
    })
    @PostMapping("/fixture/{fixtureId}/stream")
    //@RolesAllowed({ROLE_SYSTEM, ROLE_ADMIN_WRITE})
    public void streamMatch(@PathVariable String fixtureId, @RequestParam Boolean add) {
        if (add) {
            List<String> fixtureIds = new ArrayList<>(service.getFixtureIds());
            fixtureIds.add(fixtureId);
            service.setFixtureIds(fixtureIds);
        } else {
            service.setFixtureIds(List.of(fixtureId));
        }
    }

    @Operation(summary = "Clear the list of datacore fixtures used for live streaming")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Normal behavior", content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = List.class))}),
            @ApiResponse(responseCode = "404", description = "Error case, contact support")
    })
    @PostMapping("/fixture/clear")
    //@RolesAllowed({ROLE_SYSTEM, ROLE_ADMIN_WRITE})
    public void clearMatchesStreaming() {
        service.setFixtureIds(emptyList());
    }

    @Operation(summary = "Import play by play data for fixture-Ids")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Normal behavior", content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = List.class))}),
            @ApiResponse(responseCode = "404", description = "Error case, contact support")
    })
    @PostMapping("/fixture/pbp/import")
    //@RolesAllowed({ROLE_SYSTEM, ROLE_ADMIN_WRITE})
    public void streamPlayByPlay(@RequestBody List<String> fixtureIds) throws InvalidOperationException, FormatException {
        service.doUpdateLiveMatchData(fixtureIds, MATCH_PLAY_BY_PLAY, service.getFixtureIdHomeAwayEntityMapCache());
    }

    @Operation(summary = "Import play by play data for fixture-Ids")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Normal behavior", content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = List.class))}),
            @ApiResponse(responseCode = "404", description = "Error case, contact support")
    })
    @PostMapping("/fixture/statistics/import")
    //@RolesAllowed({ROLE_SYSTEM, ROLE_ADMIN_WRITE})
    public void streamStatistics(@RequestBody List<String> fixtureIds) throws InvalidOperationException, FormatException {
        service.doUpdateLiveMatchData(fixtureIds, MATCH_STATISTICS, service.getFixtureIdHomeAwayEntityMapCache());
    }

}
