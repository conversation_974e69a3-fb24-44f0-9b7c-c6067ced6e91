package com.pass.hbl.manager.backend.persistence.service.datacore.streaming;

import com.pass.hbl.manager.backend.persistence.config.AbstractHandballManagerConfigurationProperties;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmDataCoreHpiCalculation;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.exception.InvalidOperationException;
import com.pass.hbl.manager.backend.persistence.mapper.datacore.live.Sr2HmDataCorePlayerMatchEventMapper;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmDataCoreHpiCalculationRepository;
import com.pass.hbl.manager.backend.persistence.service.admin.ExternalDataMappingService;
import com.pass.hbl.manager.backend.persistence.service.admin.ParameterService;
import com.pass.hbl.manager.backend.persistence.service.datacore.streaming.handlers.rest.DataCoreRestStreamingHandler;
import com.pass.hbl.manager.backend.persistence.service.datacore.streaming.handlers.rest.LiveMatchPlayByPlayHandler;
import com.pass.hbl.manager.backend.persistence.service.datacore.streaming.handlers.rest.LiveMatchSummaryHandler;
import com.pass.hbl.manager.backend.persistence.service.datacore.streaming.handlers.rest.LivePersonMatchStatisticsHandler;
import com.pass.hbl.manager.backend.persistence.service.hm.MatchService;
import com.pass.hbl.manager.backend.persistence.util.ParameterDefaults;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

import static com.pass.hbl.manager.backend.persistence.util.Constants.SYSTEM_USERNAME;
import static java.util.Collections.emptyList;
import static java.util.Objects.isNull;
import static java.util.stream.Collectors.toSet;

@Transactional
@Service
@Slf4j
@Getter
@Setter
/*
  Manages the live-streaming using the DataCore Api
 */
@ConditionalOnSchedulerEnabled
public class DataCoreRestStreamingService {

    private final AbstractHandballManagerConfigurationProperties properties;
    private final Environment environment;

    private final MqttListener mqttListener;

    private final MatchService matchService;
    private final ExternalDataMappingService mappingService;
    private final DataCoreMappingService dataCoreMappingService;
    private final ParameterService parameterService;
    private final ExternalDataMappingService externalDataMappingService;

    private final LiveMatchSummaryHandler liveMatchSummaryHandler;
    private final LivePersonMatchStatisticsHandler livePersonMatchStatisticsHandler;
    private final LiveMatchPlayByPlayHandler liveMatchPlayByPlayHandler;
    private final DataCoreRestStreamingHandler dataCoreRestStreamingHandler;

    private final HmDataCoreHpiCalculationRepository hpiCalculationRepository;

    private final Sr2HmDataCorePlayerMatchEventMapper sr2HmDataCorePlayerMatchEventMapper;

    public static final String MATCH_STATUS = "match_status";
    public static final String MATCH_STATISTICS = "match_statistics";
    public static final String MATCH_PLAY_BY_PLAY = "match_play_by_play";

    private String organizationId;

    // TODO HBLMAN-565 replace it with the separate list of fixture Ids rate specific
    private List<String> fixtureIds = new CopyOnWriteArrayList<>();

    @Getter
    private Map<String, Pair<String, String>> fixtureIdHomeAwayEntityMapCache = new ConcurrentHashMap<>();

    // Fixture Ids of matches that are live running [matchStart - 30 Minutes --> matchStart + 2h = matchEnd]
    // Should be updated every 30 seconds
    private List<String> liveRunningFixtureIds = new CopyOnWriteArrayList<>();
    public static int MIN_STREAMING_MINUTES = 10;

    // Fixture Ids of matches that have already ended [matchEnd --> matchEnd + 4h]
    // Should be updated with a rate of thirty minutes
    private List<String> thirtyMinutesRateFixtureIds = new CopyOnWriteArrayList<>();
    public static int THIRTY_MINUTES_STREAMING_HOURS = 4;

    // Fixture Ids of matches that have already ended [matchEnd --> matchEnd + 48h]
    // Should be updated with a rate of one hour
    private List<String> oneHourRateFixtureIds = new CopyOnWriteArrayList<>();
    public static int MAX_STREAMING_HOURS = 48;

    private final ConcurrentHashMap<String, String> hmId2FixtureIdCache = new ConcurrentHashMap<>();

    // flag used to active streaming only on the admin service, since the persistence-service is used
    // from both api & admin-service
    private boolean streamingActive = false;

    private List<String> relevantEventTypes = new ArrayList<>();

    public DataCoreRestStreamingService(AbstractHandballManagerConfigurationProperties properties, Environment environment, MqttListener mqttListener, MatchService matchService, ExternalDataMappingService mappingService, DataCoreMappingService dataCoreMappingService, ParameterService parameterService, @Lazy ExternalDataMappingService externalDataMappingService, LiveMatchSummaryHandler liveMatchSummaryHandler, LivePersonMatchStatisticsHandler livePersonMatchStatisticsHandler, LiveMatchPlayByPlayHandler liveMatchPlayByPlayHandler, DataCoreRestStreamingHandler dataCoreRestStreamingHandler, HmDataCoreHpiCalculationRepository hpiCalculationRepository, @Lazy Sr2HmDataCorePlayerMatchEventMapper sr2HmDataCorePlayerMatchEventMapper) {
        this.properties = properties;
        this.environment = environment;
        this.mqttListener = mqttListener;
        this.matchService = matchService;
        this.mappingService = mappingService;
        this.dataCoreMappingService = dataCoreMappingService;
        this.parameterService = parameterService;
        this.externalDataMappingService = externalDataMappingService;
        this.liveMatchSummaryHandler = liveMatchSummaryHandler;
        this.livePersonMatchStatisticsHandler = livePersonMatchStatisticsHandler;
        this.liveMatchPlayByPlayHandler = liveMatchPlayByPlayHandler;
        this.dataCoreRestStreamingHandler = dataCoreRestStreamingHandler;
        this.hpiCalculationRepository = hpiCalculationRepository;
        this.sr2HmDataCorePlayerMatchEventMapper = sr2HmDataCorePlayerMatchEventMapper;
    }

    public void initializeDatCoreStreaming() throws InvalidOperationException, FormatException, EntityNotExistException {
        boolean isMqttStreamingActive = isStreamingActive();
        if (!isMqttStreamingActive) {
            organizationId = getParameterService().getValue(ParameterDefaults.PARAM_DATACORE_ORGANIZATION_KEY, ParameterDefaults.DEFAULT_DATACORE_ORGANIZATION_KEY, ParameterDefaults.SYSTEM_USER);
            // activate the streaming only in the admin-service
            streamingActive = true;

            relevantEventTypes = sr2HmDataCorePlayerMatchEventMapper.getHpiCalculations().stream().map(HmDataCoreHpiCalculation::getDcEventType).collect(toSet()).stream().toList();
            // TODO HBLMAN-565 Only for the first test, should be uncommented
            //fixtureIds = initializeSubscriptions(isMqttStreamingActive);
            // first test "756397e2-9e83-11ee-a5e8-b5570c7f34b9"
            // second test: fixtureIds = List.of("cc65b3a7-1dd9-11ef-a150-ab04fa67a478", "7603ac68-9e83-11ee-ae9c-b5570c7f34b9");
            // third test: fixtureIds = List.of("76324bb6-9e83-11ee-a86d-b5570c7f34b9");
            // fourth test: fixtureIds = List.of("7711a9c4-9e83-11ee-b2e4-b5570c7f34b9");
            // test Nr.5 fixtureIds = List.of("c3af7aab-28b6-11ef-bfd3-87aae92f2967");
            // test matches with GoalkeeperId: fixtureIds = List.of("e6f088cf-3992-11ef-b34e-437040163a99");
            // 2nd Test Summer break 12.07 fixtureIds = List.of("9f7bc588-3e02-11ef-9499-0f9fed378a94");
            // fixtureIds = List.of("4fd5650f-4067-11ef-8b9b-7d1c174bda53", "835a39ae-4067-11ef-8ef4-37e71b597752");
            //fixtureIds = List.of("18dab348-4505-11ef-9285-c7a76f4265fb", "48b9a1d4-4505-11ef-b855-5dfded8fed71");
            //fixtureIds = List.of("b10dc452-637f-11ef-a61f-2ffd8e37a68d", "38a8e63e-637f-11ef-8bec-e32e00f0cdcb");
            fixtureIds = emptyList();
            // update the frequency rate for each fixture
            updateFixturesIdsFrequencyRate();

            // TODO HBLMAN-565 initialize fixture home-club-Id & away-club-id map, check if relevant in the final version
            updateFixtureIdHomeAwayEntityMap(fixtureIds);
        }
        log.info("initializeDataCoreStreaming: mqttActive = " + isMqttStreamingActive + ". FixtureIds = " + fixtureIds);
    }

    private void updateFixtureIdHomeAwayEntityMap(List<String> fixtureIds) {
        // if the cache is empty update it with all fixture ids (in one query), otherwise check each fixture one by one if it
        // already exists in cache
        if (fixtureIdHomeAwayEntityMapCache.isEmpty()) {
            dataCoreRestStreamingHandler.updateHomeAwayEntityIdMap(fixtureIds, fixtureIdHomeAwayEntityMapCache);
        } else {
            fixtureIds.forEach(fixtureId -> {
                        if (!fixtureIdHomeAwayEntityMapCache.containsKey(fixtureId)) {
                            Optional<Pair<String, String>> homeAwayEntityIdOptional = dataCoreRestStreamingHandler.getHomeAwayEntityId(fixtureId);
                            if (homeAwayEntityIdOptional.isPresent()) {
                                String dcHomeClubId = homeAwayEntityIdOptional.get().getKey();
                                String dcAwayClubId = homeAwayEntityIdOptional.get().getValue();
                                fixtureIdHomeAwayEntityMapCache.put(fixtureId, Pair.of(dcHomeClubId, dcAwayClubId));
                                log.info("FixtureId [" + fixtureId + "]: DataCore home-clubId [" + dcHomeClubId + "], away-ClubId [" + dcAwayClubId + "]");
                            }
                        }
                    }
            );
        }
    }

    private boolean isStreamingActive() throws FormatException, InvalidOperationException {
        return parameterService.getAsBoolean(ParameterDefaults.PARAM_DATACORE_MQTT_STREAMING_ACTIVE, ParameterDefaults.DEFAULT_DATACORE_MQTT_STREAMING_ACTIVE, SYSTEM_USERNAME);
    }

    /* =================================================================================================================
     * Fixture-IDs frequency rate
     * ============================================================================================================== */

    /**
     * Update the frequency rate by Fixture-Id every minute. The relevant matches should be split into three categories:
     * 30 seconds rate streaming, 30 minutes rate streaming, 1 hour rate streaming
     *
     * @throws EntityNotExistException thrown if required is not found
     */
    @Scheduled(fixedRate = 120000)
    protected void updateFixturesIdsFrequencyRate() throws EntityNotExistException {
        // streaming is active only in the admin-service
        if (!streamingActive) {
            log.info("updateFixturesIdsFrequencyRate: streaming is not active. Skipping..");
            return;
        }
        //get the list of fixtures to update the home_away entity map
        Triple<List<String>, List<String>, List<String>> fixtureIdsTriple = dataCoreRestStreamingHandler.updateFixturesIdsFrequencyRate();
        List<String> liveRunningFixtureIds = fixtureIdsTriple.getLeft();
        List<String> allFixtureIds = new ArrayList<>(liveRunningFixtureIds);
        if (!liveRunningFixtureIds.isEmpty()) {
            this.liveRunningFixtureIds = liveRunningFixtureIds;
        } else {
            this.liveRunningFixtureIds = emptyList();
        }
        List<String> thirtyMinutesRateFixtureIds = fixtureIdsTriple.getMiddle();
        if (!thirtyMinutesRateFixtureIds.isEmpty()) {
            this.thirtyMinutesRateFixtureIds = thirtyMinutesRateFixtureIds;
            allFixtureIds.addAll(thirtyMinutesRateFixtureIds);
        } else {
            this.thirtyMinutesRateFixtureIds = emptyList();
        }
        List<String> oneHourRateFixtureIds = fixtureIdsTriple.getRight();
        if (!oneHourRateFixtureIds.isEmpty()) {
            this.oneHourRateFixtureIds = oneHourRateFixtureIds;
            allFixtureIds.addAll(oneHourRateFixtureIds);
        } else {
            this.oneHourRateFixtureIds = emptyList();
        }
        updateFixtureIdHomeAwayEntityMap(allFixtureIds);
    }

    /* =================================================================================================================
     * Live match data update methods
     * ============================================================================================================== */

    @Scheduled(fixedRate = 40000)
    public void updateMatchLiveStatus() throws InvalidOperationException, FormatException {
        //doUpdateLiveMatchData(fixtureIds, MATCH_STATUS, fixtureIdHomeAwayEntityMapCache);
        doUpdateLiveMatchData(liveRunningFixtureIds, MATCH_STATUS, fixtureIdHomeAwayEntityMapCache);
    }

    @Scheduled(fixedRate = 40000)
    public void updateMatchLiveEvents() throws InvalidOperationException, FormatException {
        //doUpdateLiveMatchData(fixtureIds, MATCH_PLAY_BY_PLAY, fixtureIdHomeAwayEntityMapCache);
        doUpdateLiveMatchData(liveRunningFixtureIds, MATCH_PLAY_BY_PLAY, fixtureIdHomeAwayEntityMapCache);
    }

    @Scheduled(fixedRate = 45000)
    public void updateMatchLiveStatistics() throws InvalidOperationException, FormatException {
        // Schedule explicitly set after live events in order to calculate the total score. Should be after commit of the events
        //doUpdateLiveMatchData(fixtureIds, MATCH_STATISTICS, fixtureIdHomeAwayEntityMapCache);
        doUpdateLiveMatchData(liveRunningFixtureIds, MATCH_STATISTICS, fixtureIdHomeAwayEntityMapCache);
    }

    @Scheduled(cron = "0 0/30 * * * ?")
    public void updateMatchLiveStatusRate2() throws InvalidOperationException, FormatException {
        doUpdateLiveMatchData(thirtyMinutesRateFixtureIds, MATCH_STATUS, fixtureIdHomeAwayEntityMapCache);
    }

    @Scheduled(cron = "0 0/30 * * * ?")
    public void updateMatchLiveEventsRate2() throws InvalidOperationException, FormatException {
        doUpdateLiveMatchData(thirtyMinutesRateFixtureIds, MATCH_PLAY_BY_PLAY, fixtureIdHomeAwayEntityMapCache);
    }

    @Scheduled(cron = "5 0/30 * * * ?")
    public void updateMatchLiveStatisticsRate2() throws InvalidOperationException, FormatException {
        // Schedule explicitly set after live events in order to calculate the total score. Should be after commit of the events
        doUpdateLiveMatchData(thirtyMinutesRateFixtureIds, MATCH_STATISTICS, fixtureIdHomeAwayEntityMapCache);
    }

    @Scheduled(cron = "0 0 * * * ?")
    public void updateMatchLiveStatusRate3() throws InvalidOperationException, FormatException {
        doUpdateLiveMatchData(oneHourRateFixtureIds, MATCH_STATUS, fixtureIdHomeAwayEntityMapCache);
    }

    @Scheduled(cron = "0 0 * * * ?")
    public void updateMatchLiveEventsRate3() throws InvalidOperationException, FormatException {
        doUpdateLiveMatchData(oneHourRateFixtureIds, MATCH_PLAY_BY_PLAY, fixtureIdHomeAwayEntityMapCache);
    }

    @Scheduled(cron = "5 0 * * * ?")
    public void updateMatchLiveStatisticsRate3() throws InvalidOperationException, FormatException {
        // Schedule explicitly set after live events in order to calculate the total score. Should be after commit of the events
        doUpdateLiveMatchData(oneHourRateFixtureIds, MATCH_STATISTICS, fixtureIdHomeAwayEntityMapCache);
    }

    public void doUpdateLiveMatchData(List<String> fixtureIds, String type, Map<String, Pair<String, String>> fixtureIdHomeAwayEntityMap) throws InvalidOperationException, FormatException {
        String operation;
        switch (type) {
            case MATCH_STATUS -> operation = "updateLiveMatchSummary";
            case MATCH_STATISTICS -> operation = "updateLiveMatchStatistics";
            case MATCH_PLAY_BY_PLAY -> operation = "updateLiveMatchPlayByPlay";
            default -> operation = "";
        }
        if (!streamingActive) {
            log.info(operation + ": streaming is not active. Skipping..");
            return;
        }
        if (fixtureIds.isEmpty()) {
            log.info(operation + ": fixtureIds List is empty");
            return;
        }
        boolean isMqttStreamingActive = isStreamingActive();

        if (isNull(organizationId)) {
            log.info(operation + ": organizationId is null");
            return;
        }

        if (!isMqttStreamingActive) {
            log.info(operation + ": Streaming with DataCore Rest-Api is active");
            long start = System.currentTimeMillis();
            fixtureIds.stream().parallel().forEach(fixtureId -> {
                try {
                    switch (type) {
                        case MATCH_STATUS -> liveMatchSummaryHandler.updateLiveMatchSummary(fixtureId, organizationId, fixtureIdHomeAwayEntityMap.get(fixtureId));
                        case MATCH_STATISTICS -> livePersonMatchStatisticsHandler.updateLiveMatchStatistics(fixtureId, organizationId);
                        case MATCH_PLAY_BY_PLAY -> {
                            if (relevantEventTypes.isEmpty()) {
                                relevantEventTypes = sr2HmDataCorePlayerMatchEventMapper.getHpiCalculations().stream().map(HmDataCoreHpiCalculation::getDcEventType).collect(Collectors.toSet()).stream().toList();
                            }
                            liveMatchPlayByPlayHandler.updateLiveMatchPlayByPlay(fixtureId, organizationId, relevantEventTypes, fixtureIdHomeAwayEntityMap.get(fixtureId));
                        }
                    }

                } catch (Exception e) {
                    log.error(operation + ": update with DataCore Rest-Api for fixtureId [" + fixtureId + "] failed. Skipping", e);
                }
            });
            long executionTime = System.currentTimeMillis() - start;
            log.info(operation + ": executed in " + executionTime + " ms");
        } else {
            log.info(operation + ": Mqtt streaming is active . Streaming with DataCore Rest-Api disabled");
        }
    }
}
