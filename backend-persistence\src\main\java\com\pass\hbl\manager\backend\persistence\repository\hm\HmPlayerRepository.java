package com.pass.hbl.manager.backend.persistence.repository.hm;

import com.pass.hbl.manager.backend.persistence.domain.hm.HmPlayerDO;
import com.pass.hbl.manager.backend.persistence.dto.hm.Position;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmPlayer;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Set;
import java.util.UUID;

public interface HmPlayerRepository extends PagingAndSortingRepository<HmPlayer, UUID> {

    List<HmPlayer> findByPictureIsNull();

    Page<HmPlayer> findByFirstNameLikeIgnoreCase(String firstName, Pageable pageable);
    Page<HmPlayer> findByLastNameLikeIgnoreCase(String lastName, Pageable pageable);
    Page<HmPlayer> findByPosition(Position position, Pageable pageable);

    List<HmPlayer> findAllByIdIn(List<UUID> ids);

    Page<HmPlayer> findByFirstNameLikeIgnoreCaseAndLastNameLikeIgnoreCase(String firstName, String lastName, Pageable pageable);
    Page<HmPlayer> findByFirstNameLikeIgnoreCaseAndPosition(String firstName, Position position, Pageable pageable);
    Page<HmPlayer> findByLastNameLikeIgnoreCaseAndPosition(String lastName, Position position, Pageable pageable);

    Page<HmPlayer> findByFirstNameLikeIgnoreCaseAndLastNameLikeIgnoreCaseAndPosition(String firstName, String lastName, Position position, Pageable pageable);

    @Query(value = "select p from HmPlayer p " +
            "inner join HmPlayerMarketValue mv on p.id = mv.player and mv.currentValue = true and mv.deleted = false " +
            "where p.id not in (select distinct t.player.id from HmTeam t where t.left is null and t.league.id=:league and t.deleted=false)" +
            " and p.position = :position" +
            " order by random()")
    List<HmPlayer> findRandomPlayer(@Param("league")UUID league, @Param("position") Position position, Pageable pageable);

    @Query(value = "select p from HmPlayer p " +
            "inner join HmPlayerMarketValue mv on p.id = mv.player and mv.currentValue = true and mv.deleted = false " +
            "where p.id not in (select distinct t.player.id from HmTeam t where t.left is null and t.league.id=:league and t.deleted=false) " +
            " and p.position = :position" +
            " and p.id not in (:existing) " +
            "order by random()")
    List<HmPlayer> findRandomPlayerForDuplicatedPosition(@Param("league")UUID league, @Param("position") Position position, @Param("existing") Set<UUID> existing, Pageable pageable);

    @Modifying
    @Query(value = "update HmPlayer p set p.active = false")
    void setAllInactive();

    List<HmPlayer> findByPosition(Position position);

    @Query(value = "select p from HmPlayer p where p.position = :position and p.id not in (:existing) order by random()")
    List<HmPlayer> findRandomPlayerByPositionExcludingList(@Param("position") Position position,  @Param("existing") List<UUID> existing);

    @Query(value = "select p.id as id, p.position as position, mv.marketValue as marketValue from HmPlayer p " +
            "inner join HmPlayerMarketValue mv on p.id = mv.player.id and mv.currentValue = true and mv.deleted = false " +
            " where p.active = true and (not mv.marketValue is null) and mv.marketValue > :marketValueMin and mv.marketValue < :marketValueMax")
    List<HmPlayerDO> findAllPlayersByMarketValueIn(@Param("marketValueMin") Integer marketValueMin, @Param("marketValueMax") Integer marketValueMax);

}
