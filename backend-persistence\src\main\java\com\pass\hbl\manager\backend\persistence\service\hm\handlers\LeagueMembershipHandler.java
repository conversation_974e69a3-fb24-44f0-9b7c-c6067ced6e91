package com.pass.hbl.manager.backend.persistence.service.hm.handlers;

import com.pass.hbl.manager.backend.persistence.domain.admin.AdminLeagueMembersCountDO;
import com.pass.hbl.manager.backend.persistence.domain.hm.*;
import com.pass.hbl.manager.backend.persistence.dto.hm.NotificationBodyKeyword;
import com.pass.hbl.manager.backend.persistence.dto.hm.SessionAttribute;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmLeague;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmLeagueMembership;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmPlayer;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmUserProfile;
import com.pass.hbl.manager.backend.persistence.exception.*;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmLeagueMembershipRepository;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmUserAwardRepository;
import com.pass.hbl.manager.backend.persistence.service.admin.LogMessageService;
import com.pass.hbl.manager.backend.persistence.service.hm.*;
import com.pass.hbl.manager.backend.persistence.service.hm.helpers.LeagueHelper;
import com.pass.hbl.manager.backend.persistence.service.shared.TransactionHandler;
import com.pass.hbl.manager.backend.persistence.util.Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.context.annotation.Lazy;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

import static com.pass.hbl.manager.backend.persistence.dto.hm.NotificationEvent.REMOVE_LEAGUE_MEMBER;
import static com.pass.hbl.manager.backend.persistence.util.Constants.ON_HOLD_MEMBERSHIP_MAX_DAYS;
import static com.pass.hbl.manager.backend.persistence.util.Constants.SYSTEM_USERNAME;
import static java.util.Objects.nonNull;
import static java.util.Optional.empty;
import static org.springframework.transaction.annotation.Propagation.REQUIRES_NEW;

@Slf4j
@Service
@Transactional
public class LeagueMembershipHandler {

    private final HmLeagueMembershipRepository leagueMembershipRepository;
    private final HmUserAwardRepository userAwardRepository;

    private final TransferMarketService transferMarketService;
    private final TeamService teamService;
    private final LogMessageService logMessageService;
    private final UserProfileService userProfileService;
    private final AwardService awardService;

    private final LeagueScoreHandler leagueScoreHandler;
    private final TransactionHandler transactionHandler;
    private final UserNotificationHandler userNotificationHandler;
    private final MessagingService messagingService;

    private final LeagueHelper leagueHelper;


    public LeagueMembershipHandler(@Lazy HmLeagueMembershipRepository leagueMembershipRepository, @Lazy HmUserAwardRepository userAwardRepository, @Lazy TransferMarketService transferMarketService, @Lazy TeamService teamService, LogMessageService logMessageService, @Lazy UserProfileService userProfileService, @Lazy AwardService awardService, @Lazy LeagueScoreHandler leagueScoreHandler, @Lazy TransactionHandler transactionHandler, @Lazy UserNotificationHandler userNotificationHandler, @Lazy MessagingService messagingService, @Lazy LeagueHelper leagueHelper) {
        this.leagueMembershipRepository = leagueMembershipRepository;
        this.userAwardRepository = userAwardRepository;
        this.transferMarketService = transferMarketService;
        this.teamService = teamService;
        this.logMessageService = logMessageService;
        this.userProfileService = userProfileService;
        this.awardService = awardService;
        this.leagueScoreHandler = leagueScoreHandler;
        this.transactionHandler = transactionHandler;
        this.userNotificationHandler = userNotificationHandler;
        this.messagingService = messagingService;
        this.leagueHelper = leagueHelper;
    }

    @Retryable(value = {RuntimeException.class}, backoff = @Backoff(delay = 1000))
    public void checkOnHoldMemberships(LocalDateTime now) {
        try {
            LocalDateTime fromDate = now.minusDays(ON_HOLD_MEMBERSHIP_MAX_DAYS);
            List<HmLeagueMembershipDO> onHoldMembershipsToDelete = leagueMembershipRepository.findOnHoldMembershipsBeforeOrEqualDate(fromDate);
            int size = onHoldMembershipsToDelete.size();
            log.info("checkOnHoldMemberships: found [" + size + "] on hold league memberships to be deleted");
            List<UUID> idsToDelete = onHoldMembershipsToDelete.stream().map(m -> UUID.fromString(m.getId())).toList();
            int rows = leagueMembershipRepository.resetOnHoldById(idsToDelete);
            log.info("checkOnHoldMemberships: on hold status reset for [" + rows + "] on hold league memberships");
            AtomicInteger counter = new AtomicInteger(1);
            onHoldMembershipsToDelete.stream().parallel().forEach(lm -> {
                teamService.deleteTeamByLeagueAndOwner(UUID.fromString(lm.getLeagueId()), UUID.fromString(lm.getMemberId()));
                log.info("checkOnHoldMemberships: delete team and lineup by league [" + lm.getLeagueId() + "] and member [" + lm.getMemberId() +
                        "] done (" + counter + " / " + size + ")");
                counter.getAndIncrement();
            });
        } catch (Exception e) {
            String message = "checkOnHoldMemberships: failed to check to check on hold memberships";
            log.error(message, e);
            logMessageService.logException("checkOnHoldMemberships", message, e);
        }
    }

    public void doDeleteLeagueMember(UUID leagueId, UUID memberId, boolean setOnHold) {
        if (setOnHold) {
            leagueMembershipRepository.deleteAndSetOnHoldByLeagueAndMember(leagueId, memberId);
        } else {
            leagueMembershipRepository.deleteByLeagueAndMember(leagueId, memberId);
        }
        transferMarketService.deleteAppearancesAndBidsByLeagueAndOwner(leagueId, memberId);
        transferMarketService.deleteBidsByLeagueAndBidder(leagueId, memberId);
        if (!setOnHold) {
            teamService.deleteTeamByLeagueAndOwner(leagueId, memberId);
        }
        userAwardRepository.deleteByLeagueIdAndUserId(leagueId, memberId);
        leagueScoreHandler.deleteScoresByLeagueAndUser(leagueId, memberId);
    }

    public List<LeagueMembershipInfoDo> getAllMembershipsByLeague(UUID leagueId) {
        return leagueMembershipRepository.findAllMembershipInfoByLeagueId(leagueId).stream().map(info -> {
            UUID id = (UUID) info[0];
            UUID userId = (UUID) info[1];
            LocalDateTime joined = (LocalDateTime) info[2];
            Integer score = (Integer) info[3];
            Integer balance = (Integer) info[4];
            return new LeagueMembershipInfoDo(id, userId, leagueId, joined, score, balance);
        }).toList();
    }

    public List<LeagueMembershipInfoDo> getAllMembershipsByLeagueJoinedBeforeClosing(UUID leagueId, LocalDateTime closing) {
        return leagueMembershipRepository.findAllMembershipInfoByLeagueId(leagueId).stream().map(info -> {
            UUID id = (UUID) info[0];
            UUID userId = (UUID) info[1];
            LocalDateTime joined = (LocalDateTime) info[2];
            Integer score = (Integer) info[3];
            Integer balance = (Integer) info[4];
            return new LeagueMembershipInfoDo(id, userId, leagueId, joined, score, balance);
        }).filter(memberInfo -> memberInfo.joined().isBefore(closing)).toList();
    }

    public UUID join(HmUserProfile user, HmLeague league, LocalDateTime joinedTime, boolean newLeague, boolean joinByLeagueReset) throws
            InvalidOperationException, FormatException, ForbiddenOperationException, SchedulingException, EntityNotExistException {
        log.info("join league called    for user[" + user.getId() + "] and league[" + league.getId() + "], newLeague[" + newLeague + "]");
        long start = System.currentTimeMillis();
        Optional<Pair<UUID, HmAwardDescriptionDO>> fullHouseAwardByOwnerIdPairOptional = empty();
        if (!newLeague && !joinByLeagueReset) {
            int leagueSize = leagueHelper.checkLeagueCompleteness(league.getId(), user.getId().toString());
            // check full house award. If assigned return [ownerId, award description] pair relevant for sending notification to the league owner
            fullHouseAwardByOwnerIdPairOptional = awardService.checkFullHouseAward(league, leagueSize + 1);

            Optional<HmLeagueMembershipDO> onHoldMembershipOpt = leagueMembershipRepository.findOnHoldMembershipByUserIdAndLeagueId(user.getId(), league.getId());
            if (onHoldMembershipOpt.isPresent()) {
                log.info("on hold league membership found for user[" + user.getId() + "] and league[" + league.getId() + "], it will be activated");
                HmLeagueMembershipDO leagueMembershipDO = onHoldMembershipOpt.get();
                UUID membershipId = transactionHandler.runInNewTransaction(() -> activateOnHoldLeagueMembership(leagueMembershipDO));
                userProfileService.setSessionAttribute(user.getId().toString(), SessionAttribute.CURRENT_LEAGUE, league.getId().toString());
                // By full bench award send notification to the league owner
                if (fullHouseAwardByOwnerIdPairOptional.isPresent()) {
                    Pair<UUID, HmAwardDescriptionDO> fullHouseAwardByOwnerIdPair = fullHouseAwardByOwnerIdPairOptional.get();
                    awardService.sendNotificationByLeagueAward(league.getName(), fullHouseAwardByOwnerIdPair.getKey(), fullHouseAwardByOwnerIdPair.getValue());
                }
                return membershipId;
            }
        }
        Pair<Integer, List<HmPlayer>> initialTeam = teamService.drawInitialTeam(user, league);
        log.info("drawInitialTeam execution time: " + (System.currentTimeMillis() - start));
        int remainingBudget = initialTeam.getLeft();
        List<HmPlayer> selectedPlayers = initialTeam.getRight();
        // add random players to the transfer market by league creation
        if (newLeague) {
            transferMarketService.drawInitialTransferMarket(user, league, selectedPlayers);
            log.info("drawInitialTransferMarket execution time: " + (System.currentTimeMillis() - start));
        }
        HmLeagueMembership membership = leagueMembershipRepository.save(new HmLeagueMembership(user, league, joinedTime, 0, remainingBudget));
        if (!joinByLeagueReset) {
            userProfileService.setSessionAttribute(user.getId().toString(), SessionAttribute.CURRENT_LEAGUE, league.getId().toString());
        }
        // By full bench award send notification to the league owner
        if (!newLeague && !joinByLeagueReset && fullHouseAwardByOwnerIdPairOptional.isPresent()) {
            Pair<UUID, HmAwardDescriptionDO> fullHouseAwardByOwnerIdPair = fullHouseAwardByOwnerIdPairOptional.get();
            awardService.sendNotificationByLeagueAward(league.getName(), fullHouseAwardByOwnerIdPair.getKey(), fullHouseAwardByOwnerIdPair.getValue());
        }
        log.info("join league execution time: " + (System.currentTimeMillis() - start));
        return membership.getId();
    }

    public UUID activateOnHoldLeagueMembership(HmLeagueMembershipDO leagueMembershipDO) {
        // activate the league memberships, user awards and user round scores; Team and lineup were not deleted for on hold league memberships
        UUID id = UUID.fromString(leagueMembershipDO.getId());
        UUID userId = UUID.fromString(leagueMembershipDO.getMemberId());
        int rows = leagueMembershipRepository.activateMembershipsByIds(List.of(id));
        log.info("activateOnHoldLeagueMembership: [" + rows + "] on hold memberships activated for user id[" + leagueMembershipDO.getMemberId() + "] and league id [" + leagueMembershipDO.getLeagueId() + "]");
        if (rows == 0) {
            log.info("activateOnHoldLeagueMembership: on hold membership could not be activated for user id[" + leagueMembershipDO.getMemberId() + "] and league id [" + leagueMembershipDO.getLeagueId() + "]. Skipping..");
            return null;
        }
        UUID leagueId = UUID.fromString(leagueMembershipDO.getLeagueId());
        awardService.activateAwardsByUserAndLeagues(userId, List.of(leagueId));
        leagueScoreHandler.activateUserRoundScoresByUserAndLeague(userId, leagueId);
        return UUID.fromString(leagueMembershipDO.getId());
    }

    public void doCleanupMembers(HmLeague league) {
        List<HmUserProfile> users = league.getLeagueMemberships().stream().map(HmLeagueMembership::getUserProfile).toList();
        for (HmUserProfile user : users) deleteLeagueMember(user, league);
    }

    public void removeLeagueMemberByAdmin(HmUserProfile user, HmLeague league) throws ForbiddenOperationException {
        doRemoveLeagueMemberByAdmin(league, user);
    }

    public void removeLeagueMemberByAdmin(HmLeague league, HmUserProfile user, @NotNull String currentRole) throws ForbiddenOperationException {
        doRemoveLeagueMemberByAdmin(league, user);
        String message = "User [" + user.getId() + "] has been removed from league [" + league.getId() + "] by Admin with Role [" + currentRole + "]";
        log.info(message);
        logMessageService.logInfo("removePlayerByAdmin", message);
    }

    private void doRemoveLeagueMemberByAdmin(HmLeague league, HmUserProfile user) throws ForbiddenOperationException {
        leagueHelper.checkUserIsNotLeagueOwner(league.getId(), user.getId().toString(), "Remove player by Admin");
        deleteLeagueMember(user, league);
        UUID picture = league.getPicture();
        String userId = user.getId().toString();
        String leagueId = league.getId().toString();
        String leagueLogo = nonNull(picture) ? picture.toString() : null;
        Map<NotificationBodyKeyword, String> keywords = userNotificationHandler.getNotificationBodyKeywords(null, SYSTEM_USERNAME, null, league.getName(), null);
        Locale locale = Util.getLocaleByLanguageTagOrDefault(user.getAppLanguage());
        log.info("Firebase message with id [" + messagingService.sendNotification(userNotificationHandler.getNotificationTitle(REMOVE_LEAGUE_MEMBER, locale),
                userNotificationHandler.getNotificationBody(REMOVE_LEAGUE_MEMBER, keywords, locale),
                userId, new LeagueNotificationDo(leagueId, userId, REMOVE_LEAGUE_MEMBER, leagueLogo)) + "] sent. Event: " + REMOVE_LEAGUE_MEMBER);
    }

    public void deleteLeagueMember(HmUserProfile user, HmLeague league) {
        UUID leagueId = league.getId();
        UUID memberId = user.getId();
        doDeleteLeagueMember(leagueId, memberId, false);
    }

    public List<HmLeagueMembershipBaseDO> findAllLeagueMembershipsInLeagues(List<UUID> leagueIds) {
        return leagueMembershipRepository.findAllMembershipsByLeagueIdIn(leagueIds);
    }

    @Transactional(propagation = REQUIRES_NEW, readOnly = true)
    public List<UUID> getLeagueIdsWithFullMemberships(UUID seasonId) {
        return leagueMembershipRepository.findLeaguesWithMaxMembershipCount(seasonId, getMaxLeagueSize()).stream()
                .map(UUID::fromString).toList();
    }

    public int getMaxLeagueSize() {
        return leagueHelper.getMaxLeagueSize();
    }

    @Transactional(propagation = REQUIRES_NEW, readOnly = true)
    public List<HmLeagueMembershipScoreDO> getAllMembershipsInSeason(UUID seasonId) {
        return leagueMembershipRepository.findAllMembershipInfoBySeason(seasonId);
    }

    @Transactional(propagation = REQUIRES_NEW, readOnly = true)
    public List<HmLeagueMembershipScoreDO> getAllMembershipsOfActiveLeaguesInSeason(UUID seasonId) {
        return leagueMembershipRepository.findAllMembershipInfoBySeasonAndActiveLeagues(seasonId);
    }

    @Transactional(propagation = REQUIRES_NEW, readOnly = true)
    public List<HmLeagueMembershipScoreDO> getAllMembershipsInSeasonExcludingLeagues(UUID seasonId, Set<UUID> excludedLeagues) {
        return leagueMembershipRepository.findMembershipInfoBySeasonAndLeagueIdNotIn(seasonId, excludedLeagues);
    }

    @Transactional(propagation = REQUIRES_NEW, readOnly = true)
    public List<AdminLeagueMembersCountDO> findMembersCountByLeague(UUID seasonId) {
        return leagueMembershipRepository.findMembersCountByLeague(seasonId);
    }
}
