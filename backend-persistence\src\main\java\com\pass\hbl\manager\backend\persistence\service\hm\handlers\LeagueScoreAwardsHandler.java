package com.pass.hbl.manager.backend.persistence.service.hm.handlers;


import com.pass.hbl.manager.backend.persistence.domain.hm.*;
import com.pass.hbl.manager.backend.persistence.dto.hm.AwardCode;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmAward;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmRound;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmSeason;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmUserProfile;
import com.pass.hbl.manager.backend.persistence.entity.shared.SharedLocalization;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmUserRoundScoreRepository;
import com.pass.hbl.manager.backend.persistence.service.admin.LogMessageService;
import com.pass.hbl.manager.backend.persistence.service.hm.*;
import com.pass.hbl.manager.backend.persistence.service.shared.TransactionHandler;
import com.pass.hbl.manager.backend.persistence.util.Util;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.pass.hbl.manager.backend.persistence.dto.hm.AwardCode.*;
import static com.pass.hbl.manager.backend.persistence.util.Constants.*;
import static java.util.Objects.nonNull;
import static java.util.Optional.empty;

@Service
@Slf4j
@Transactional
public class LeagueScoreAwardsHandler {

    private final HmUserRoundScoreRepository userRoundScoreRepository;

    private final LogMessageService logMessageService;
    private final TeamService teamService;
    private final SeasonService seasonService;
    private final UserProfileService userProfileService;
    private final PlayerService playerService;
    private final AwardService awardService;

    private final LeagueMembershipHandler leagueMembershipHandler;
    private final AwardCheckingHandler awardCheckingHandler;
    private final TransactionHandler transactionHandler;

    public LeagueScoreAwardsHandler(HmUserRoundScoreRepository userRoundScoreRepository, LogMessageService logMessageService, TeamService teamService, SeasonService seasonService, UserProfileService userProfileService, PlayerService playerService, @Lazy AwardService awardService, LeagueMembershipHandler leagueMembershipHandler, AwardCheckingHandler awardCheckingHandler, TransactionHandler transactionHandler) {
        this.userRoundScoreRepository = userRoundScoreRepository;
        this.logMessageService = logMessageService;
        this.teamService = teamService;
        this.seasonService = seasonService;
        this.userProfileService = userProfileService;
        this.playerService = playerService;
        this.awardService = awardService;
        this.leagueMembershipHandler = leagueMembershipHandler;
        this.awardCheckingHandler = awardCheckingHandler;
        this.transactionHandler = transactionHandler;
    }

    public void assignMatchDayAwards(HmRound round, HmRound currentRound) throws EntityNotExistException {
        UUID roundId = round.getId();
        // check if we are before the closing of the given round
        LocalDateTime now = LocalDateTime.now();
        if (now.isAfter(currentRound.getClosing()) && now.isBefore(currentRound.getTo())) {
            log.info("assignMatchDayAwards: disabled in order to keep user balance on round closing unchanged. Reason: we are now in interval [closing - end] of current round");
            return;
        }

        // check all round awards exist
        Set<AwardCode> codes = Set.of(MATCHDAY_WINNER, PICKER_BRONZE, PICKER_SILVER, PICKER_GOLD);
        Map<AwardCode, HmAward> roundScoreAwards = awardCheckingHandler.getAwardsByCodeIn(codes);
        if (roundScoreAwards.isEmpty() || (codes.size() != roundScoreAwards.size())) {
            String message = "assignMatchDayAwards: disabled since the award codes: " + codes + " are not completely found.";
            log.info(message);
            logMessageService.logInfo("assignMatchDayAwards", message);
            return;
        }

        // get already processed leagues: leagues that already have awards assigned to the given round
        // Important: It doesn't include leagues that have been processed and no awards created to these leagues:
        // 1st run: league 1 -> no user award created
        // 2nd run: league 2-> will be processed again since it doesn't have awards
        Set<UUID> alreadyProcessedLeagues = awardCheckingHandler.getLeagueIdsByLastRoundIdAndAwardIdIn(roundScoreAwards.values().stream().map(HmAward::getId).collect(Collectors.toList()), roundId);
        log.info("assignMatchDayAwards: already processed leagues having round score awards = " + alreadyProcessedLeagues.size());

        // 1- get all DOs (league_id, user_id, score) having non-null result in the given round
        List<HmUserLeagueScoreDO> userLeagueScoresToProcess;
        if (alreadyProcessedLeagues.isEmpty()) {
            userLeagueScoresToProcess = userRoundScoreRepository.findAllUserLeagueScoresByRoundId(roundId);
        } else {
            userLeagueScoresToProcess = userRoundScoreRepository.findUserLeagueScoresByRoundIdExcludingLeagues(roundId, alreadyProcessedLeagues);
        }
        Map<String, List<HmUserLeagueScoreDO>> userScoresByLeagueId = userLeagueScoresToProcess.stream()
                .collect(Collectors.groupingBy(HmUserLeagueScoreDO::getLeagueId));
        int size = userScoresByLeagueId.size();
        log.info("assignMatchDayAwards: leagues to process = " + size);

        // 2- compare the scores to get the match day winner by league: user with the highest score in league.
        // If scores are equal, return the user with the lowest team value
        Map<UUID, UUID> matchDayWinnerByLeagueMap = new HashMap<>();

        // 3- get pairs of <league_id, user_id> including only leagues where the match-day winner could be set
        // Interrupt all the process if an exception occurs by getting all match day winners by league
        // since this step could not be executed partly.
        List<HmPlayerMarketValueDO> playerMarketValuesAtClosing = playerService.getAllPlayerMarketValuesAtDate(round.getClosing().toLocalDate());
        log.info("assignMatchDayAwards: count playerMarketValuesAtClosing = " + playerMarketValuesAtClosing.size());

        AtomicInteger counter = new AtomicInteger(1);
        userScoresByLeagueId.entrySet().stream().parallel().forEach(entry -> {
            UUID leagueId = UUID.fromString(entry.getKey());
            List<HmUserLeagueScoreDO> userScores = entry.getValue();
            Optional<HmUserLeagueScoreDO> matchDayWinnerByLeague = getMatchDayWinnerByLeague(roundId, leagueId, userScores, playerMarketValuesAtClosing);
            matchDayWinnerByLeague.ifPresent(hmUserLeagueScoreDO -> matchDayWinnerByLeagueMap.put(leagueId, UUID.fromString(hmUserLeagueScoreDO.getUserId())));
            String leagueWinnerId = matchDayWinnerByLeague.isPresent() ? matchDayWinnerByLeague.get().getUserId() : "empty";
            log.info("assignMatchDayAwards: get round winner for league " + leagueId + " done. Winner id = [" + leagueWinnerId + "] (" + counter + " / " + size + ")");
            counter.getAndIncrement();
        });
        log.info("matchDayWinnerByLeagueMap count = " + matchDayWinnerByLeagueMap.size());

        // 4- get all league memberships for relevant leagues
        UUID seasonId = seasonService.getCurrentSeason().getId();
        List<HmLeagueMembershipScoreDO> membershipInfosBySeasonToProcess;
        if (alreadyProcessedLeagues.isEmpty()) {
            membershipInfosBySeasonToProcess = leagueMembershipHandler.getAllMembershipsInSeason(seasonId);
        } else {
            membershipInfosBySeasonToProcess = leagueMembershipHandler.getAllMembershipsInSeasonExcludingLeagues(seasonId, alreadyProcessedLeagues);
        }
        log.info("assignMatchDayAwards: count memberships to process");
        if (membershipInfosBySeasonToProcess.isEmpty()) {
            log.info("assignMatchDayAwards: no memberships found return");
            return;
        }

        // 5- group the memberships by league id
        Map<UUID, List<HmLeagueMembershipScoreDO>> membershipsByLeagueIdMap = membershipInfosBySeasonToProcess.stream()
                .collect(Collectors.groupingBy(HmLeagueMembershipScoreDO::getLeagueId));
        int leagueMapSize = membershipsByLeagueIdMap.size();

        // 6- iterate over the leagues and process each league separately in a new transaction.
        // Once the league is processed the resulted awards will be created & all league members balances
        // will be updated accordingly.
        // the HmUserAward.lastRound.id attribute will be used to recognize the leagues that were processed in case
        // the whole process should be repeated.
        AtomicInteger leagueCounter = new AtomicInteger(1);
        log.info("assignMatchDayAwards: checkRoundScoreAwardsByLeague, count leagues to process = " + leagueMapSize);
        List<HmLeagueMemberAwardsDO> leagueMemberAwardsResult  = new ArrayList<>();
        List<HmUserProfile> userCache = new ArrayList<>();

        membershipsByLeagueIdMap.entrySet().stream().parallel().forEach(entry -> {
            UUID leagueId = entry.getKey();
            try {
                UUID matchDayWinnerId = matchDayWinnerByLeagueMap.get(leagueId);
                // handle league without match day winner
                Optional<UUID> matchDayWinnerOptional = nonNull(matchDayWinnerId) ? Optional.of(matchDayWinnerId) : Optional.empty();

                // assign awards to these users:
                // - match day winner: increment with 1 if existing, create new one if not
                // - point picker: assign award if not exists (not stackable)
                // 1- update balance by league membership.
                // 2- return the experience points (to add) & assigned award by user and league

                List<HmLeagueMemberAwardsDO> leagueMemberAwardsResultByLeague = awardCheckingHandler.checkRoundScoreAwardsByLeague(userCache, entry, matchDayWinnerOptional, round);

                if (!leagueMemberAwardsResultByLeague.isEmpty()) {
                    leagueMemberAwardsResult.addAll(leagueMemberAwardsResultByLeague);
                }
                log.info("assignMatchDayAwards: checkRoundScoreAwardsByLeague for league [" + leagueId + "] done. (" + leagueCounter + " / " + leagueMapSize + ")");
            } catch (Exception e) {
                String message = "assignMatchDayAwards: checkRoundScoreAwardsByLeague for league [" + leagueId + "] failed. Reason:" + e.getMessage() + ". Skipping..";
                log.error(message, e);
                logMessageService.logException("assignMatchDayAwards: checkRoundScoreAwardsByLeague", message, e);
            }
            leagueCounter.getAndIncrement();
        });

        // 7- update experience points by users
        Map<UUID, Integer> experiencePointsToAddByUser = leagueMemberAwardsResult.stream().filter(t -> nonNull(t.getUserId())).collect(Collectors.groupingBy(HmLeagueMemberAwardsDO::getUserId, Collectors.summingInt(HmLeagueMemberAwardsDO::getExperiencePointsByAwards)));

        addExperiencePointsToUserProfiles("assignMatchDayAwards", userCache, experiencePointsToAddByUser);

        // 8- send notifications to users who have earned awards
        Map<UUID, List<HmLeagueMemberAwardsDO>> awardsByUserMap = leagueMemberAwardsResult.stream().filter(t -> nonNull(t.getUserId())).collect(Collectors.groupingBy(HmLeagueMemberAwardsDO::getUserId));
        awardService.sendUserNotifications("assignMatchDayAwards", awardsByUserMap, userCache, false);
    }

    private void addExperiencePointsToUserProfiles(String process, List<HmUserProfile> userCache, Map<UUID, Integer> experiencePointsToAddByUser) {
        int usersMapSize = experiencePointsToAddByUser.keySet().size();
        AtomicInteger userCounter = new AtomicInteger(1);
        log.info(process + ": update experience points by user, count user to process = " + usersMapSize);
        experiencePointsToAddByUser.entrySet().stream().parallel().forEach(entry -> {
            UUID userId = entry.getKey();
            Integer experiencePointsToAdd = entry.getValue();
            Optional<HmUserProfile> userProfileOptional = awardCheckingHandler.getFromUserCache(userCache, userId);
            if (userProfileOptional.isPresent()) {
                HmUserProfile user = userProfileOptional.get();
                if (experiencePointsToAdd > 0) {
                    int newExperiencePoints = user.getExperiencePoints() + experiencePointsToAdd;
                    userProfileService.updateExperiencePointsAndLevelByUser(userId, newExperiencePoints);
                    log.info(process + ": update experience points by user for user " + userId + " done. (" + userCounter + " / " + usersMapSize + ")");
                } else {
                    log.info(process + ": update experience points by user for user " + userId + " skipped. Reason: [experiencePointsToAdd = 0] . (" + userCounter + " / " + usersMapSize + ")");
                }
                userCounter.getAndIncrement();
            }
        });
    }

    private Optional<HmUserLeagueScoreDO> getMatchDayWinnerByLeague(UUID roundId, UUID leagueId, List<HmUserLeagueScoreDO> userScores, List<HmPlayerMarketValueDO> playerMarketValuesAtClosing) {
        // check all scores are -70 (no lineup)
        Set<Integer> allScoresInLeague = userScores.stream().map(HmUserLeagueScoreDO::getScore).collect(Collectors.toSet());
        if (allScoresInLeague.size() == 1 && allScoresInLeague.contains(-70)) {
            log.info("assignMatchDayAwards: all scores in league with id [" + leagueId + "] are equal -70, no match day winner. Skipping..");
        } else if (allScoresInLeague.size() == 1 && allScoresInLeague.contains(0)) {
            log.info("assignMatchDayAwards: all scores in league with id [" + leagueId + "] are equal 0. no match day winner. Skipping..");
        } else {
            // The league winner is the member with the highest score.
            // By equality then the member with lower lineup market value
            Comparator<HmUserLeagueScoreDO> compareByScoreThenLineupValue = Comparator.comparingInt(HmUserLeagueScoreDO::getScore)
                    // reverse the order by multiplying by - 1 to get the member with the lowest lineup value
                    .thenComparingInt(userLeagueScoreDO -> -teamService.getLineupMarketValueAtDate(userLeagueScoreDO.getUserId(), userLeagueScoreDO.getLeagueId(), roundId, playerMarketValuesAtClosing));
            return userScores.stream()
                    .filter(userLeagueScoreDO -> nonNull(userLeagueScoreDO.getScore()))
                    .max(compareByScoreThenLineupValue);
        }
        return Optional.empty();
    }

    /**
     * Adds the experience points to users who got match-day winner or point picker awards in the given round.
     * IMPORTANT: This is a recovery method to resume {@link #assignMatchDayAwards(HmRound, HmRound)} if the last step has failed.
     * It should be executed only one time by round in these cases:
     * - All leagues in the method {@link #assignMatchDayAwards(HmRound, HmRound)} have been processed (not partly)
     * - The last step to add experience points to users in the method {@link #assignMatchDayAwards(HmRound, HmRound)} has failed
     *
     * @param roundId id of the round in which the user awards were assigned
     */
    public void addExperiencePointsByLeagueScoreAwards(UUID roundId) {
        // check all round awards exist
        Set<AwardCode> codes = Set.of(MATCHDAY_WINNER, PICKER_BRONZE, PICKER_SILVER, PICKER_GOLD);
        Map<AwardCode, HmAward> roundScoreAwards = awardCheckingHandler.getAwardsByCodeIn(codes);
        if (roundScoreAwards.isEmpty() || (codes.size() != roundScoreAwards.size())) {
            String message = "updateExperiencePointsByLeagueScoreAwards: disabled since the award codes: " + codes + " are not completely found.";
            log.info(message);
            logMessageService.logInfo("assignMatchDayAwards", message);
            return;
        }
        ArrayList<HmAward> awards = new ArrayList<>(roundScoreAwards.values());
        List<HmUserAwardDO> userAwards = awardCheckingHandler.getUserAwardByLastRoundIdAndAwardIdIn(awards.stream().map(HmAward::getId).toList(), roundId);
        log.info("updateExperiencePointsByLeagueScoreAwards: found user awards: " + userAwards.size());
        Map<UUID, Integer> experiencePointsToAddByUser = userAwards.stream().collect(Collectors.groupingBy(value -> UUID.fromString(value.getUserId()),
                Collectors.summingInt(value -> getExperiencePointsByAwardId(awards, UUID.fromString(value.getAwardId())))));

        List<HmUserProfile> userProfileList = userProfileService.getAllByIdIn(experiencePointsToAddByUser.keySet());
        addExperiencePointsToUserProfiles("updateExperiencePointsByLeagueScoreAwards", userProfileList, experiencePointsToAddByUser);
    }

    private int getExperiencePointsByAwardId(List<HmAward> awards, UUID awardId) {
        return awards.stream().filter(a -> Objects.equals(a.getId(), awardId)).map(HmAward::getExperiencePoints).filter(Objects::nonNull).findFirst().orElse(0);
    }

    public void assignAwardsToAllLeagueWinners(String seasonId) {
        try {
            // 1- get league winner award
            Optional<HmAward> leagueWinnerAwardOptional = awardService.getLeagueWinnerAward();
            if (leagueWinnerAwardOptional.isEmpty()) {
                log.info("assignAwardsToAllLeagueWinners: League winner award with descriptions not found. Please make sure that an award with code LEAGUE_WINNER with descriptions exists.");
                return;
            }
            HmAward award = leagueWinnerAwardOptional.get();
            Map<Locale, HmAwardDescriptionDO> awardDescriptionByLocaleMap = new HashMap<>();
            Arrays.asList(DEFAULT_LOCALE, EN_LOCALE).forEach(locale -> {
                Optional<SharedLocalization> localizationOptional = award.getDescriptions().stream().filter(localization ->
                        Objects.equals(localization.getLocale().toString(), locale.toString()) && Objects.equals(localization.getKey(), AWARD_KEY_NAME)).findFirst();
                String awardName = localizationOptional.isPresent()? localizationOptional.get().getValue(): LEAGUE_WINNER.name();
                awardDescriptionByLocaleMap.put(locale, new HmAwardDescriptionDO(LEAGUE_WINNER, awardName, award.getPicture(), locale, award.getMoney()));
            });

            // 2- get the league winner for each league
            Map<UUID, HmLeagueMembershipScoreDO> winnerByLeague = getWinnersByLeague(seasonId);
            if (winnerByLeague == null) return;

            // 3- assign league winner award to the users
            int size = winnerByLeague.size();
            log.info("assignAwardsToAllLeagueWinners: league winners to process count = " + size);
            Set<UUID> userIds = winnerByLeague.values().stream().map(HmLeagueMembershipScoreDO::getUserId).collect(Collectors.toSet());

            // get all distinct user profiles of winners
            log.info("assignAwardsToAllLeagueWinners: count distinct user ids = " + userIds.size());
            List<HmUserProfile> leagueWinnersProfiles = userProfileService.getAllByIdIn(userIds);

            List<HmLeagueMemberAwardsDO> leagueMemberAwardsResult  = new ArrayList<>();
            AtomicInteger counter = new AtomicInteger(1);
            winnerByLeague.entrySet().stream().parallel().forEach(entry -> {
                // Transaction handler run in new transaction (write)
                UUID userId = entry.getValue().getUserId();
                Optional<HmUserProfile> userOptional = leagueWinnersProfiles.stream().filter(u -> Objects.equals(u.getId(), userId)).findAny();
                HmUserProfile user;
                UUID leagueId = entry.getKey();
                try {
                    if (userOptional.isPresent()) {
                        user = userOptional.get();
                    } else {
                        user = userProfileService.getByIdInNewTransaction(userId);
                    }
                    HmAwardDescriptionDO awardDescription = awardDescriptionByLocaleMap.get(Util.getLocaleByLanguageTag(user.getAppLanguage()));
                    Optional<HmLeagueMemberAwardsDO> leagueMemberAwardsOptional = transactionHandler.runInNewTransaction(() -> assignLeagueWinnerAwardToUser(award, leagueId, user, awardDescription));

                    // add the award earned by the user to the result list
                    leagueMemberAwardsOptional.ifPresent(leagueMemberAwardsResult::add);
                    log.info("assignAwardsToAllLeagueWinners: assign league winner award for user [" + userId + "] in league [" + leagueId + "] done. (" + counter + " / " + size + ")");
                } catch (Exception e) {
                    String message = "assignAwardsToAllLeagueWinners: assign league winner award for user [" + userId + "] in league [" + leagueId + "]. (" + counter + " / " + size + ")";
                    log.error(message, e);
                    logMessageService.logException("assignAwardsToAllLeagueWinners", message, e);
                }
                counter.getAndIncrement();
            });

            // 3- send notifications to users who have earned league winner awards
            Map<UUID, List<HmLeagueMemberAwardsDO>> awardsByUserMap = leagueMemberAwardsResult.stream().filter(t -> nonNull(t.getUserId())).collect(Collectors.groupingBy(HmLeagueMemberAwardsDO::getUserId));
            transactionHandler.runInNewTransaction(() -> {
                awardService.sendUserNotifications("assignAwardsToAllLeagueWinners", awardsByUserMap, leagueWinnersProfiles, false);
                return null;
            });
        } catch (Exception e) {
            log.error("Failed to assign awards to league winners", e);
            logMessageService.logException("assignAwardsToAllLeagueWinners", e);
        }
    }

    //TODO hbs remove operation parameter
    @Nullable
    private Map<UUID, HmLeagueMembershipScoreDO> getWinnersByLeague(String seasonId) throws FormatException, EntityNotExistException {
        HmSeason season = seasonService.getByIdInNewTransaction(seasonId);
        List<HmLeagueMembershipScoreDO> allMembershipInfoBySeason = leagueMembershipHandler.getAllMembershipsOfActiveLeaguesInSeason(UUID.fromString(seasonId));
        log.info("assignAwardsToAllLeagueWinners" + ": count league memberships = " + allMembershipInfoBySeason.size());
        log.info("assignAwardsToAllLeagueWinners" +  ": count leagues = " + allMembershipInfoBySeason.stream().map(HmLeagueMembershipScoreDO::getLeagueId).collect(Collectors.toSet()).size());

        if (allMembershipInfoBySeason.isEmpty()) {
            log.info("assignAwardsToAllLeagueWinners" + ": no memberships found return");
            return null;
        }
        // start date summer break = (real) = end date of last round in season should be in the same year of the season end
        LocalDateTime startDateSummerBreak = seasonService.getStartDateSummerBreak();
        if (Objects.isNull(startDateSummerBreak) || startDateSummerBreak.getYear() != season.getEndDate().getYear()) {
            log.info("assignAwardsToAllLeagueWinners" + ": startDateSummerBreak is null or outdated");
            return null;
        }

        // The league winner is the member with the highest score.
        // By equality then the member with lower count of scored round. By Equality then the member with higher squad value at last round end
        Comparator<HmLeagueMembershipScoreDO> compareByScoreThenCountScoredRoundsThenSquadValue = Comparator.comparingInt(HmLeagueMembershipScoreDO::getScore)
                // reverse the order by multiplying by - 1 to get the member with the lowest scored rounds
                .thenComparingInt(membershipScoreDO -> -countUserRoundScores(membershipScoreDO.getUserId(), membershipScoreDO.getLeagueId()))
                .thenComparingInt(membershipScoreDO -> teamService.getSquadValueAtDate(membershipScoreDO.getUserId(), membershipScoreDO.getLeagueId(), startDateSummerBreak));
        return allMembershipInfoBySeason.stream()
                .collect(Collectors.groupingBy(
                        HmLeagueMembershipScoreDO::getLeagueId,
                        Collectors.collectingAndThen(
                                Collectors.maxBy(compareByScoreThenCountScoredRoundsThenSquadValue),
                                membershipScoreDO -> membershipScoreDO.orElse(null)
                        )
                ));
    }

    private Optional<HmLeagueMemberAwardsDO> assignLeagueWinnerAwardToUser(HmAward award, UUID leagueId, HmUserProfile user, HmAwardDescriptionDO awardDescription) {
        try {
            return awardService.assignLeagueWinnerAward(leagueId, award, user, awardDescription);
        } catch (Exception e) {
            String message = "Failed to assign league winner award of league [" + leagueId + "] to user [" + user.getId() + "]";
            log.error(message, e);
            logMessageService.logException("assignAwardsToAllLeagueWinners", message, e);
            return empty();
        }
    }

    private int countUserRoundScores(UUID userId, UUID leagueId) {
        int size = Util.toStream(userRoundScoreRepository.findByUserIdAndLeagueId(userId, leagueId))
                .filter(urs -> nonNull(urs.getScore())).toList().size();
        log.info("count user round score by user[" + userId + "] and league[" + leagueId + "] = [" + size + "]");
        return size;
    }
}
