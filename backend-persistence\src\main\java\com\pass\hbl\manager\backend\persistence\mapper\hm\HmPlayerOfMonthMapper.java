package com.pass.hbl.manager.backend.persistence.mapper.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.PlayerOfMonthDto;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmPlayerOfMonth;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import com.pass.hbl.manager.backend.persistence.mapper.hm.converters.PlayerStatisticsAggregator;
import com.pass.hbl.manager.backend.persistence.util.ParameterDefaults;
import org.modelmapper.AbstractConverter;
import org.modelmapper.Converter;
import org.modelmapper.TypeMap;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.pass.hbl.manager.backend.persistence.util.Constants.HBL_IMAGE_URL;

@Component
public class HmPlayerOfMonthMapper extends AbstractMapper<HmPlayerOfMonth, PlayerOfMonthDto> {

    private final PlayerStatisticsAggregator playerStatisticsAggregator;


    public HmPlayerOfMonthMapper(PlayerStatisticsAggregator playerStatisticsAggregator) {
        super(HmPlayerOfMonth.class, PlayerOfMonthDto.class);
        this.playerStatisticsAggregator = playerStatisticsAggregator;
    }

    @Override
    protected void customizeInit() {
        TypeMap<HmPlayerOfMonth, PlayerOfMonthDto> e2d = getModelMapper().createTypeMap(HmPlayerOfMonth.class, PlayerOfMonthDto.class);

        e2d.addMappings(mapper -> mapper.map(entity -> entity.getSeason().getId(), PlayerOfMonthDto::setSeasonId));
        e2d.addMappings(mapper -> mapper.map(entity -> entity.getPlayer().getId(), PlayerOfMonthDto::setPlayerId));
        e2d.addMappings(mapper -> mapper.map(entity -> entity.getPlayer().getFirstName(), PlayerOfMonthDto::setFirstName));
        e2d.addMappings(mapper -> mapper.map(entity -> entity.getPlayer().getLastName(), PlayerOfMonthDto::setLastName));
        e2d.addMappings(mapper -> mapper.map(entity -> entity.getPlayer().getPicture(), PlayerOfMonthDto::setPicture));
        e2d.addMappings(mapper -> mapper.map(entity -> entity.getPlayer().getPosition(), PlayerOfMonthDto::setPosition));
        e2d.addMappings((mapper -> mapper.map(entity -> entity.getPlayer().getHblImageId(), PlayerOfMonthDto::setHblImageId)));
    }

    @Override
    protected PlayerOfMonthDto customizeMapToDto(PlayerOfMonthDto playerOfMonthDto, HmPlayerOfMonth hmPlayerOfMonth, Map<String, Object> context) {
        int monthNumber = hmPlayerOfMonth.getMonthNumber();
        playerStatisticsAggregator.setPlayerOfMonthStatistics(playerOfMonthDto, monthNumber, hmPlayerOfMonth.getPlayer().getPosition());
        return super.customizeMapToDto(playerOfMonthDto, hmPlayerOfMonth, context);
    }
}
