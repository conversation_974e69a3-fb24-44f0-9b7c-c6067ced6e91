package com.pass.hbl.manager.backend.persistence.util;

import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.support.CronExpression;

import javax.validation.constraints.NotNull;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;

import static com.pass.hbl.manager.backend.persistence.util.Constants.*;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

@Slf4j
public class Util {

    public static <T> Stream<T> toStream(Iterable<T> iterable) {
        if (iterable == null) {
            return Stream.empty();
        }
        return StreamSupport.stream(iterable.spliterator(), false);
    }

    public static <T> Stream<T> toParallelStream(Iterable<T> iterable) {
        if (iterable == null) {
            return Stream.empty();
        }
        return StreamSupport.stream(iterable.spliterator(), true);
    }

    public static double toDoubleWithDecimals(double input, int decimalPlaces) {
        return decimalPlaces >= 0 ? new BigDecimal(input).setScale(decimalPlaces, RoundingMode.HALF_UP).doubleValue() : input;
    }

    /**
     * Transform an exception to a string including the stack trace.
     *
     * @param e the exception to transform
     * @return the transformed exception as string
     */
    public static String toString(Throwable e) {
        if (e == null) {
            return null;
        }
        StringWriter stringWriter = new StringWriter();
        e.printStackTrace(new PrintWriter(stringWriter));
        return stringWriter.toString();
    }

    /**
     * Helper for throwing exceptions within stream operations.
     *
     * @param e   the exception to throw
     * @param <E> the exception class
     * @throws E the exception
     */
    @SuppressWarnings("unchecked")
    public static <E extends Throwable> void sneakyThrow(Throwable e) throws E {
        //noinspection unchecked
        throw (E) e;
    }

    @SuppressWarnings("BooleanMethodIsAlwaysInverted")
    public static boolean isValidUUID(String id) {
        try {
            //noinspection ResultOfMethodCallIgnored
            UUID.fromString(id);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public static UUID convertId(String id) throws FormatException {
        try {
            return UUID.fromString(id);
        } catch (Exception e) {
            throw new FormatException("ID is no UUID: " + id, id, UUID.class);
        }
    }

    public static String toCronExpression(@NotNull LocalDateTime dateTime) {
        String second = String.valueOf(dateTime.getSecond());
        String minute = String.valueOf(dateTime.getMinute());
        String hour = String.valueOf(dateTime.getHour());
        String day = String.valueOf(dateTime.getDayOfMonth());
        String month = String.valueOf(dateTime.getMonth()).toUpperCase().substring(0, 3);
        return second + " " + minute + " " + hour + " " + day + " " + month + " " + "?";
    }

    public static String convertUtcToLocalCron(@NotNull String utcCron) throws FormatException {
        try {
            LocalDateTime utcLocalDateTime = CronExpression.parse(utcCron).next(LocalDateTime.now());
            assert utcLocalDateTime != null;
            return toCronExpression(toLocalDateTime(utcLocalDateTime));
        } catch (Exception e) {
            throw new FormatException("Cron expression is not valid: " + utcCron, utcCron, String.class);
        }
    }

    public static LocalDateTime getNextCronExecutionTime(@NotNull String utcCron) throws FormatException {
        return getNextCronExecutionTime(utcCron, LocalDateTime.now());
    }

    public static LocalDateTime getNextCronExecutionTime(@NotNull String utcCron, LocalDateTime timestamp) throws FormatException {
        try {
            return CronExpression.parse(utcCron).next(timestamp);
        } catch (Exception e) {
            throw new FormatException("Cron expression could not be converted to Local Datetime: " + utcCron, utcCron, String.class);
        }
    }

    public static LocalDateTime toUtc(@NotNull LocalDateTime localDateTime) {
        return localDateTime.atZone(ZoneId.systemDefault()).withZoneSameInstant(ZoneOffset.UTC).toLocalDateTime();
    }

    public static LocalDateTime toUtc(@NotNull ZonedDateTime zonedDateTime) {
        if (nonNull(zonedDateTime)) {
            return LocalDateTime.ofInstant(zonedDateTime.toInstant(), ZoneId.systemDefault());
        }
        return null;
    }

    public static LocalDateTime toLocalDateTime(@NotNull LocalDateTime utcLocalDateTime) {
        return utcLocalDateTime.atZone(ZoneOffset.UTC).withZoneSameInstant(ZoneId.systemDefault()).toLocalDateTime();
    }

    public static <T, G> String convertToString(Map<T, G> map) {
        if (map == null || map.isEmpty()) {
            return (null);
        }

        return (map.entrySet().stream()
                .map(entry -> String.join("=", entry.getKey().toString(), entry.getValue().toString()))
                .collect(Collectors.joining(";")));
    }

    public static Map<String, String> convertToMap(String parameters) {
        if (StringUtils.isEmpty(parameters)) {
            return Collections.emptyMap();
        }
        HashMap<String, String> map = new HashMap<>();
        Arrays.stream(parameters.split(";"))
                .filter(StringUtils::isNotEmpty)
                .forEach(s -> {
                    String[] tokens = s.split("=");
                    if (tokens.length > 1) {
                        String key = tokens[0] == null ? "" : tokens[0].trim();
                        String value = tokens[1] == null ? "" : tokens[1].trim();
                        map.put(key, value);
                    }
                });
        return map;
    }

    public static <T> List<T> concatLists(List<T> list1, List<T> list2) {
        List<T> newList = new ArrayList<>();
        if (nonNull(list1)) newList.addAll(list1);
        if (nonNull(list2)) newList.addAll(list2);
        return newList;
    }

    @SafeVarargs
    public static <T> Set<T> mergeSets(Set<T>... sets) {
        if (sets.length == 0) {
            return Collections.emptySet();
        }
        HashSet<T> result = new HashSet<>();
        for (Set<T> set : sets) {
            if (CollectionUtils.isNotEmpty(set)) {
                result.addAll(set);
            }
        }
        return result;
    }

    public static <T> List<T> concatLists(List<T> list1, List<T> list2, List<T> list3) {
        List<T> newList = new ArrayList<>();
        if (nonNull(list1)) newList.addAll(list1);
        if (nonNull(list2)) newList.addAll(list2);
        if (nonNull(list3)) newList.addAll(list3);
        return newList;
    }

    public static <K, V> Map<K, V> concatMaps(Map<K, V> map1, Map<K, V> map2) {
        Map<K, V> newMap = new HashMap<>();
        if (nonNull(map1)) newMap.putAll(map1);
        if (nonNull(map2)) newMap.putAll(map2);
        return newMap;
    }

    public static String formatNumberWithSeparator(String number) {
        if (number == null || number.isEmpty()) {
            return "";
        }
        String formatted = number.replaceAll("(\\d)(?=(\\d{3})+$)", "$1.");
        return formatted.contains(".") ? formatted : number;
    }

    public static Locale getLocaleByLanguageTagOrDefault(String languageTag) {
        if (languageTag != null && !languageTag.isEmpty()) {
            return Locale.forLanguageTag(languageTag);
        }
        return Constants.DEFAULT_LOCALE;
    }

    public static Locale getLocaleByLanguageTag(String languageTag) throws FormatException {
        if (languageTag != null && !languageTag.isEmpty()) {
            return Locale.forLanguageTag(languageTag);
        }
        throw new FormatException("Invalid language tag", languageTag, Locale.class);
    }

    public static String getActiveProfile(Environment environment) {
        String[] activeProfiles = environment.getActiveProfiles();
        if (Arrays.asList(activeProfiles).contains(DEV_PROFILE)) {
            return DEV_PROFILE;
        } else if (Arrays.asList(activeProfiles).contains(STAGE_PROFILE)) {
            return STAGE_PROFILE;
        } else if (Arrays.asList(activeProfiles).contains(STAGE2_PROFILE)) {
            return STAGE2_PROFILE;
        } else if (Arrays.asList(activeProfiles).contains(STAGE3_PROFILE)) {
            return STAGE3_PROFILE;
        } else if (Arrays.asList(activeProfiles).contains(PROD_PROFILE)) {
            return PROD_PROFILE;
        } else if (Arrays.asList(activeProfiles).contains(PROD2_PROFILE)) {
            return PROD2_PROFILE;
        } else if (Arrays.asList(activeProfiles).contains(PROD3_PROFILE)) {
            return PROD3_PROFILE;
        } else if (Arrays.asList(activeProfiles).contains(PROD4_PROFILE)) {
            return PROD4_PROFILE;
        } else if (Arrays.asList(activeProfiles).contains(PROD5_PROFILE)) {
            return PROD5_PROFILE;
        } else {
            return LOCAL_PROFILE;
        }
    }

    public static String getAppIcon(Environment environment) {
        switch (getActiveProfile(environment)) {
            case PROD_PROFILE, PROD2_PROFILE, PROD3_PROFILE, PROD4_PROFILE, PROD5_PROFILE -> {
                return HBL_APP_ICON_PROD;
            }
            case STAGE_PROFILE ,STAGE2_PROFILE ,STAGE3_PROFILE-> {
                return HBL_APP_ICON_STAGE;
            }
        }
        return HBL_APP_ICON_DEV;
    }

    @SafeVarargs
    public static <T> boolean equalsOneOf(T object, T... list) {
        for(T other : list) {
            if (Objects.equals(object, other)) {
                return true;
            }
        }
        return false;
    }

    public static String getUrlMatcher(Environment environment, String url, boolean regEx) {
        return environment.getProperty("server.servlet.context-path") + (regEx ? "/.*" : "/**") + (url == null ? "" : url) + (regEx ? "/.*" : "/**");
    }

    /**
     * Gets a language tag from a given locale and converts an ISO-3 language tag to ISO-2 if necessary.
     *
     * @param locale given locale
     * @return ISO-2 language tag from a given locale.
     */
    public static String getLanguageTag(Locale locale) {
        if (isNull(locale)) {
            return null;
        } else if (locale.toLanguageTag().chars().count() == 2) {
            return locale.toLanguageTag().toUpperCase();
        } else if (locale.toLanguageTag().chars().count() == 3) {
            return iso3ToIso2Code(locale.toLanguageTag()).toUpperCase();
        } else {
            log.warn("Unable to get ISO-2 code from language tag: " + locale.toLanguageTag());
            return null;
        }
    }

    public static String convertToIso2LanguageTag(String iso3LanguageTag) throws FormatException {
        return Util.getLanguageTag(Util.getLocaleByLanguageTag(iso3LanguageTag));
    }

    private static Map<String, Locale> createCountryCodeMapping() {
        String[] countries = Locale.getISOCountries();
        Map<String, Locale> localeMap = new HashMap<>(countries.length);
        for (String country : countries) {
            Locale locale = new Locale("", country);
            localeMap.put(locale.getISO3Country().toUpperCase(), locale);
        }
        return localeMap;
    }

    private static String iso3ToIso2Code(String iso3Code) {
        return createCountryCodeMapping().get(iso3Code.toUpperCase()).getCountry();
    }

    public static ZonedDateTime toZonedDateTime(LocalDateTime localDateTime) {
        // TODO @Hind: when using TimeZone.getDefault() don't you use always servers time? Do we need the client TZ?
        return isNull(localDateTime) ? null : ZonedDateTime.of(localDateTime, TimeZone.getDefault().toZoneId());
    }

    public static LocalDateTime toLocalDateTime(ZonedDateTime zonedDateTime) {
        return isNull(zonedDateTime) ? null : zonedDateTime.toLocalDateTime();
    }

    public static Integer toInteger(String s) throws FormatException {
        if (StringUtils.isEmpty(s)) {
            return null;
        }
        if (StringUtils.isNumeric(s)) {
            return Integer.parseInt(s);
        }
        throw new FormatException("Value is no integer", s, Util.class);
    }

    public static boolean toBoolean(String s) {
        if (StringUtils.isEmpty(s)) {
            return false;
        }
        if (StringUtils.isNumeric(s)) {
            return Integer.parseInt(s) > 0;
        }
        return Boolean.parseBoolean(s);
    }

    public static String stripSportradarId(String e) {
        return e == null ? null : e.toLowerCase().replaceAll("[a-z_:]*:", "");
    }

    public static String getSystemDisplayName() {
        return "START7";
    }

    public static LocalDateTime toLocalDateTime(String s) throws FormatException {
        String pattern = "yyyy-MM-dd HH:mm:ss";
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
            return LocalDateTime.parse(s, formatter);
        } catch (Exception e) {
            throw new FormatException("Value is not valid LocalDateTime with format: " + pattern, s, Util.class);
        }
    }

    public static Duration max(Duration a, Duration b) {
        return a.compareTo(b) >= 0 ? a : b;
    }

    public static boolean isLocalEnvironment(String[] activeProfiles) {
        return Arrays.asList(activeProfiles).contains(LOCAL_PROFILE);
    }

    public static LocalDateTime roundToNearest30Or00WithHourBuffer(LocalDateTime referenceDate) {

        // Add 1 hour to the given time
        LocalDateTime adjustedDateTime = referenceDate.plusHours(1);

        // Get the current minutes
        int minutes = adjustedDateTime.getMinute();

        // Round to nearest 00 or 30 minutes
        if (minutes < 15) {
            adjustedDateTime = adjustedDateTime.withMinute(0);
        } else if (minutes >= 15 && minutes < 45) {
            adjustedDateTime = adjustedDateTime.withMinute(30);
        } else {
            adjustedDateTime = adjustedDateTime.plusHours(1).withMinute(0);
        }

        // Set seconds and nanoseconds to 0 for clean output
        adjustedDateTime = adjustedDateTime.withSecond(0).withNano(0);

        // Ensure the time difference is at least 1 hour (skipped since it could lead to 2 hours difference)
        /*if (Duration.between(referenceDate, adjustedDateTime).toMinutes() < 60) {
            adjustedDateTime = adjustedDateTime.plusHours(1);
        }*/
        return adjustedDateTime;
    }

    public static boolean isAtLeastOneHourLater(LocalDateTime date1, LocalDateTime date2) {
        // Check if date2 is after date1
        if (date2.isAfter(date1)) {
            // Calculate the difference in time
            Duration duration = Duration.between(date1, date2);
            // Check if the duration is at least 1 hour (3600 seconds)
            return duration.toHours() >= 1;
        }
        return false;
    }
}
