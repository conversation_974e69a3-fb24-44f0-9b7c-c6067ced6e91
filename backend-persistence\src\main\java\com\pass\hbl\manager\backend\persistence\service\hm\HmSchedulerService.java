package com.pass.hbl.manager.backend.persistence.service.hm;

import com.pass.hbl.manager.backend.persistence.domain.hm.HmJobCronExpressionDO;
import com.pass.hbl.manager.backend.persistence.dto.SchedulerJobDto;
import com.pass.hbl.manager.backend.persistence.dto.SchedulerJobMode;
import com.pass.hbl.manager.backend.persistence.dto.hm.NotificationTopic;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmRound;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmSchedulerJob;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.mapper.hm.HmSchedulerJobMapper;
import com.pass.hbl.manager.backend.persistence.repository.AbstractSchedulerJobTerminationRepository;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmSchedulerJobRepository;
import com.pass.hbl.manager.backend.persistence.service.AbstractSchedulerService;
import com.pass.hbl.manager.backend.persistence.service.admin.LogMessageService;
import com.pass.hbl.manager.backend.persistence.service.shared.TransactionHandler;
import com.pass.hbl.manager.backend.persistence.util.Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.LockModeType;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.pass.hbl.manager.backend.persistence.util.Constants.*;


@Slf4j
@Service
@Transactional
public class HmSchedulerService extends AbstractSchedulerService<HmSchedulerJob, HmSchedulerJobRepository, HmSchedulerJobMapper> {

    private final SeasonService seasonService;

    private final TransactionHandler transactionHandler;

    public HmSchedulerService(HmSchedulerJobRepository repository,
                              AbstractSchedulerJobTerminationRepository terminationRepository,
                              ThreadPoolTaskScheduler executor,
                              HmSchedulerJobMapper mapper,
                              LogMessageService logMessageService,
                              ApplicationContext context,
                              @Lazy SeasonService seasonService,
                              Environment environment,
                              TransactionHandler transactionHandler) {
        super(repository, terminationRepository, mapper, context, logMessageService, executor, HmSchedulerJob.class, environment);
        this.seasonService = seasonService;
        this.transactionHandler = transactionHandler;
    }

    public void initRoundResultSchedule(HmRound currentRound) {
        UUID roundId = currentRound.getId();
        Map<String, String> map = Map.of(ROUND_ID, roundId.toString());
        String parameter = Util.convertToString(map);

        Optional<HmSchedulerJob> roundResultSchedulerJobOpt = getSchedulerJobByNameAndParameterStartsWith(ROUND_RESULT_JOB, parameter);
        if (roundResultSchedulerJobOpt.isEmpty()) {
            scheduleRoundResultJob(roundId, currentRound.getTo(), false);
        }

        Optional<HmSchedulerJob> tempRoundResultSchedulerJobOpt = getSchedulerJobByNameAndParameterStartsWith(ROUND_RESULT_UPDATE_JOB, parameter);
        if (tempRoundResultSchedulerJobOpt.isEmpty()) {
            scheduleRoundResultUpdateJob(roundId, currentRound.getTo());
        }
    }

    public void scheduleRoundResultJob(String roundId) throws EntityNotExistException, FormatException {
        HmRound round = seasonService.getRound(roundId);
        scheduleRoundResultJob(round.getId(), round.getTo(), false);
    }

    public void scheduleRoundResultUpdateJob(String roundId) throws EntityNotExistException, FormatException {
        HmRound round = seasonService.getRound(roundId);
        scheduleRoundResultUpdateJob(round.getId(), round.getTo());
    }

    public void scheduleRoundResultJob(UUID roundId, LocalDateTime roundEnd, boolean autoRerun) {
        //First round result should be executed at round end
        scheduleRoundResultJob(roundId, roundEnd, ROUND_RESULT_JOB, autoRerun);
    }

    public void scheduleRoundResultNotificationJob(NotificationTopic topic, LocalDateTime notificationTime) {
        //Round result notification job should be executed at the given notification time
        scheduleRoundResultNotificationJob(topic, notificationTime, ROUND_RESULT_NOTIFICATION_JOB);
    }

    public void scheduleRoundResultUpdateJob(UUID roundId, LocalDateTime roundEnd) {
        //Round result update should be executed 24 h after the end of the round
        scheduleRoundResultJob(roundId, roundEnd.plusHours(24), ROUND_RESULT_UPDATE_JOB, false);
    }

    public void scheduleLeagueScoreAwardsJob(UUID roundId, LocalDateTime roundEnd) {
        //League score awards should be executed 25 h after the end of the round
        scheduleRoundResultJob(roundId, roundEnd.plusHours(25), LEAGUE_SCORE_AWARDS_JOB, false);
    }

    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public boolean existsByRoundResultJobNameAndRoundId(String jobName, UUID roundId, boolean autoRerun) {
        String parameters = Util.convertToString(getRoundResultJobParameterMap(roundId, autoRerun));
        return existsByNameAndParameters(jobName, parameters);
    }

    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public List<HmJobCronExpressionDO> getOpenJobsByRoundResultJobNameAndRoundId(String jobName, UUID roundId) {
        String parameters = Util.convertToString(getRoundResultJobParameterMap(roundId, false));
        return getOpenJobsByRoundResultJobNameAndRoundId(jobName, parameters);
    }

    private void scheduleRoundResultJob(UUID roundId, LocalDateTime executionTime, String jobName, boolean autoRerun) {

        Optional<HmSchedulerJob> template = getOneTimeJobByNameIncludingDeletedReadOnly(jobName);
        log.info("scheduleRoundResultJob: template for job" + jobName + " is present = [" + template.isPresent() + "]");
        if (template.isPresent()) {
            HmSchedulerJob initialJob = template.get();
            if (isNotCronJob(initialJob)) {
                return;
            }

            log.info("Scheduling job " + initialJob.getName() + " [" + initialJob.getJobClass() + "] to  {" + executionTime + "}");
            Map<String, String> parameters = getRoundResultJobParameterMap(roundId, autoRerun);
            scheduleOneTimeJobs(template.get(), List.of(Pair.of(executionTime, parameters)));
        }
    }

    private void scheduleRoundResultNotificationJob(NotificationTopic topic, LocalDateTime executionTime, String jobName) {

        Optional<HmSchedulerJob> template = getOneTimeJobByNameIncludingDeletedReadOnly(jobName);
        log.info("scheduleRoundResultNotificationJob: template for job" + jobName + " is present = [" + template.isPresent() + "]");
        if (template.isPresent()) {
            HmSchedulerJob initialJob = template.get();
            if (isNotCronJob(initialJob)) {
                return;
            }

            log.info("Scheduling job " + initialJob.getName() + " [" + initialJob.getJobClass() + "] to  {" + executionTime + "}");
            Map<String, String> parameters = Map.of(TOPIC_NAME, topic.name());
            scheduleOneTimeJobs(template.get(), List.of(Pair.of(executionTime, parameters)));
        }
    }

    @NotNull
    private Map<String, String> getRoundResultJobParameterMap(UUID roundId, boolean autoRerun) {
        if (autoRerun) {
            return Map.of(ROUND_ID, roundId.toString(), AUTO_RERUN, Boolean.toString(autoRerun));
        } else {
            return Map.of(ROUND_ID, roundId.toString());
        }
    }

    @Transactional(propagation = Propagation.MANDATORY)
    public void removeTransferMarketSchedule(UUID transferMarketId, String jobName) {
        Map<String, String> map = Map.of(TRANSFER_MARKET_ID, transferMarketId.toString());
        //UUID jobId = transactionHandler.runInNewTransactionReadOnly(() -> getSchedulerJobByNameAndParameters(jobName, map).map(HmSchedulerJob::getId).orElse(null));
        UUID jobId = transactionHandler.runInNewTransactionReadOnly(() -> getSchedulerJobByNameAndParametersContain(jobName, map).orElse(null));
        terminateJobSchedule(jobId, SchedulerJobMode.CRON, false);
    }

    @SuppressWarnings("UnusedReturnValue")
    public boolean scheduleTransferMarketJobs(Map<UUID, LocalDateTime> scheduleByIdMap, boolean isSystemPlayer, String jobName) {
        Optional<HmSchedulerJob> transferMarketJobOpt = getOneTimeJobByNameIncludingDeletedReadOnly(jobName);
        if (transferMarketJobOpt.isEmpty()) {
            log.warn("No template job found for " + jobName + ". Cannot add schedule");
            return false;
        }

        HmSchedulerJob template = transferMarketJobOpt.get();
        if (isNotCronJob(template)) {
            return false;
        }

        log.info("Scheduling job " + template.getName() + " [" + template.getJobClass() + "] to  {" + Util.convertToString(scheduleByIdMap) + "}");

        List<Pair<LocalDateTime, Map<String, String>>> jobs = scheduleByIdMap.entrySet().stream()
                .map(entry -> {
                    Map<String, String> parameters = Map.of(TRANSFER_MARKET_ID, entry.getKey().toString(), SYSTEM_PLAYER, Boolean.toString(isSystemPlayer));
                    LocalDateTime executionTime = entry.getValue();
                    return Pair.of(executionTime, parameters);
                })
                .collect(Collectors.toList());

        return scheduleOneTimeJobs(template, jobs);
    }

    public Optional<HmSchedulerJob> getOneTimeJobByNameIncludingDeletedReadOnly(String jobName) {
        return transactionHandler.runInNewTransactionReadOnly(() -> getOneTimeJobByNameIncludingDeleted(jobName));
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true)
    public List<SchedulerJobDto> getRoundResultJobStatus(String roundId) {
        return getByNameAndParametersLike(ROUND_RESULT_JOB, roundId);
    }

    @Lock(LockModeType.READ)
    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true)
    public Optional<LocalDateTime> getConsistencyCheckJobNextExecutionTime() {
        Optional<HmJobCronExpressionDO> cronExpressionByIdOptional = getCronExpressionsByJobName(CONSISTENCY_CHECK_JOB).stream().findFirst();
        if (cronExpressionByIdOptional.isPresent()) {
            try {
                return Optional.of(Util.getNextCronExecutionTime(cronExpressionByIdOptional.get().getCronExpression()));
            } catch (Exception e) {
                return Optional.empty();
            }
        } else return Optional.empty();
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public int removeEndedJobs(List<UUID> jobIds) {
        return deleteEndedJobs(jobIds);
    }

    @Transactional(readOnly = true,propagation = Propagation.REQUIRES_NEW)
    public List<String> getEndedJobIds(int limit) {
        return findEndedJobIds(limit);
    }
}