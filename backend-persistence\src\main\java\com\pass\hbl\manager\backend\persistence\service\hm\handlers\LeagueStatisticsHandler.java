package com.pass.hbl.manager.backend.persistence.service.hm.handlers;

import com.opencsv.CSVWriter;
import com.pass.hbl.manager.backend.persistence.domain.hm.LeagueStatisticsDetailDO;
import com.pass.hbl.manager.backend.persistence.domain.hm.LeagueMembershipCsvExportDO;
import com.pass.hbl.manager.backend.persistence.dto.hm.LeagueStatisticsResponseDto;
import com.pass.hbl.manager.backend.persistence.dto.hm.LeagueStatisticsItemDto;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmClientRequest;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.exception.InvalidOperationException;
import com.pass.hbl.manager.backend.persistence.exception.RateLimitExceededException;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmClientRequestRepository;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmLeagueRepository;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmLeagueMembershipRepository;
import com.pass.hbl.manager.backend.persistence.service.admin.ParameterService;
import com.pass.hbl.manager.backend.persistence.service.hm.SeasonService;
import com.pass.hbl.manager.backend.persistence.util.ParameterDefaults;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.StringWriter;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.pass.hbl.manager.backend.persistence.util.Constants.SYSTEM_USERNAME;
import static java.util.Objects.isNull;

@Service
@Slf4j
@Transactional
public class LeagueStatisticsHandler {

    private static final DateTimeFormatter CSV_DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");

    private final HmLeagueRepository leagueRepository;
    private final HmLeagueMembershipRepository leagueMembershipRepository;
    private final ParameterService parameterService;
    private final HmClientRequestRepository clientRequestRepository;
    private final SeasonService seasonService;
    private final RateLimitingHandler rateLimitHandler;

    @Getter
    private int maxStatPageSize = ParameterDefaults.DEFAULT_MAX_LEAGUE_STAT_PAGE_SIZE;
    @Getter
    private int rateLimitMinutes = ParameterDefaults.DEFAULT_PARAM_LEAGUE_STAT_RATE_LIMIT_MINUTES;
    @Getter
    private boolean rateLimitingActive = ParameterDefaults.DEFAULT_PARAM_LEAGUE_STAT_RATE_LIMITING_ACTIVE;
    @Getter
    private int forceRefreshCacheAfterMinutes = ParameterDefaults.DEFAULT_PARAM_FORCE_REFRESH_CACHE_AFTER_MINUTES;

    /**
     * Cache for league IDs to support pagination = Triple <left,Middle,right> represents the league ids who have
     * performed any change (related to the requested data structure e.g. joined leagues) in this time frame.
     * right -> IDs
     * middle -> changeStart e.g. 12:00 (requested date)
     * left -> changeEnd e.g 12h30 (cache update date, client request sent at)
     */
    private Triple<LocalDateTime, LocalDateTime, List<UUID>> cacheData = Triple.of(null, null, new ArrayList<>());
    private int cacheLeagueCount = 0;

    public LeagueStatisticsHandler(HmLeagueRepository leagueRepository,
                                 HmLeagueMembershipRepository leagueMembershipRepository,
                                 ParameterService parameterService,
                                 HmClientRequestRepository clientRequestRepository,
                                 SeasonService seasonService,
                                 RateLimitingHandler rateLimitHandler) {
        this.leagueRepository = leagueRepository;
        this.leagueMembershipRepository = leagueMembershipRepository;
        this.parameterService = parameterService;
        this.clientRequestRepository = clientRequestRepository;
        this.seasonService = seasonService;
        this.rateLimitHandler = rateLimitHandler;
    }

    @Transactional(readOnly = true)
    public LeagueStatisticsResponseDto getAllLeagueStatistics(LocalDateTime changedAfter, Pageable pageable, String requestUrl, String externalClient) throws InvalidOperationException, FormatException, RateLimitExceededException, EntityNotExistException {

        log.info("getAllLeagueStatistics: changedAfter: {}, pageable: {}, client: {}", changedAfter, pageable, externalClient);

        boolean isRateLimitingActive = parameterService.getAsBoolean( ParameterDefaults.PARAM_LEAGUE_STAT_RATE_LIMITING_ACTIVE, ParameterDefaults.DEFAULT_PARAM_LEAGUE_STAT_RATE_LIMITING_ACTIVE, SYSTEM_USERNAME);

        Optional<HmClientRequest> existingRequestOpt = clientRequestRepository.findByRequestAndExternalClient(requestUrl, externalClient);

        if (isRateLimitingActive) {
            rateLimitHandler.checkRateLimit(changedAfter, pageable, requestUrl, externalClient, existingRequestOpt, rateLimitMinutes);
        }

        maxStatPageSize = parameterService.getAsInteger(ParameterDefaults.PARAM_MAX_LEAGUE_STAT_PAGE_SIZE, ParameterDefaults.DEFAULT_MAX_LEAGUE_STAT_PAGE_SIZE, SYSTEM_USERNAME);

        rateLimitMinutes = parameterService.getAsInteger(ParameterDefaults.PARAM_LEAGUE_STAT_RATE_LIMIT_MINUTES, ParameterDefaults.DEFAULT_PARAM_LEAGUE_STAT_RATE_LIMIT_MINUTES, SYSTEM_USERNAME);

        forceRefreshCacheAfterMinutes = parameterService.getAsInteger(ParameterDefaults.PARAM_FORCE_REFRESH_CACHE_AFTER_MINUTES, ParameterDefaults.DEFAULT_PARAM_FORCE_REFRESH_CACHE_AFTER_MINUTES, SYSTEM_USERNAME);

        LocalDateTime now = LocalDateTime.now();
        boolean forceRefreshCache = !isNull(cacheData.getLeft()) && now.isAfter(cacheData.getLeft().plusMinutes(forceRefreshCacheAfterMinutes));

        // Check if we need to refresh the cache based on multiple conditions:
        // 1. Cache hasn't been initialized yet (cacheData.getLeft() == null) -> first time refreshing cache
        // 2. Cache hasn't been initialized yet (cacheData.getRight() == null) -> User Ids list is empty
        // 3. Filter parameter changed (cacheData.getMiddle() != changedAfter) -> changeStart-date was changed
        // 4. forceRefreshCache if "forceRefreshCacheAfterMinutes" minutes passed -> Cache expiration time (in minutes)
        boolean refreshCache = cacheData.getLeft() == null || cacheData.getRight() == null || !Objects.equals(cacheData.getMiddle(), changedAfter) || forceRefreshCache;

        if (refreshCache) {
            log.info("Refreshing league statistics cache with changedAfter: {}", changedAfter);

            UUID currentSeasonId = seasonService.getCurrentSeason().getId();
            List<UUID> leagueIds;
            if (isNull(changedAfter)) {
                leagueIds = leagueRepository.findAllIdsBySeasonId(currentSeasonId);
            } else {
                leagueIds = leagueRepository.findBySeasonIdAndChangedAfter(changedAfter,currentSeasonId);
            }
            cacheData = Triple.of(LocalDateTime.now(), changedAfter, leagueIds);
            cacheLeagueCount = leagueRepository.countBySeasonId(currentSeasonId);

            log.info("Found {} leagues in current season modified after: {}", leagueIds.size(), changedAfter);
        }

        // If pageable size is > maxStatPageSize, use maxStatPageSize instead
        int pageSize = pageable.getPageSize() > maxStatPageSize ? maxStatPageSize : pageable.getPageSize();
        int pageNumber = pageable.getPageNumber();

        // Calculate pagination
        int startIndex = pageNumber * pageSize;
        int endIndex = Math.min(startIndex + pageSize, cacheData.getRight().size());

        // Check if the requested page is valid
        if (startIndex >= cacheData.getRight().size()) {
            return new LeagueStatisticsResponseDto(cacheLeagueCount, Collections.emptyList()); // Return empty list if page is out of bounds
        }

        List<UUID> pageLeagueIds = cacheData.getRight().subList(startIndex, endIndex);

        List<LeagueStatisticsDetailDO> leagueDetails = leagueRepository.findDetailByIdIn(pageLeagueIds);

        List<LeagueStatisticsItemDto> leagueItems = leagueDetails.stream()
                .map(detail -> new LeagueStatisticsItemDto(
                        detail.getId().toString(),
                        detail.getName(),
                        detail.getTotalMemberCount()
                ))
                .collect(Collectors.toList());

        return new LeagueStatisticsResponseDto(cacheLeagueCount, leagueItems);
    }

    @Transactional(readOnly = true)
    public String exportLeagueMembershipDataAsCsv() throws EntityNotExistException {
        log.info("exportLeagueMembershipDataAsCsv: Starting CSV export for league membership data");

        UUID currentSeasonId = seasonService.getCurrentSeason().getId();
        List<LeagueMembershipCsvExportDO> membershipData = leagueMembershipRepository.findAllMembershipDataForCsvExport(currentSeasonId);

        log.info("exportLeagueMembershipDataAsCsv: Found {} membership records for export", membershipData.size());

        return convertMembershipDataToCSV(membershipData);
    }

    private String convertMembershipDataToCSV(List<LeagueMembershipCsvExportDO> membershipData) {
        try (StringWriter writer = new StringWriter();
             CSVWriter csvWriter = new CSVWriter(writer)) {

            // CSV header with required fields in specified order
            String[] header = {"SSO_ID", "score", "balance", "rank", "league_id", "league_name", "sso_owner_id", "joined_at", "league_created_at"};
            csvWriter.writeNext(header);

            // Calculate ranks within each league
            Map<String, List<LeagueMembershipCsvExportDO>> membershipByLeague = membershipData.stream()
                    .collect(Collectors.groupingBy(LeagueMembershipCsvExportDO::getLeagueId));

            // Process each league separately to calculate ranks
            for (Map.Entry<String, List<LeagueMembershipCsvExportDO>> entry : membershipByLeague.entrySet()) {
                List<LeagueMembershipCsvExportDO> leagueMembers = entry.getValue();

                // Sort by score descending for rank calculation (higher score = better rank)
                leagueMembers.sort((a, b) -> Integer.compare(
                    b.getScore() != null ? b.getScore() : 0,
                    a.getScore() != null ? a.getScore() : 0
                ));

                // Assign ranks and write CSV rows
                for (int i = 0; i < leagueMembers.size(); i++) {
                    LeagueMembershipCsvExportDO member = leagueMembers.get(i);
                    int rank = i + 1; // Rank starts from 1

                    String[] data = {
                        member.getSsoId() != null ? member.getSsoId() : "",
                        member.getScore() != null ? member.getScore().toString() : "0",
                        member.getBalance() != null ? member.getBalance().toString() : "0",
                        String.valueOf(rank),
                        member.getLeagueId() != null ? member.getLeagueId() : "",
                        member.getLeagueName() != null ? member.getLeagueName() : "",
                        member.getSsoOwnerId() != null ? member.getSsoOwnerId() : "",
                        member.getJoinedAt() != null ? member.getJoinedAt().format(CSV_DATETIME_FORMATTER) : "",
                        member.getLeagueCreatedAt() != null ? member.getLeagueCreatedAt().format(CSV_DATETIME_FORMATTER) : ""
                    };
                    csvWriter.writeNext(data, false);
                }
            }

            csvWriter.flush();
            return writer.toString();
        } catch (Exception e) {
            log.error("exportLeagueMembershipDataAsCsv: Failed to convert league membership data to CSV", e);
            return null;
        }
    }
}
