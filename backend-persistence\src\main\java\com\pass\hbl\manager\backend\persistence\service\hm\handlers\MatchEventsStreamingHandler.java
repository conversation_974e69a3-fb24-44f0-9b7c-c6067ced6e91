package com.pass.hbl.manager.backend.persistence.service.hm.handlers;

import com.pass.hbl.manager.backend.persistence.dto.hm.PlayerMatchEventDto;
import com.pass.hbl.manager.backend.persistence.dto.hm.PlayerRoundScoreDetailsDto;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmRound;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmStreamingSubscription;
import com.pass.hbl.manager.backend.persistence.entity.shared.SharedLocalization;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.exception.InvalidOperationException;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmStreamingSubscriptionRepository;
import com.pass.hbl.manager.backend.persistence.service.AbstractStreamingService;
import com.pass.hbl.manager.backend.persistence.service.admin.ParameterService;
import com.pass.hbl.manager.backend.persistence.service.hm.LeagueService;
import com.pass.hbl.manager.backend.persistence.service.hm.PlayerService;
import com.pass.hbl.manager.backend.persistence.service.shared.LocalizationService;
import com.pass.hbl.manager.backend.persistence.service.shared.TransactionHandler;
import com.pass.hbl.manager.backend.persistence.util.Util;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.Nullable;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.pass.hbl.manager.backend.persistence.dto.shared.EntityType.MATCH_EVENT;
import static com.pass.hbl.manager.backend.persistence.util.Constants.DEFAULT_LOCALE;
import static com.pass.hbl.manager.backend.persistence.util.ParameterDefaults.DEFAULT_STREAMING_ACTIVE;
import static com.pass.hbl.manager.backend.persistence.util.ParameterDefaults.PARAM_STREAMING_ACTIVE;
import static java.util.Objects.isNull;

@Slf4j
@Service
@Transactional
public class MatchEventsStreamingHandler extends AbstractStreamingService<HmStreamingSubscription, PlayerRoundScoreDetailsDto> {

    @Getter
    @Setter
    private HashMap<Pair<String, String>, Pair<SseEmitter, Locale>> emitterCache;

    @Getter
    @Setter
    private HashMap<Pair<String, String>, Set<UUID>> lineupCache;

    @Getter
    @Setter
    private HashMap<Locale, Map<String, SharedLocalization>> localeCache;

    private final HmStreamingSubscriptionRepository streamingSubscriptionRepository;

    private final TransactionHandler transactionHandler;
    private final TeamLineUpHandler lineUpHandler;

    private final PlayerService playerService;
    private final LeagueService leagueService;
    private final LocalizationService localizationService;

    private final String activeProfile;
    private final AtomicLong counter;

    public MatchEventsStreamingHandler(Environment environment, HmStreamingSubscriptionRepository streamingSubscriptionRepository, ParameterService parameterService, TransactionHandler transactionHandler, @Lazy TeamLineUpHandler lineUpHandler, @Lazy PlayerService playerService, @Lazy LeagueService leagueService, @Lazy LocalizationService localizationService) {
        super(streamingSubscriptionRepository, parameterService, transactionHandler, PARAM_STREAMING_ACTIVE, DEFAULT_STREAMING_ACTIVE, true);
        this.streamingSubscriptionRepository = streamingSubscriptionRepository;
        this.transactionHandler = transactionHandler;
        this.lineUpHandler = lineUpHandler;
        this.playerService = playerService;
        this.leagueService = leagueService;
        this.localizationService = localizationService;
        emitterCache = new HashMap<>();
        lineupCache = new HashMap<>();
        localeCache = new HashMap<>();
        activeProfile = Util.getActiveProfile(environment);
        counter = new AtomicLong(0);
    }

    /* =================================================================================================================
     * Subscribe/unsubscribe methods
     * ============================================================================================================== */

    /**
     * Subscribe to live-streaming.
     *
     * @param userId the user id
     * @param leagueId the league id
     * @param response the http response for the streaming
     * @param language the language the notifications will be pushed
     * @return the emitter object
     * @throws FormatException if given ids are no UUIDs
     */
    public SseEmitter subscribeToMatchEvents(@NotNull String userId, @NotNull String leagueId, boolean isStreamingActive, LocalDateTime streamingEnd, @NotNull HttpServletResponse response, @NotNull String language, HmRound currentRound) throws FormatException, EntityNotExistException {

        try {
            validate(userId, leagueId);
        } catch (FormatException e) {
            log.error("Cannot subscribe", e);
            return null;
        }

        // if no streaming is active, return null
        if (!isStreamingEnabled() || !isStreamingActive) {
            log.info("Subscription to match events tried by userId=" + userId +", leagueId=" + leagueId + " while no streaming is active or disabled");
            return null;
        }

        if (!leagueService.isMember(Util.convertId(userId), Util.convertId(leagueId))) {
            log.warn("Cannot subscribe", new InvalidOperationException("Subscription to match events not possible for user id[" + userId + "] and league id[" + leagueId + "]", userId, "The user ist not member of the league"));
            return null;
        }

        Locale locale = initLocale(Util.getLocaleByLanguageTagOrDefault(language));
        long timeout = Duration.between(LocalDateTime.now(), streamingEnd).toMillis();
        // it might happen during testing that timeout might become negative. For this case set timeout fix to 15min
        if (timeout < 0) {
            timeout = 15000 * 60;
        }

        // if response is null it is a test subscription
        SseEmitter emitter = response == null ? null : createEmitter(userId, leagueId, response, timeout);
        Pair<String, String> mapKey = Pair.of(userId, leagueId);
        emitterCache.put(mapKey, Pair.of(emitter, locale));
        streamingSubscriptionRepository.save(new HmStreamingSubscription(Util.convertId(userId), Util.convertId(leagueId), activeProfile));

        // get lineup of user for current round and cache it until end of round, no matter if he unsubscribes
        updateLineupCache(mapKey, currentRound);

        log.info("userId[" + userId + "] subscribed to match events on leagueId[" + leagueId + "] with timeout time " + timeout + ". Current streams " + emitterCache.size());
        return emitter;
    }

    /**
     * Create a streaming emitter.
     *
     * @param userId the user id
     * @param leagueId the league id
     * @param response the web response
     * @param timeout the timeout for the subscription
     * @return the emitter
     * @throws FormatException if the given ids are no UUIDs
     */
    private SseEmitter createEmitter(@NotNull String userId, @NotNull String leagueId, @NotNull HttpServletResponse response, long timeout) throws FormatException {
        SseEmitter emitter = buildEmitter(userId, leagueId, response, timeout);
        streamingSubscriptionRepository.save(new HmStreamingSubscription(Util.convertId(userId), Util.convertId(leagueId), activeProfile));
        return emitter;
    }

    /**
     * Unsubscribe, works on any cluster node synchronized by database
     * @param userId the user id
     * @param leagueId the league id
     * @throws FormatException if the given ids are no UUIDs
     */
    public void unsubscribe(String userId, String leagueId) throws FormatException {
        validate(userId, leagueId);
        terminateEmitter(userId, leagueId, "Terminate live match events emitter by unsubscribe.", true);
    }

    /* =================================================================================================================
     * Cache update methods
     * ============================================================================================================== */

    /**
     * Cache translations for the given locale.
     *
     * @param locale the locale to cache
     */
    private Locale initLocale(Locale locale) {
        if (!localeCache.containsKey(locale)) {
            synchronized (counter) {
                if (!localeCache.containsKey(locale)) {
                    Map<String, SharedLocalization> translationMap = localizationService.getAllByEntityTypeAndLocale(MATCH_EVENT, locale).stream()
                            .collect(Collectors.toMap(SharedLocalization::getKey, Function.identity()));
                    localeCache.put(locale, translationMap);
                }
            }
        }
        return locale;
    }


    /**
     * Get lineup of user for current round and cache it until end of round, no matter if he unsubscribes.
     *
     * @param mapKey the pair with userId left and leagueId right
     * @throws FormatException should not happen, only if IDs are no UUIDs (pre-checked in public methods)
     */
    private void updateLineupCache(Pair<String, String> mapKey, HmRound currentRound) throws FormatException, EntityNotExistException {
        if (!lineupCache.containsKey(mapKey)) {
            Set<UUID> lineupForCurrentRound = lineUpHandler.getLineupPlayerIds(mapKey.getLeft(), mapKey.getRight(), currentRound.getId(), currentRound.getClosing());
            lineupCache.put(mapKey, lineupForCurrentRound);
        }
    }

    /* =================================================================================================================
     * cleanup and termination methods
     * ============================================================================================================== */

    /**
     * Terminate the emitter for the given user/league pair.
     *
     * @param userId the user id
     * @param leagueId the league id
     * @param logMessage the message to log on termination
     * @param complete if true call a complete at the emitter, otherwise terminate without connection completion.
     * @throws FormatException if given ids are no UUIDs
     */
    @Override
    public void terminateEmitter(@NotNull String userId, @NotNull String leagueId, String logMessage, boolean complete) throws FormatException {
        // check if emitter runs on this node. If yes, terminate it
        Pair<SseEmitter, Locale> pair = emitterCache.remove(Pair.of(userId, leagueId));
        if (pair != null) {
            SseEmitter emitter = pair.getLeft();
            if (complete && emitter != null) {
                try {
                    emitter.complete();
                } catch (Exception ex) {
                    log.warn("Error on completing emitter: " + ex.getMessage());
                }
            }
            log.info(logMessage + " Current streams " + emitterCache.size());
        }

        // independent on which node, remove it from database, so that other node will terminate emitter on next check
        transactionHandler.runInNewTransaction(() -> {
            try {
                streamingSubscriptionRepository.deleteByUserIdAndLeagueId(Util.convertId(userId), Util.convertId(leagueId));
            } catch (FormatException e) {
                log.error("Could not terminate emitter", e);
            }
            return null;
        });
    }

    /**
     * Terminate all emitters, cached and assigned to this node.
     */
    @Override
    public void terminateAndClearAllEmitters() {
        // use separate list to avoid ConcurrentModificationException
        List<SseEmitter> emitters = emitterCache.values().parallelStream().map(Pair::getLeft).filter(Objects::nonNull).toList();
        emitters.forEach(e -> {
            try {
                e.complete();
            } catch (Exception ex) {
                log.warn("Error completing emitter: " + ex.getMessage());
            }
        });
        emitterCache.clear();
        transactionHandler.runInNewTransaction(() -> {
            streamingSubscriptionRepository.deleteByActiveProfile(activeProfile);
            return null;
        });
        log.info("All emitters terminated. Current streams " + emitterCache.size());
    }

    /**
     * Check if cached subscribers are still in database. If they were terminated on another node, terminate the emitter.
     */
    @Override
    public boolean checkSubscribers() {
        // get all assigned subscriptions to this node from database
        Set<Pair<String, String>> registeredEmitters = streamingSubscriptionRepository.getByActiveProfile(activeProfile)
                .stream()
                .map(s -> Pair.of(s.getUserId().toString(), s.getLeagueId().toString()))
                .collect(Collectors.toSet());

        // terminate all cached emitters that were terminated on the other node
        // use separate list to avoid ConcurrentModificationException
        List<Pair<String, String>> emitters = emitterCache.keySet().stream()
                .filter(p -> !registeredEmitters.contains(p)).toList();
        emitters
                .forEach(p -> {
                    try {
                        terminateEmitter(p.getLeft(), p.getRight(), "Terminated by other node", true);
                    } catch (FormatException e) {
                        log.error("could not terminate emitter", e);
                    }
                });
        return true;
    }

    public void clearLineupCache() {
        this.lineupCache.clear();
    }

    public void clearLocaleCache() {
        this.localeCache.clear();
    }

    /* =================================================================================================================
     * Events sending methods
     * ============================================================================================================== */

    /**
     * Send out the ping event to all cached emitters.
     */
    @Override
    public void doSendPingEvent() {
        SseEmitter.SseEventBuilder event = buildPingEvent();

        // now iterate over emitters and pick players according to emitter's lineup from the precalculated results
        Set<Map.Entry<Pair<String, String>, Pair<SseEmitter, Locale>>> entries = new HashSet<>(this.emitterCache.entrySet());
        for (Map.Entry<Pair<String, String>, Pair<SseEmitter,Locale>> entry : entries) {
            String userId = entry.getKey().getLeft();
            String leagueId = entry.getKey().getRight();
            SseEmitter emitter = entry.getValue().getLeft();

            // send event
            emitEvent(userId, leagueId, emitter, event);
        }
    }

    /**
     * Do send the match events.
     */
    public void doSendMatchEvents(HmRound currentRound) {
        HashMap<Locale, Map<String, PlayerRoundScoreDetailsDto>> playerEventsByLocale = getPlayerStatisticsByLocale(currentRound);
        if (playerEventsByLocale == null || playerEventsByLocale.isEmpty()) {
            return;
        }

        // now iterate over emitters and pick players according to emitter's lineup from the precalculated results
        Set<Map.Entry<Pair<String, String>, Pair<SseEmitter, Locale>>> entries = new HashSet<>(this.emitterCache.entrySet());
        for (Map.Entry<Pair<String, String>, Pair<SseEmitter,Locale>> entry : entries) {
            String userId = entry.getKey().getLeft();
            String leagueId = entry.getKey().getRight();
            Map<String, PlayerRoundScoreDetailsDto> playerMap = playerEventsByLocale.get(entry.getValue().getRight());
            Set<String> lineup = Util.toStream(lineupCache.get(entry.getKey())).map(UUID::toString).collect(Collectors.toSet());
            SseEmitter emitter = entry.getValue().getLeft();

            // send event
            List<PlayerRoundScoreDetailsDto> lineupScores = lineup.stream().map(playerMap::get).toList();
            emitEvent(userId, leagueId, emitter, buildEvent(lineupScores));
        }
    }

    /**
     * Get the player statistics details from the database and translate it to the different languages.
     *
     * @return a map by locale with the player match statistics
     */
    @Nullable
    private HashMap<Locale, Map<String, PlayerRoundScoreDetailsDto>> getPlayerStatisticsByLocale(HmRound currentRound) {
        // in first step get the players that need to be streamed to the emitters
        List<UUID> linedUpPlayers = emitterCache.keySet().stream().map(lineupCache::get)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .distinct().collect(Collectors.toList());

        // then prepare all round scores of these players in all required languages
        HashMap<Locale, Map<String, PlayerRoundScoreDetailsDto>> playerEventsByLocale = new HashMap<>();

        // if no players are lined up return an empty set
        if (linedUpPlayers.isEmpty()) {
            playerEventsByLocale.put(DEFAULT_LOCALE, Collections.emptyMap());
            return playerEventsByLocale;
        }

        // first in default language
        try {
            List<PlayerRoundScoreDetailsDto> data = playerService.getLineupScoreDetailsForStreaming(linedUpPlayers, currentRound.getId(), DEFAULT_LOCALE);
            Map<String, PlayerRoundScoreDetailsDto> defaultPlayerScores = data.stream().collect(Collectors.toMap(p -> p.getPlayer().getId(), Function.identity()));

            playerEventsByLocale.put(DEFAULT_LOCALE, defaultPlayerScores);
        } catch (EntityNotExistException e) {
            log.error("Cannot get player round scores for locale " + DEFAULT_LOCALE + ". Cannot send events", e);
            return null;
        }

        // then the translations
        for (Locale locale : localeCache.keySet()) {
            if (locale.equals(DEFAULT_LOCALE)) {
                continue;
            }
            Collection<PlayerRoundScoreDetailsDto> lineupScoreDetailsInDefaultLocale = playerEventsByLocale.get(DEFAULT_LOCALE).values();
            Collection<PlayerRoundScoreDetailsDto> lineupScoreDetails = translate(lineupScoreDetailsInDefaultLocale, locale);
            Map<String, PlayerRoundScoreDetailsDto> map = lineupScoreDetails
                    .stream().collect(Collectors.toMap(p -> p.getPlayer().getId(), Function.identity()));
            playerEventsByLocale.put(locale, map);
        }
        return playerEventsByLocale;
    }

    /**
     * Translate the given player statistics to the given locale.
     *
     * @param inDefaultLanguage the player statistics in default locale
     * @param locale the locale to translate to
     * @return the translated player statistics
     */
    private List<PlayerRoundScoreDetailsDto> translate(Collection<PlayerRoundScoreDetailsDto> inDefaultLanguage, Locale locale) {
        // re-initialize the match events so that the translation doesn't reference to the initial objects
        List<PlayerRoundScoreDetailsDto> inTranslatedLanguage =
                inDefaultLanguage.stream().map(psd -> {
                    List<PlayerMatchEventDto> matchEvents = psd.getPlayerMatchEvents().stream().
                            map(e -> new PlayerMatchEventDto(e.getId(), e.getPlayerId(), e.getMatchId(), e.getEvent(), e.getEventCharacteristic(), e.getEventTime(), e.getScore(), e.getDescription())).toList();
                    return new PlayerRoundScoreDetailsDto(psd.getPlayer(), psd.getClub(), psd.getTotalScore(), psd.getValueGrowth(), psd.getOpponents(), matchEvents);
                }).toList();
        for (PlayerRoundScoreDetailsDto playerScoreDetails: inTranslatedLanguage) {
            playerScoreDetails.getPlayerMatchEvents().forEach(event -> {
                String key = isNull(event.getEventCharacteristic())? event.getEvent().name(): event.getEvent().name() + "_" + event.getEventCharacteristic().name();
                SharedLocalization translation = localeCache.get(locale).get(key);
                if (translation != null) {
                    event.setDescription(translation.getValue());
                }
            });
        }
        return inTranslatedLanguage;
    }

    /**
     * Build the SSE event for the given player statistics.
     *
     * @param statistics the player statistics
     * @return the event
     */
    @NotNull
    private SseEmitter.SseEventBuilder buildEvent(List<PlayerRoundScoreDetailsDto> statistics) {
        return buildEvent(statistics, counter, "Player score details");
    }
}
