version: '3.8'

services:

  nginx:
    container_name: nginx
    image: nginx:1.21.4-alpine
    user: root
    ports:
      - "80:80"
      - "443:443"
      - "4000:4000"
    volumes:
      - ./conf/nginx.conf:/etc/nginx/nginx.conf:ro
      - /etc/ssl/certs/STAR_pass-consulting_com.nginx.crt:/etc/ssl/certs/STAR_pass-consulting_com.nginx.crt:ro
      - /etc/ssl/certs/STAR_pass-consulting_com.key:/etc/ssl/certs/STAR_pass-consulting_com.key:ro
    depends_on:
      - api
      - admin-api

  admin-api:
    container_name: admin-api
    image: *************:5000/hbl-handball-manager/${BRANCH}/admin-api:latest
    user: root
    ports:
      - "8181:8181"
      - "8182:8182"
    volumes:
      - ./conf/admin-api/:/conf/:z
    environment:
      - spring_profiles_active=${SPRING_PROFILE_ACTIVE},chaos-monkey
      - SPRING_CONFIG_LOCATION=file:///conf/application.yml
    depends_on:
      - postgres

  api:
    container_name: api
    image: *************:5000/hbl-handball-manager/${BRANCH}/api:latest
    user: root
    ports:
      - "8081:8081"
      - "8082:8082"
    volumes:
      - ./conf/api/:/conf/:z
    environment:
      - spring_profiles_active=${SPRING_PROFILE_ACTIVE},chaos-monkey
      - SPRING_CONFIG_LOCATION=file:///conf/application.yml
      - WAIT_HOSTS="postgres:5432"
    depends_on:
      - postgres

  postgres:
    container_name: postgres
    image: postgres:14.1
    user: root
    volumes:
      - hbl-data:/var/lib/postgresql/data:z
      - ./infrastructure/postgres-init/:/docker-entrypoint-initdb.d/:z
    command: postgres -c shared_preload_libraries=pg_stat_statements -c pg_stat_statements.track=all -c max_connections=10000 -c max_wal_size=2GB -c deadlock_timeout=5000
    environment:
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_USER=postgres
      - POSTGRES_DB=handball_manager
      - POSTGRES_INITDB_ARGS="--deadlock_timeout=5s"
    ports:
      - "5432:5432"

volumes:
  hbl-data:
    external: true

networks:
  default:
    name: hblmanager-network
