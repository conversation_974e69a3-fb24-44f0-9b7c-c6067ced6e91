package com.pass.hbl.manager.backend.admin.config;

import com.pass.hbl.manager.backend.admin.config.security.CustomFilter;
import com.pass.hbl.manager.backend.admin.config.security.MyBasicAuthenticationEntryPoint;
import com.pass.hbl.manager.backend.persistence.util.Util;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.provisioning.InMemoryUserDetailsManager;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.www.BasicAuthenticationFilter;

import static com.pass.hbl.manager.backend.admin.util.ApiConstants.*;

@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true, jsr250Enabled = true)
public class SecurityConfig {

	private final MyBasicAuthenticationEntryPoint authenticationEntryPoint;

	private final HandballManagerAdminConfigurationProperties properties;

	public SecurityConfig(MyBasicAuthenticationEntryPoint authenticationEntryPoint, HandballManagerAdminConfigurationProperties properties) {
		this.authenticationEntryPoint = authenticationEntryPoint;
		this.properties = properties;
	}
	@Bean
	public AuthenticationManager authenticationManager(AuthenticationConfiguration authenticationConfiguration) throws Exception {
		return authenticationConfiguration.getAuthenticationManager();
	}

	@Bean
	public UserDetailsService userDetailsService() {
		UserDetails admin = User.builder()
				.username(properties.getSecurity().getAdminUser())
				.password(passwordEncoder().encode(properties.getSecurity().getAdminPassword()))
				.roles(ADMIN)
				.build();
		UserDetails writeAdmin = User.builder()
				.username(properties.getSecurity().getAdminWriteUser())
				.password(passwordEncoder().encode(properties.getSecurity().getAdminWritePassword()))
				.roles(ADMIN_WRITE)
				.build();
		return new InMemoryUserDetailsManager(admin, writeAdmin);
	}

	@Bean
	public DaoAuthenticationProvider authenticationProvider() {
		DaoAuthenticationProvider authProvider = new DaoAuthenticationProvider();
		authProvider.setUserDetailsService(userDetailsService());
		authProvider.setPasswordEncoder(passwordEncoder());
		return authProvider;
	}

	@Bean
	public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
		http.cors().and().csrf().disable();

		http.authorizeRequests()
				.anyRequest().authenticated()
				.and()
				.httpBasic()
				.authenticationEntryPoint(authenticationEntryPoint);

		http.addFilterAfter(new CustomFilter(),
				BasicAuthenticationFilter.class);
		return http.build();
	}

	@Bean
	public PasswordEncoder passwordEncoder() {
		return new BCryptPasswordEncoder();
	}
}
