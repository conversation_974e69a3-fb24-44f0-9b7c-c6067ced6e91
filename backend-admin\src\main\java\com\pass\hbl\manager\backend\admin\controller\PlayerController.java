package com.pass.hbl.manager.backend.admin.controller;

import com.pass.hbl.manager.backend.admin.config.HandballManagerAdminConfigurationProperties;
import com.pass.hbl.manager.backend.admin.util.ApiConstants;
import com.pass.hbl.manager.backend.persistence.domain.admin.AdminPlayerInfoDO;
import com.pass.hbl.manager.backend.persistence.dto.hm.PlayerDto;
import com.pass.hbl.manager.backend.persistence.dto.hm.PlayerOfMonthDto;
import com.pass.hbl.manager.backend.persistence.dto.hm.Position;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.exception.InvalidOperationException;
import com.pass.hbl.manager.backend.persistence.service.hm.PlayerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.security.RolesAllowed;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Optional;

import static com.pass.hbl.manager.backend.admin.util.ApiConstants.*;

@RestController
@RequestMapping(ApiConstants.PLAYER_API)
@Validated
@Tag(name = "player", description = "Admin API for player management")
@RolesAllowed({ROLE_ADMIN, ApiConstants.ROLE_ADMIN_WRITE})
public class PlayerController extends AbstractController {

    private final PlayerService service;

    private final HandballManagerAdminConfigurationProperties properties;

    public PlayerController(ApplicationEventPublisher eventPublisher, PlayerService service, HandballManagerAdminConfigurationProperties properties) {
        super(eventPublisher);
        this.service = service;
        this.properties = properties;
    }

    @Operation(summary = "List all available players")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Found players", content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = PlayerDto.class))}),
            @ApiResponse(responseCode = "404", description = "No players found", content = {@Content})
    })
    @GetMapping(value = "/all", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<AdminPlayerInfoDO> getAllAdminPlayerInfoDOs() {
        return service.getAllAdminPlayerInfoDOs();
    }

    @Operation(summary = "Get all player scores by round")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Found player scores", content = {@Content(mediaType = MediaType.MULTIPART_FORM_DATA_VALUE)}),
            @ApiResponse(responseCode = "404", description = "No player scores found", content = {@Content})
    })
    @GetMapping(value = "/scores/all")
    public ResponseEntity<ByteArrayResource> getAllPlayerScoresByRound(@Parameter(name = "roundNumber", schema = @Schema(allowableValues = {"1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34"}))
                                                                       @RequestParam(name = "roundNumber")
                                                                       @NotNull String roundNumber, @Parameter(name = "specialRound")
                                                                       @RequestParam(name = "specialRound") @NotNull Boolean specialRound) throws EntityNotExistException {
        String playerScoresAsCsvContent = service.getAllPlayerScoresByRound(roundNumber, specialRound);

        // Convert the CSV content to a byte array and create a ByteArrayResource with the CSV content
        byte[] csvBytes = playerScoresAsCsvContent.getBytes(StandardCharsets.UTF_8);
        ByteArrayResource resource = new ByteArrayResource(csvBytes);

        // Set the response headers
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + PLAYER_SCORE_DATA_ROUND + roundNumber + (specialRound? "_specialRound.csv" : "_regularRound.csv") );

        // Return the ResponseEntity with the CSV file and headers
        return ResponseEntity.ok()
                .headers(headers)
                .contentType(MediaType.MULTIPART_FORM_DATA)
                .body(resource);
    }

    @Operation(summary = "Set one player of the month over all positions for the current season")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Normal behavior",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = PlayerOfMonthDto.class))}),
            @ApiResponse(responseCode = "400", description = "player of the month already set",
                    content = {@Content}),
            @ApiResponse(responseCode = "404", description = "Error case, contact support",
                    content = {@Content})
    })
    @PostMapping(value = "/{id}/ofMonth", produces = MediaType.APPLICATION_JSON_VALUE)
    public PlayerOfMonthDto setPlayerOfMonth(@Parameter(name = "id", description = "id of the player", required = true, schema = @Schema(implementation = String.class))
                                             @PathVariable(name = "id") @NotBlank String id,
                                             @Parameter(name = "month", description = "month number", required = true, schema = @Schema(implementation = Integer.class))
                                             @RequestParam("month") @NotNull Integer month, @Parameter(name = "totalScore", description = "total score in month", schema = @Schema(implementation = Integer.class))
                                                 @RequestParam("totalScore") Integer totalScore) throws EntityNotExistException {
        return service.setPlayerOfMonth(id, month, totalScore);
    }

    @Operation(summary = "Set top scorer by position and month for current season")
    @ApiResponses(value = {@ApiResponse(responseCode = "200", description = "Normal behavior", content = {@Content}),
            @ApiResponse(responseCode = "400", description = "Forbidden", content = {@Content}),
            @ApiResponse(responseCode = "404", description = "Error case, contact support", content = {@Content})}
    )
    @PostMapping(value = "/topScorer/ofMonth", produces = MediaType.APPLICATION_JSON_VALUE)
    public void setTopScorerOfMonthByPosition(@Parameter(name = "month", description = "month number", required = true, schema = @Schema(implementation = Integer.class))
                                              @RequestParam("month") @NotNull Integer month) throws EntityNotExistException {
        service.doSetTopScorerByMonthAndPosition(month);
    }

    @Operation(summary = "Gets the player of the month for current season")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Normal behavior",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = PlayerOfMonthDto.class))}),
            @ApiResponse(responseCode = "400", description = "player of the month not found",
                    content = {@Content}),
            @ApiResponse(responseCode = "404", description = "Error case, contact support",
                    content = {@Content})
    })
    @GetMapping(value = "/ofMonth", produces = MediaType.APPLICATION_JSON_VALUE)
    public PlayerOfMonthDto getPlayerOfMonth(@Parameter(name = "month", description = "month number", schema = @Schema(implementation = Integer.class))
                                             @RequestParam("month") Integer month, @Parameter(name = "position", description = "position", schema = @Schema(implementation = Position.class))
                                             @RequestParam(name = "position", required = false) Position position) throws EntityNotExistException {
        return service.getPlayerOfMonth(month, null);
    }

    /* =================================================================================================================
     * Write endpoints
     * ============================================================================================================== */

    @Operation(summary = "Add current market value to given player")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Normal behavior", content = {@Content}),
            @ApiResponse(responseCode = "404", description = "Error case, contact support", content = {@Content})
    })
    @RolesAllowed({ROLE_ADMIN_WRITE})
    @PostMapping(value = "/{playerId}/marketValue/add", produces = MediaType.APPLICATION_JSON_VALUE)
    public void addPlayerMarketValue(HttpServletRequest request, HttpServletResponse response, @Parameter(name = "playerId") @PathVariable(name = "playerId")
    @NotNull String playerId, @Parameter(name = "currentMarketValue") @RequestParam(name = "currentMarketValue") int currentMarketValue) throws IOException, EntityNotExistException, InvalidOperationException {
        Optional<String> currentRole = getRequesterRole(request, properties);
        if (currentRole.isEmpty()) {
            handleRoleNotFound(response);
        } else {
            service.addPlayerMarketValue(playerId, currentMarketValue, currentRole.get());
        }
    }
}
