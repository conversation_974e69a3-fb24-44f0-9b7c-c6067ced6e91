package com.pass.hbl.manager.backend.persistence.service.hm.handlers;

import com.pass.hbl.manager.backend.persistence.domain.hm.HmLeagueInfoDO;
import com.pass.hbl.manager.backend.persistence.domain.hm.HmLeagueMembershipBaseDO;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmLeague;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmSeason;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmUserProfile;
import com.pass.hbl.manager.backend.persistence.exception.*;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmLeagueRepository;
import com.pass.hbl.manager.backend.persistence.service.admin.LogMessageService;
import com.pass.hbl.manager.backend.persistence.service.admin.ParameterService;
import com.pass.hbl.manager.backend.persistence.service.hm.UserProfileService;
import com.pass.hbl.manager.backend.persistence.util.ParameterDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

import static com.pass.hbl.manager.backend.persistence.util.Constants.SYSTEM_USERNAME;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

@Service
@Transactional
@Slf4j
public class DefaultLeagueResetHandler {

    private final HmLeagueRepository leagueRepository;

    private final LogMessageService logMessageService;
    private final UserProfileService userProfileService;
    private final ParameterService parameterService;

    private final LeagueMembershipHandler leagueMembershipHandler;

    public DefaultLeagueResetHandler(@Lazy HmLeagueRepository leagueRepository, @Lazy LogMessageService logMessageService,
                                     @Lazy UserProfileService userProfileService, @Lazy ParameterService parameterService, @Lazy LeagueMembershipHandler leagueMembershipHandler) {
        this.leagueRepository = leagueRepository;
        this.logMessageService = logMessageService;
        this.userProfileService = userProfileService;
        this.parameterService = parameterService;
        this.leagueMembershipHandler = leagueMembershipHandler;
    }


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public UUID buildLeagueFromPrevious(HmLeagueInfoDO previousLeagueInfoDO, HmSeason newSeason, Map<UUID, List<HmLeagueMembershipBaseDO>> membershipsByLeague) {
        HmLeague newLeague;
        try {
            newLeague = createNewLeagueFromPrevious(previousLeagueInfoDO, newSeason, membershipsByLeague);
        } catch (Exception e) {
            String message = "resetAllLeaguesBySeasonChange: Failed to create new league from previous one with id[" + previousLeagueInfoDO.getId() + "]";
            log.error(message, e);
            logMessageService.logException("resetAllLeaguesBySeasonChange", message, e);
            return null;
        }

        if (nonNull(newLeague)) {
            try {
                addMembersFromPreviousLeague(previousLeagueInfoDO, newLeague, membershipsByLeague);
                return newLeague.getId();
            } catch (Exception e) {
                String message = "resetAllLeaguesBySeasonChange: Failed to add members to new league [" + newLeague.getId() + "] from previous one with id[" + previousLeagueInfoDO.getId() + "]";
                log.error(message, e);
                logMessageService.logException("resetAllLeaguesBySeasonChange", message, e);
                return newLeague.getId();
            }
        }
        return null;
    }

    private void addMembersFromPreviousLeague(HmLeagueInfoDO previousLeagueInfoDO, HmLeague newLeague, Map<UUID, List<HmLeagueMembershipBaseDO>> membershipsByLeague) throws EntityNotExistException {
        List<HmLeagueMembershipBaseDO> oldMemberships = membershipsByLeague.get(previousLeagueInfoDO.getId());
        if (isNull(oldMemberships) || oldMemberships.isEmpty()) {
            String message = "resetAllLeaguesBySeasonChange: addMembersFromPreviousLeague. List of previous memberships including owner in league [" + previousLeagueInfoDO.getId() + "] is null or empty. Skipping";
            log.info(message);
            logMessageService.logInfo("resetAllLeaguesBySeasonChange", message);
            return;
        }
        log.info("resetAllLeaguesBySeasonChange: addMembersFromPreviousLeague. Found [" + oldMemberships.size() + "] including owner in league [" + previousLeagueInfoDO.getId() + "]");
        int countNewMemberships = 0;
        for (HmLeagueMembershipBaseDO hmLeagueMembershipBaseDO : oldMemberships) {
            if (!Objects.equals(hmLeagueMembershipBaseDO.getMemberId(), previousLeagueInfoDO.getOwnerId())) {
                HmUserProfile user;
                user = userProfileService.getByIdInNewTransaction(hmLeagueMembershipBaseDO.getMemberId());
                try {
                    leagueMembershipHandler.join(user, newLeague, LocalDateTime.now(), false, true);
                    countNewMemberships ++;
                    log.info("resetAllLeaguesBySeasonChange: addMembersFromPreviousLeague. User [" + user.getId() + "] joined newly created league id[" + newLeague.getId() + "]");
                } catch (Exception e) {
                    String message = "resetAllLeaguesBySeasonChange: addMembersFromPreviousLeague. Failed to join User [" + user.getId() + "] to newly created league id[" + newLeague.getId() + ". Skipping";
                    log.error(message, e);
                    logMessageService.logException("resetAllLeaguesBySeasonChange", message, e);
                }
            }
        }
        if (countNewMemberships > oldMemberships.size()) {
            log.info("Inconsistency: countNewMemberships > oldMemberships.size()");
        }
    }

    private HmLeague createNewLeagueFromPrevious(HmLeagueInfoDO previousLeagueInfoDO, HmSeason newSeason, Map<UUID, List<HmLeagueMembershipBaseDO>> membershipsByLeague) throws EntityNotExistException, InvalidOperationException, ForbiddenOperationException, SchedulingException, FormatException {
        long start = System.currentTimeMillis();
        UUID previousLeagueId = previousLeagueInfoDO.getId();
        // validateLeagueNameIsNotExistingOrBlacklisted Skipped since double naming is allowed
        HmUserProfile owner;
        try {
            owner = userProfileService.getByIdInNewTransaction(previousLeagueInfoDO.getOwnerId());
        } catch (EntityNotExistException e) {
            String message = "createNewLeagueFromPrevious: Could not create new league from previous one with id[" + previousLeagueInfoDO.getId() + "]. Reason: owner [" + previousLeagueInfoDO.getOwnerId() + "] does not exits. Skipping";
            log.error(message);
            logMessageService.logInfo("resetAllLeaguesBySeasonChange", message);
            return null;
        }

        HmLeague hmPreviousLeague = previousLeagueId == null ? null : leagueRepository.findById(previousLeagueId).orElse(null);
        assert hmPreviousLeague != null;
        HmLeague hmLeague = new HmLeague(previousLeagueInfoDO.getName(), previousLeagueInfoDO.getDescription(), newSeason, owner, hmPreviousLeague.getPublicAccess(), hmPreviousLeague);
        hmLeague.setPicture(previousLeagueInfoDO.getImageId());
        int leagueMaxSize = parameterService.getAsInteger(ParameterDefaults.PARAM_LEAGUE_MAX_SIZE, SYSTEM_USERNAME);
        if (hmLeague.getMaxSize() <= 0 || hmLeague.getMaxSize() > leagueMaxSize) {
            hmLeague.setMaxSize(leagueMaxSize);
        }
        hmLeague = leagueRepository.save(hmLeague);
        log.info("league with id=[" + hmLeague.getId() + "] is already saved");
        // check if the owner ir really member of the league
        List<HmLeagueMembershipBaseDO> oldMemberships = membershipsByLeague.get(previousLeagueInfoDO.getId());
        boolean isOwnerMemberOfTheLeague = nonNull(oldMemberships) && oldMemberships.stream().anyMatch(m -> Objects.equals(m.getMemberId(), previousLeagueInfoDO.getOwnerId()));
        if (isOwnerMemberOfTheLeague) {
            leagueMembershipHandler.join(owner, hmLeague, LocalDateTime.now(), true, true);
        } else {
            log.info( "createNewLeagueFromPrevious: owner["+ previousLeagueInfoDO.getOwnerId() +"] of previous league with id[" + previousLeagueInfoDO.getId() + "] is not member of the league. Skipping");
        }
        long end = System.currentTimeMillis();
        log.info("create league execution time: " + (end - start) + " milliseconds");
        return hmLeague;
    }
}
