package com.pass.hbl.manager.backend.admin.controller;

import com.pass.hbl.manager.backend.admin.util.ApiConstants;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmRound;
import com.pass.hbl.manager.backend.persistence.service.hm.SeasonService;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.security.RolesAllowed;
import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping(ApiConstants.SEASON_API)
@Validated
@Tag(name = "season", description = "Admin APi for season and round management", externalDocs = @ExternalDocumentation(description = "Further details", url = "https://www.liquimoly-hbl.de/de/liqui-moly-hbl/spielplan/"))
@RolesAllowed({ApiConstants.ROLE_ADMIN})
public class SeasonController {

    private final SeasonService service;

    public SeasonController(SeasonService service) {
        this.service = service;
    }

    @Operation(summary = "Get all available round numbers in current season set up by START7")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "All available round numbers in current season", content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = List.class))}),
            @ApiResponse(responseCode = "404", description = "Available round numbers in current season could not be found")
    })
    @GetMapping(value = "/roundNumber/all", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<Integer> getAllUsernames() throws Exception {
        return service.getSortedRoundsCurrentSeason().stream().map(HmRound::getRoundNumber).filter(Objects::nonNull).sorted().toList();
    }
}
