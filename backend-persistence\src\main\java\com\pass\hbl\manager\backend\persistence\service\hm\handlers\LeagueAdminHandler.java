package com.pass.hbl.manager.backend.persistence.service.hm.handlers;

import com.pass.hbl.manager.backend.persistence.domain.admin.AdminLeagueMembersCountDO;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmLeague;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmRound;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.exception.ForbiddenOperationException;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmLeagueRepository;
import com.pass.hbl.manager.backend.persistence.service.hm.SeasonService;
import com.pass.hbl.manager.backend.persistence.service.hm.UserProfileService;
import com.pass.hbl.manager.backend.persistence.service.shared.TransactionHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.constraints.NotNull;
import java.util.*;

@Slf4j
@Service
@Transactional
/*
  Handles league operations performed by the administrator (system)
 */
public class LeagueAdminHandler {

    private final HmLeagueRepository leagueRepository;

    private final TransactionHandler transactionHandler;
    private final LeagueMembershipHandler leagueMembershipHandler;
    private final LeagueScoreHandler leagueScoreHandler;

    private final UserProfileService userProfileService;
    private final SeasonService seasonService;

    public LeagueAdminHandler(@Lazy HmLeagueRepository leagueRepository, @Lazy TransactionHandler transactionHandler, @Lazy LeagueMembershipHandler leagueMembershipHandler, @Lazy LeagueScoreHandler leagueScoreHandler, @Lazy UserProfileService userProfileService, @Lazy SeasonService seasonService) {
        this.leagueRepository = leagueRepository;
        this.transactionHandler = transactionHandler;
        this.leagueMembershipHandler = leagueMembershipHandler;
        this.leagueScoreHandler = leagueScoreHandler;
        this.userProfileService = userProfileService;
        this.seasonService = seasonService;
    }

    public Map<String, String> getLeagueByNameAsDto(String leagueName) throws EntityNotExistException {
        HmLeague league = getByName(leagueName);
        Map<String, String> map = new LinkedHashMap<>();
        map.put("id", league.getId().toString());
        map.put("owner", "username="+ league.getOwner().getUsername() + ", email=" + league.getOwner().getEmailAddress());
        map.put("name", leagueName);
        map.put("public access", league.getPublicAccess().toString());
        map.put("members count", Integer.toString(league.getSize()));
        return map;
    }

    public Map<String, String> getUserRoundScoreDetails(String username, String leagueName, HmRound round) throws EntityNotExistException, FormatException {
        return leagueScoreHandler.getUserRoundScoreDetails(userProfileService.getByUserNameOrEmailAddress(username), getByNameReadOnly(leagueName), round);
    }

    public List<AdminLeagueMembersCountDO> getMembersCountByLeague() throws EntityNotExistException {
        return leagueMembershipHandler.findMembersCountByLeague(seasonService.getCurrentSeason().getId());
    }

    public Map<String, String> addPointsToUserRoundScore(String username, String leagueName, HmRound round, int pointsToAdd) throws EntityNotExistException {
        return leagueScoreHandler.addPointsToUserRoundScore(userProfileService.getByUserNameOrEmailAddress(username), getByNameReadOnly(leagueName), round, pointsToAdd);
    }

    public void removeLeagueMemberByAdmin(@NotNull String leagueName, @NotNull String username, @NotNull String currentRole) throws EntityNotExistException, ForbiddenOperationException {
        leagueMembershipHandler.removeLeagueMemberByAdmin(getByNameReadOnly(leagueName), userProfileService.getByUserNameOrEmailAddress(username), currentRole);
    }

    public HmLeague getByNameReadOnly(String leagueName) throws EntityNotExistException {
        UUID currentSeasonId = seasonService.getCurrentSeason().getId();
        return transactionHandler.runInNewTransactionReadOnly(() -> leagueRepository.findBySeasonIdAndNameIgnoreCase(currentSeasonId, leagueName).stream().findFirst()).orElseThrow(() -> new EntityNotExistException(HmLeague.class, "league name", leagueName));
    }

    private HmLeague getByName(String leagueName) throws EntityNotExistException {
        UUID currentSeasonId = seasonService.getCurrentSeason().getId();
        return leagueRepository.findBySeasonIdAndNameIgnoreCase(currentSeasonId, leagueName).stream().findFirst().orElseThrow(() -> new EntityNotExistException(HmLeague.class, "league name", leagueName));
    }
}
