package com.pass.hbl.manager.backend.persistence.repository.hm;

import com.pass.hbl.manager.backend.persistence.domain.hm.HmPlayerMarketValueDO;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmPlayerMarketValue;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface HmPlayerMarketValueRepository extends PagingAndSortingRepository<HmPlayerMarketValue, UUID> {

    @Query("select m from HmPlayerMarketValue m where m.player.id = :playerId and (m.validFrom >= :startDate or m.validTo >= :startDate)")
    List<HmPlayerMarketValue> findByPlayerIdAndValidAfterDate(@Param("playerId") UUID playerId, @Param("startDate") LocalDateTime startDate);

    @Query(value ="select * from hm.player_market_value where player_id = :playerId and valid_from < :startDate and current_value = true and deleted = false ORDER BY valid_from DESC limit 1", nativeQuery = true)
    Optional<HmPlayerMarketValue> findLastMarketValueValidAfterDate(@Param("playerId") UUID playerId, @Param("startDate") LocalDateTime startDate);

    Iterable<HmPlayerMarketValue> findByCurrentValue(boolean currentValue);

    boolean existsByPlayerIdAndCurrentValue(UUID playerId, boolean currentValue);

    @Query(value = "select * from hm.player_market_value where player_id = :playerId and deleted = false and current_value = true order by created_at desc limit 1", nativeQuery = true)
    Optional<HmPlayerMarketValue> findLatestMarketValue(@Param("playerId") UUID playerId);

    @Query("select p.player.id from HmPlayerMarketValue p where p.id = :playerMarketValueId and p.deleted = false")
    UUID findPlayerIdById(@Param("playerMarketValueId") UUID playerMarketValueId);

    @Query(value = "select * from hm.player_market_value where player_id = :playerId and (valid_from <= :startDate and (valid_to <= :endDate or current_value = true)) and deleted = false ORDER BY valid_from DESC limit 1", nativeQuery = true)
    Optional<HmPlayerMarketValue> findByPlayerIdValidInDay(@Param("playerId") UUID playerId, @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    @Query(value = "select cast(player_id as varchar) as playerId, valid_from as validFrom, valid_to as validTo, market_value as marketValue, current_value as currentValue  from hm.player_market_value where (valid_from <= :startDate and (valid_to <= :endDate or current_value = true)) and deleted = false", nativeQuery = true)
    List<HmPlayerMarketValueDO> findAllPlayerMarketValuesAtDate(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    @Modifying
    @Query("update HmPlayerMarketValue p set p.currentValue = false, p.validTo = :validTo, p.modifiedAt = CURRENT_TIMESTAMP where p.id = :id and deleted = false")
    int resetCurrentValueById(@Param("id") UUID id, @Param("validTo") LocalDateTime validTo);


}
