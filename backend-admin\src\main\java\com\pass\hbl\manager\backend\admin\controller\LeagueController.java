package com.pass.hbl.manager.backend.admin.controller;

import com.pass.hbl.manager.backend.admin.config.HandballManagerAdminConfigurationProperties;
import com.pass.hbl.manager.backend.persistence.domain.admin.AdminLeagueMembersCountDO;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmLeague;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.exception.ForbiddenOperationException;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.service.hm.LeagueService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.security.RolesAllowed;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.pass.hbl.manager.backend.admin.util.ApiConstants.*;

@RestController
@RequestMapping(LEAGUE_API)
@Validated
@Tag(name = "league", description = "Admin Api for managing league details, memberships and scores")
public class LeagueController extends AbstractController {

    private final LeagueService service;

    private final HandballManagerAdminConfigurationProperties properties;

    public LeagueController(ApplicationEventPublisher eventPublisher, LeagueService service, HandballManagerAdminConfigurationProperties properties) {
        super(eventPublisher);
        this.service = service;
        this.properties = properties;
    }

    @Operation(summary = "Get all available league names")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "All available league names found", content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = List.class))}),
            @ApiResponse(responseCode = "404", description = "Available league names could not be found")
    })
    @RolesAllowed({ROLE_ADMIN, ROLE_ADMIN_WRITE})
    @GetMapping(value = "/name/all", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<String> getAllLeagueNames() {
        return service.getAll().stream().map(HmLeague::getName).toList();
    }

    @Operation(summary = "Get members count by league")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Normal behavior", content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = List.class))}),
            @ApiResponse(responseCode = "404", description = "Error case, contact support")
    })
    @RolesAllowed({ROLE_ADMIN, ROLE_ADMIN_WRITE})
    @GetMapping(value = "/statistics/membersCount", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<AdminLeagueMembersCountDO> getMembersCountByLeague() throws EntityNotExistException {
        return service.getMembersCountByLeague();
    }

    @Operation(summary = "Get statistics of public/private leagues count")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Normal behavior", content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = List.class))}),
            @ApiResponse(responseCode = "404", description = "Error case, contact support")
    })
    @RolesAllowed({ROLE_ADMIN, ROLE_ADMIN_WRITE})
    @GetMapping(value = "/statistics/publicAccess", produces = MediaType.APPLICATION_JSON_VALUE)
    public Map<String, Long> getPublicAccessStatistics() {
        Map<Boolean, Long> map = service.getAll().stream().collect(Collectors.groupingBy(HmLeague::getPublicAccess, Collectors.counting()));
        return map.entrySet().stream().collect(Collectors.toMap(
                entry -> (entry.getKey()? "public leagues" : "private leagues"),
                Map.Entry::getValue
        ));
    }

    @Operation(summary = "Get the round score by league for the given user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "User round score details found ", content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = Map.class))}),
            @ApiResponse(responseCode = "404", description = "No user round score details found", content = {@Content})
    })
    @RolesAllowed({ROLE_ADMIN, ROLE_ADMIN_WRITE})
    @GetMapping(value = "/{leagueName}/user/{username}/roundScore", produces = MediaType.APPLICATION_JSON_VALUE)
    public Map<String, String> getUserRoundScoreDetails(@Parameter(name = "username") @PathVariable(name = "username")
                                                                     @NotNull String username, @Parameter(name = "leagueName")
                                                                     @PathVariable(name = "leagueName") @NotNull String leagueName,
                                                                     @Parameter(name = "roundNumber", schema = @Schema(allowableValues = {"1","2","3","4","5","6","7","8","9","10","11","12","13","14","15","16","17","18","19","20","21","22","23","24","25","26","27","28","29","30","31","32","33","34"}))
                                                                     @RequestParam(name = "roundNumber")
                                                                     @NotNull String roundNumber, @Parameter(name = "specialRound")
                                                            @RequestParam(name = "specialRound") @NotNull Boolean specialRound) throws EntityNotExistException, FormatException {
        return service.getUserRoundScoreDetails(username, leagueName, roundNumber, specialRound);
    }


    @Operation(summary = "Get league info with details")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Normal behavior", content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = Map.class))}),
            @ApiResponse(responseCode = "400", description = "League already exists", content = {@Content}),
            @ApiResponse(responseCode = "404", description = "Error case, contact support", content = {@Content})
    })
    @RolesAllowed({ROLE_ADMIN, ROLE_ADMIN_WRITE})
    @GetMapping(value = "/{leagueName}", produces = MediaType.APPLICATION_JSON_VALUE)
    public Map<String, String> getLeagueInfo(@Parameter(name = "leagueName")
                                            @PathVariable(name = "leagueName") @NotNull String leagueName) throws EntityNotExistException {
        return service.getLeagueByNameAsDto(leagueName);
    }

    /* =================================================================================================================
     * Write endpoints
     * ============================================================================================================== */

    //TODO hbs WIP
    @Operation(summary = "Add points to the round score for give league and user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "User round score update info", content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = Map.class))}),
            @ApiResponse(responseCode = "404", description = "No user round score details found", content = {@Content})
    })
    @RolesAllowed({ROLE_ADMIN_WRITE})
    @PostMapping(value = "/{leagueName}/user/{username}/roundScore/add", produces = MediaType.APPLICATION_JSON_VALUE)
    public Map<String, String> addPointsToUserRoundScore(HttpServletRequest request, HttpServletResponse response, @Parameter(name = "username") @PathVariable(name = "username")
                                                        @NotNull String username, @Parameter(name = "leagueName")
                                                        @PathVariable(name = "leagueName") @NotNull String leagueName,
                                                         @Parameter(name = "roundNumber", schema = @Schema(allowableValues = {"1","2","3","4","5","6","7","8","9","10","11","12","13","14","15","16","17","18","19","20","21","22","23","24","25","26","27","28","29","30","31","32","33","34"}))
                                                        @RequestParam(name = "roundNumber")
                                                        @NotNull String roundNumber, @Parameter(name = "pointsToAdd") @RequestParam(name = "pointsToAdd") int pointsToAdd) throws EntityNotExistException, IOException {
        Optional<String> currentRole = getRequesterRole(request, properties);
        if (currentRole.isEmpty()) {
            handleRoleNotFound(response);
            return null;
        } else {
            return service.addPointsToUserRoundScore(username, leagueName, roundNumber, pointsToAdd);
        }
    }


    @Operation(summary = "Cleanup members by given league id")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Success", content = {@Content}),
            @ApiResponse(responseCode = "403", description = "Forbidden", content = {@Content}),
            @ApiResponse(responseCode = "404", description = "Exception case", content = {@Content})
    })
    @RolesAllowed(ROLE_ADMIN_WRITE)
    @DeleteMapping(value = "/{leagueId}/members/cleanup", produces = MediaType.APPLICATION_JSON_VALUE)
    public void cleanupMembers(@Parameter(name = "leagueId", description = "id of the league to leave", required = true)
                      @PathVariable("leagueId")
                      @NotNull String leagueId) throws EntityNotExistException, FormatException {
        service.cleanupMembers(leagueId);
    }


    @Operation(summary = "Cleanup members by all leagues")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Success", content = {@Content}),
            @ApiResponse(responseCode = "403", description = "Forbidden", content = {@Content}),
            @ApiResponse(responseCode = "404", description = "Exception case", content = {@Content})
    })
    @RolesAllowed(ROLE_ADMIN_WRITE)
    @DeleteMapping(value = "/members/cleanup", produces = MediaType.APPLICATION_JSON_VALUE)
    public void cleanupAllLeagues() {
        service.cleanupAllLeagues();
    }


    @Operation(summary = "Remove a user from a league")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Success", content = {@Content}),
            @ApiResponse(responseCode = "403", description = "Forbidden", content = {@Content}),
            @ApiResponse(responseCode = "404", description = "Exception case", content = {@Content})
    })
    @RolesAllowed(ROLE_ADMIN_WRITE)
    @DeleteMapping(value = "/{leagueName}/user/{username}/remove", produces = MediaType.APPLICATION_JSON_VALUE)
    public void removeLeagueMemberByAdmin(HttpServletRequest request, HttpServletResponse response,
                                          @Parameter(name = "leagueName") @PathVariable(name = "leagueName") @NotNull String leagueName, @Parameter(name = "username") @PathVariable(name = "username")
                           @NotNull String username)
            throws EntityNotExistException, IOException, ForbiddenOperationException {
        Optional<String> currentRole = getRequesterRole(request, properties);
        if (currentRole.isPresent()) {
            service.removeLeagueMemberByAdmin(leagueName, username, currentRole.get());
        } else {
            handleRoleNotFound(response);
        }
    }
}
