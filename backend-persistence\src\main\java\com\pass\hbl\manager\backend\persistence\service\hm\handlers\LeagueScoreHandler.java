package com.pass.hbl.manager.backend.persistence.service.hm.handlers;

import com.nimbusds.jose.util.Pair;
import com.pass.hbl.manager.backend.persistence.domain.hm.HmJobCronExpressionDO;
import com.pass.hbl.manager.backend.persistence.domain.hm.HmUserRoundScoreDeletedDO;
import com.pass.hbl.manager.backend.persistence.domain.hm.HmUserRoundScoreTransactionDO;
import com.pass.hbl.manager.backend.persistence.domain.hm.HmUserTotalScoreDO;
import com.pass.hbl.manager.backend.persistence.dto.hm.*;
import com.pass.hbl.manager.backend.persistence.entity.hm.*;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.exception.InvalidOperationException;
import com.pass.hbl.manager.backend.persistence.mapper.hm.HmRoundMapper;
import com.pass.hbl.manager.backend.persistence.mapper.hm.HmUserMapper;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmLeagueMembershipRepository;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmUserRoundScoreRepository;
import com.pass.hbl.manager.backend.persistence.service.admin.LogMessageService;
import com.pass.hbl.manager.backend.persistence.service.hm.*;
import com.pass.hbl.manager.backend.persistence.service.hm.helpers.CacheHandler;
import com.pass.hbl.manager.backend.persistence.service.shared.TransactionHandler;
import com.pass.hbl.manager.backend.persistence.util.Constants;
import com.pass.hbl.manager.backend.persistence.util.Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.LockModeType;
import javax.validation.constraints.NotNull;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.pass.hbl.manager.backend.persistence.util.Constants.*;
import static com.pass.hbl.manager.backend.persistence.util.Util.roundToNearest30Or00WithHourBuffer;
import static java.util.Collections.emptyList;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static org.springframework.transaction.annotation.Propagation.*;

@Slf4j
@Service
@Transactional
public class LeagueScoreHandler {

    private final SeasonService seasonService;
    private final UserProfileService userProfileService;
    private final LogMessageService logMessageService;
    private final MatchService matchService;
    private final TeamService teamService;
    private final PlayerService playerService;
    private final HmSchedulerService schedulerService;

    private final HmUserMapper userMapper;
    private final HmRoundMapper roundMapper;

    private final HmUserRoundScoreRepository userRoundScoreRepository;
    private final HmLeagueMembershipRepository leagueMembershipRepository;

    private final TransactionHandler transactionHandler;
    private final LeagueScoreUpdateHandler leagueScoreUpdateHandler;
    private final LeagueScoreResetHandler leagueScoreResetHandler;
    private final LiveLeagueScoreHandler liveLeagueScoreHandler;
    private final CacheManager cacheManager;
    private final CacheHandler cacheHandler;

    public LeagueScoreHandler(SeasonService seasonService, @Lazy UserProfileService userProfileService, LogMessageService logMessageService, @Lazy MatchService matchService, @Lazy TeamService teamService, @Lazy PlayerService playerService, @Lazy HmSchedulerService schedulerService, LiveLeagueScoreHandler liveLeagueScoreHandler, HmUserMapper userMapper, HmRoundMapper roundMapper, HmUserRoundScoreRepository userRoundScoreRepository, HmLeagueMembershipRepository leagueMembershipRepository, TransactionHandler transactionHandler, LeagueScoreUpdateHandler leagueScoreUpdateHandler, LeagueScoreResetHandler leagueScoreResetHandler, CacheManager cacheManager, CacheHandler cacheHandler) {
        this.seasonService = seasonService;
        this.userProfileService = userProfileService;
        this.logMessageService = logMessageService;
        this.matchService = matchService;
        this.teamService = teamService;
        this.playerService = playerService;
        this.schedulerService = schedulerService;
        this.userMapper = userMapper;
        this.roundMapper = roundMapper;
        this.userRoundScoreRepository = userRoundScoreRepository;
        this.leagueMembershipRepository = leagueMembershipRepository;
        this.transactionHandler = transactionHandler;
        this.leagueScoreUpdateHandler = leagueScoreUpdateHandler;
        this.leagueScoreResetHandler = leagueScoreResetHandler;
        this.liveLeagueScoreHandler = liveLeagueScoreHandler;
        this.cacheManager = cacheManager;
        this.cacheHandler = cacheHandler;
    }

    @Lock(LockModeType.PESSIMISTIC_WRITE)
    @Transactional(propagation = REQUIRES_NEW)
    public void resetManagerScoresByLeagues(HmRound round, List<UUID> leagueIds, boolean resetUserLevel) {
        long start = System.currentTimeMillis();

        // cleanup league memberships of deleted user profiles to avoid getting an exception by trying to create UserRoundScore entity with user = null
        UUID roundId = round.getId();
        leagueScoreUpdateHandler.cleanUpMemberships(leagueIds);

        // cleanup the invalid user round scores of deleted memberships including those deleted by the previous cleanup to avoid that their scores get considered in total score by user
        leagueScoreUpdateHandler.cleanUpUserRoundScores(roundId);

        // reset user experience points
        if (resetUserLevel) {
            List<HmUserTotalScoreDO> usersWithPositiveTotalScore = transactionHandler.runInNewTransactionReadOnly(() -> getUsersWithPositiveTotalScoreInLeagues(roundId, leagueIds));
            int usersSize = usersWithPositiveTotalScore.size();
            log.info("resetManagerScores: usersWithPositiveTotalScore count = " + usersSize);
            resetUserExperiencePointsAndLevel(usersWithPositiveTotalScore, usersSize);
        } else {
            log.info("resetManagerScores: resetUserLevel skipped.");
        }

        // reset league memberships
        List<UUID[]> membershipInfoList = leagueMembershipRepository.findAllIdsAndUserProfileIdsAndLeagueIdsByLeagueIdIn(leagueIds);
        int size = membershipInfoList.size();
        // reset score and balance for given memberships
        resetLeagueMembershipsScoreAndBalance(round, membershipInfoList, size);
        long end = System.currentTimeMillis();
        log.info("resetManagerScores: reset manager scores finished for " + size + " leagues. Execution time: " + (end - start) + " milliseconds");
    }

    @Lock(LockModeType.PESSIMISTIC_WRITE)
    @Transactional(propagation = REQUIRES_NEW)
    public void resetAllManagerScores(HmRound round, UUID currentSeasonId, List<HmLeague> leagues) {
        long start = System.currentTimeMillis();

        //cleanup the invalid user round scores of deleted memberships to avoid that their scores get considered in total score by user
        UUID roundId = round.getId();
        leagueScoreUpdateHandler.cleanUpMemberships(leagues.stream().map(HmLeague::getId).toList());

        // cleanup league memberships of deleted user profiles including those deleted by the previous cleanup to avoid getting an exception by trying to create UserRoundScore entity with user = null
        leagueScoreUpdateHandler.cleanUpUserRoundScores(roundId);

        // reset user experience points
        List<HmUserTotalScoreDO> usersWithPositiveTotalScore = transactionHandler.runInNewTransactionReadOnly(() -> getUsersWithPositiveTotalScore(roundId));
        int usersSize = usersWithPositiveTotalScore.size();
        log.info("resetManagerScores: usersWithPositiveTotalScore count = " + usersSize);
        resetUserExperiencePointsAndLevel(usersWithPositiveTotalScore, usersSize);

        // reset league memberships
        List<UUID[]> membershipInfoList = leagueMembershipRepository.findAllIdsAndUserProfileIdsAndLeagueIds(currentSeasonId);
        int size = membershipInfoList.size();
        // reset score and balance for given memberships
        resetLeagueMembershipsScoreAndBalance(round, membershipInfoList, size);
        long end = System.currentTimeMillis();
        log.info("resetManagerScores: reset manager scores finished for " + size + " leagues. Execution time: " + (end - start) + " milliseconds");
    }

    public List<HmUserTotalScoreDO> getUsersWithPositiveTotalScore(UUID roundId) {
        return userRoundScoreRepository.findUsersWithPositiveTotalScoreInRound(roundId);
    }

    private List<HmUserTotalScoreDO> getUsersWithPositiveTotalScoreInLeagues(UUID roundId, List<UUID> leagueIds) {
        return userRoundScoreRepository.findUsersWithPositiveTotalScoreInRoundAndLeagueIdIn(roundId, leagueIds);
    }

    @Retryable(value = {RuntimeException.class}, backoff = @Backoff(delay = 2000))
    @Lock(LockModeType.PESSIMISTIC_WRITE)
    @Transactional(propagation = REQUIRED)
    private void resetUserExperiencePointsAndLevel(List<HmUserTotalScoreDO> usersWithPositiveTotalScore, int usersSize) {
        log.info("resetManagerScores: Start resetting experience points and level for users with positive score count = " + usersSize);
        userProfileService.resetUserExperiencePointsAndLevel(usersWithPositiveTotalScore, usersSize);
    }

    @Retryable(value = {RuntimeException.class}, backoff = @Backoff(delay = 2000))
    private void resetLeagueMembershipsScoreAndBalance(HmRound round, List<UUID[]> membershipInfoList, int size) {
        log.info("resetManagerScores: Start resetting manager scores for memberships " + size);
        AtomicInteger counter = new AtomicInteger(1);
        membershipInfoList.stream().parallel().forEach(membershipInfo -> {
                    // move it a new class for transaction reasons (each thread get the transaction required)
                    leagueScoreResetHandler.resetScoreAndBalance(round, size, counter, membershipInfo);
                }
        );
    }

    /**
     * Updates manager scores, experience points and levels for the given leagues
     *
     * @param round              round in which the scores are calculated
     * @param leagues            given leagues for which the scores are calculated
     * @param firstResultInRound true for RoundResultJob (first job), otherwise false (RoundResultUpdateJob)
     */
    public void updateManagerScores(HmRound round, List<HmLeague> leagues, boolean firstResultInRound, boolean updateManagerScoresOnly) {
        long start = System.currentTimeMillis();
        if (leagues == null || leagues.isEmpty()) {
            return;
        }
        // cleanup league memberships of deleted user profiles including those deleted by the previous cleanup to avoid getting an exception by trying to create UserRoundScore entity with user = null
        UUID roundId = round.getId();
        List<UUID> leagueIds = leagues.stream().map(HmLeague::getId).toList();
        leagueScoreUpdateHandler.cleanUpMemberships(leagueIds);

        // clean up user round scores of deleted league memberships, otherwise the corresponding leagues will be marked as processed
        leagueScoreUpdateHandler.cleanUpUserRoundScores(roundId);

        /*
          By the first run of this method in the round create backup of the following data:
          - League membership score & balance
          - User profile experience points and level
          For the next runs of this method the backup should not be overwritten, since experience points
          and levels are exclusively calculated based on the backup values

          Update 11-09-2024 this block is not needed anymore,since experience points and levels are exclusively calculated based on the backup values
        */
        /*if (firstResultInRound) {
            // Not need to check if backup values already exist, since they could be backups of the last round
            leagueScoreUpdateHandler.createBackup();
        }*/

        // The attributes tempScore and tempBalance are used to temporary save the league memberships score and balance
        // while calculating the round results, so that the user can see all his league scores at the same time
        // By the first run of this method in the round we reset the temp score and temp balance for the following reason:
        // - Example: we have 5 leagues to process. By the first run only 3 leagues where processed and an error happened in transfer temp score/balance -> user score/balance
        // - By the second run 2 leagues were processed
        // - We not reset temp score and balance to be able to transfer the results of 5 leagues by the second run

        // Update 10.10.2024 this block is skipped, results are directly saved in user_score and user_balance
        /*if (firstResultInRound) {
            leagueScoreUpdateHandler.resetScoreAndBalanceTemp(leagueIds);
        }*/

        // firstResultInRound = true: In this step the backup should have been created & temp columns should have been reset otherwise the whole method will fail
        // in order to avoid that experience points and level calculation based on wrong backup values (values from last round)
        Set<UUID> alreadyEvaluated = userRoundScoreRepository.findAlreadyEvaluatedLeaguesByRoundId(roundId);
        List<HmLeague> leaguesToProcess = leagues.stream()
                .filter(l -> !alreadyEvaluated.contains(l.getId()))
                .toList();

        log.info("updateManagerScores: shall process " + leagues.size() + ". Already processed " + alreadyEvaluated.size() + " leagues. Leagues to process: " + leaguesToProcess.size());

        int size = leaguesToProcess.size();
        log.info("updateManagerScores: Start updating manager scores for leagues " + size);
        AtomicInteger counter = new AtomicInteger(1);
        AtomicInteger failedLeaguesCounter = new AtomicInteger(0);
        leaguesToProcess.stream().parallel().forEach(league -> {
            try {
                leagueScoreUpdateHandler.updateScoresByLeague(round, league);
                log.info("updateManagerScores: Update league " + league.getId() + " done (" + counter + " / " + size + ")");
            } catch (Exception e) {
                logMessageService.logException("[UpdateScoreByLeague] Error updating score of league " + league.getId() + ". Skipping ...", e);
                log.error("[UpdateScoreByLeague] Error updating score of league " + league.getId() + ". Skipping ...");
                failedLeaguesCounter.getAndIncrement();
            }
            counter.getAndIncrement();
        });

        /* For each league membership temp score and temp balance, if not null, is transferred to user score and balance.
        The number of updated rows could not match all evaluated memberships. Following memberships will be transferred,
         since they haven't been changed:
        - user joined league after closing
        - League memberships with negative start balance
        // Update 10.10.2024 this block is skipped, results are directly saved in user_score and user_balance
        */
        /*Integer rows = transactionHandler.runInNewTransaction(() -> transferTempScoreAndBalance(leagueIds));
        log.info("updateManagerScores: transfer temp manager scores and balance finished for " + rows + " league memberships. Only updated scores are transferred.");*/

        long end = System.currentTimeMillis();


        log.info("updateManagerScores: Updating manager scores finished for " + size + " leagues. Execution time: " + (end - start) + " milliseconds. Failed leagues (" + failedLeaguesCounter + " / " + size + ")");

        // if only manager scores update is requested. Skip the next steps:
        // - skip updating user levels
        // - skip handling failed leagues

        if (updateManagerScoresOnly) {
            return;
        }
        int countFailedLeagues = failedLeaguesCounter.get();
        if (countFailedLeagues == 0) {
            // update experience points levels for all users with positive total score (score over all leagues). No change for users with negative total score.
            List<HmUserTotalScoreDO> usersWithPositiveTotalScore = transactionHandler.runInNewTransactionReadOnly(() -> getUsersWithPositiveTotalScore(roundId));
            int usersSize = usersWithPositiveTotalScore.size();
            log.info("updateManagerScores: usersWithPositiveTotalScore count = " + usersSize);
            transactionHandler.runInNewTransaction(() -> updateUserLevels(usersWithPositiveTotalScore));
            //transactionHandler.runInNewTransaction(() -> updateUserLevels(roundId));
            log.info("updateManagerScores: Update user levels done");
            // firstResultInRound indicates that the method is called from "RoundResultJob" otherwise "RoundResultUpdateJob"
        } else if (firstResultInRound) {
            log.info("updateManagerScores: count failed leagues = [" + countFailedLeagues + "]. Update user levels skipped. RoundResultJob will be rescheduled");
            try {
                /* get the next round after the calculated round. Example current round is 7, the calculated round is 5 and the successor round of 5 is 6
                If the round has a successor round, the retries should end 4 hours after the closing of the successor round
                If the round has no successor, the retries should end 3 days after the round end
                */
                Optional<HmRound> successorRoundOptional = seasonService.getNextRoundAfterDate(round.getTo());

                /* the schedules are rounded to minute 00 or minutes 30 with minimum buffer of one hour.
                 Reason: We don't want that the job starts at the automatic servers restart usually at minute 15 (see server crontab)
                */
                LocalDateTime nextJobSchedule = roundToNearest30Or00WithHourBuffer(LocalDateTime.now());

                // adjust the next job schedule so that it skips the consistency check job schedules
                Optional<LocalDateTime> consistencyCheckJobNextExecutionTimeOpt = schedulerService.getConsistencyCheckJobNextExecutionTime();
                if (consistencyCheckJobNextExecutionTimeOpt.isPresent()) {
                    LocalDateTime consistencyCheckJobNextExecutionTime = consistencyCheckJobNextExecutionTimeOpt.get();
                    if (nextJobSchedule.isEqual(consistencyCheckJobNextExecutionTime)) {
                        log.info("updateManagerScores: Handling [" + countFailedLeagues + "] failed leagues. nextJobSchedule = " + nextJobSchedule + " is equal consistencyCheckJobNextExecutionTime = " + consistencyCheckJobNextExecutionTime + ". NextJobSchedule will be adjusted on one hour later");
                        nextJobSchedule = nextJobSchedule.plusHours(1);
                    } else if (nextJobSchedule.isAfter(consistencyCheckJobNextExecutionTime)) {
                        // Check if nextJobSchedule is at least 1 hour after consistencyCheckJobNextExecutionTime
                        Duration duration = Duration.between(consistencyCheckJobNextExecutionTime, nextJobSchedule);
                        if (duration.toHours() < 1) {
                            log.info("updateManagerScores: Handling [" + countFailedLeagues + "] failed leagues. nextJobSchedule = " + nextJobSchedule + " is less than 1 hour after consistencyCheckJobNextExecutionTime = " + consistencyCheckJobNextExecutionTime + ". NextJobSchedule will be adjusted on one hour after consistencyCheckJobNextExecutionTime");
                            // Adjust nextJobSchedule to be exactly 1 hour after consistencyCheckJobNextExecutionTime
                            nextJobSchedule =  consistencyCheckJobNextExecutionTime.plusHours(1);

                        }
                    }
                }

                // set the maximum date in which the retries should happen
                LocalDateTime retriesMaxDate = successorRoundOptional.map(hmRound -> hmRound.getClosing().minusHours(MIN_HOURS_BEFORE_CLOSING)).orElseGet(() -> round.getTo().plusDays(DEFAULT_ROUND_RESULT_MAX_RETRIES_DAYS));

                log.info("updateManagerScores: Handling [" + countFailedLeagues + "] failed leagues. nextJobSchedule = " + nextJobSchedule + ", retriesMaxDate " + retriesMaxDate);

                // get all open RoundResult jobs for the given round : not run, not started, not deleted
                List<HmJobCronExpressionDO> openJobsByRound = schedulerService.getOpenJobsByRoundResultJobNameAndRoundId(ROUND_RESULT_JOB, roundId);
                LocalDateTime finalNextJobSchedule = nextJobSchedule;
                List<HmJobCronExpressionDO> validOpenJobsFound = openJobsByRound.stream().filter(hmJobCronExpressionDO -> {
                    try {
                        LocalDateTime nextRun = Util.getNextCronExecutionTime(hmJobCronExpressionDO.getCronExpression());
                        /* the valid schedule is the one that could replace the nextJobSchedule:
                        1- check if the valid schedule is in interval [ round end - (successor round closing - 4 h)]
                        2- check if the schedules is in the near future: a valid schedule should be at most 30 minutes before or after than the nextJobSchedule
                        */
                        Duration duration = Duration.between(finalNextJobSchedule, nextRun);
                        long differenceInMinutes = Math.abs(duration.toMinutes());
                        return nextRun.isBefore(retriesMaxDate) && differenceInMinutes <= 30;
                    } catch (FormatException e) {
                        return false;
                    }
                }).toList();
                if (validOpenJobsFound.isEmpty()) {
                    if (nextJobSchedule.isBefore(retriesMaxDate)) {
                        log.info("updateManagerScores: Handling [" + countFailedLeagues + "] failed leagues. Rescheduling RoundResultJob for round id[" + roundId + "] at " + nextJobSchedule);
                        schedulerService.scheduleRoundResultJob(roundId, nextJobSchedule, true);
                    } else {
                        log.info("updateManagerScores: Handling [" + countFailedLeagues + "] failed leagues. Rescheduling RoundResultJob for round id [" + roundId + "] skipped. Reason: nextJobSchedule : [" + nextJobSchedule + "] is after retriesMaxDate [" + retriesMaxDate + "]");
                    }
                } else {
                    log.info("updateManagerScores: Handling [" + countFailedLeagues + "] failed leagues. Rescheduling RoundResultJob for round id [" + roundId + "] skipped. Reason: job is already scheduled for the near future " + validOpenJobsFound);
                }
            } catch (Exception e) {
                log.error("updateManagerScores: Handling [" + countFailedLeagues + "] failed leagues. Rescheduling RoundResultJob failed. Skipping..", e);
            }
        }
    }

    public int transferTempScoreAndBalance(List<UUID> leagueIds) {
        return leagueMembershipRepository.transferTempScoreAndBalance(leagueIds);
    }

    @Lock(LockModeType.PESSIMISTIC_WRITE)
    public boolean updateUserLevels(List<HmUserTotalScoreDO> usersWithPositiveTotalScore) {
        try {
            userProfileService.updateAllExperiencePointsAndLevels(usersWithPositiveTotalScore);
        } catch (Exception e) {
            log.info("updateUserLevels failed", e);
            return false;
        }
        return true;
    }

    @Lock(LockModeType.PESSIMISTIC_WRITE)
    public void updateUserLevels(UUID roundId) {
        // Admin method to update all user levels by round if this step in updateManagerScores fails
        try {
            // update experience points levels for all users with positive total score (score over all leagues). No change for users with negative total score.
            List<HmUserTotalScoreDO> usersWithPositiveTotalScore = transactionHandler.runInNewTransactionReadOnly(() -> getUsersWithPositiveTotalScore(roundId));
            int usersSize = usersWithPositiveTotalScore.size();
            log.info("resetManagerScores: usersWithPositiveTotalScore count = " + usersSize);
            transactionHandler.runInNewTransaction(() -> updateUserLevels(usersWithPositiveTotalScore));
            log.info("updateManagerScores: Update user levels done");
        } catch (Exception e) {
            log.error("updateUserLevels failed", e);
        }
    }

    @Transactional(propagation = MANDATORY)
    public void addMoneyToBalance(HmUserProfile user, HmLeague league, HmRound round, Integer money, HmLeagueMembership member, boolean createUserRoundScore) {
        leagueScoreUpdateHandler.addMoneyToBalance(user, league, round, money, member, createUserRoundScore);
    }

    public Optional<HmUserRoundScore> getUserRoundScore(HmRound round, HmUserProfile user, HmLeague league) {
        return userRoundScoreRepository.findByUserIdAndRoundIdAndLeagueId(user.getId(), round.getId(), league.getId());
    }

    public List<UserRoundRankingDto> getManagerRoundRankingByLeague(HmLeague league,String roundId) throws EntityNotExistException, FormatException {
        List<UserRoundRankingDto> userRoundRankings = new ArrayList<>();
        List<HmRound> alreadyPlayedRounds;
        //if roundId != null we get the specific round else we get all rounds of the season
        if (nonNull(roundId)) {
            alreadyPlayedRounds = List.of( seasonService.getRound(roundId));
        } else {
            alreadyPlayedRounds = seasonService.getAlreadyPlayedRounds();
        }
        for (HmRound round : alreadyPlayedRounds) {
            RoundDto roundDto = roundMapper.mapToDto(round);
            List<UserRankingDto> userRankings = new ArrayList<>();
            for (HmUserProfile user : league.getLeagueMemberships().stream().map(HmLeagueMembership::getUserProfile).filter(Objects::nonNull).toList()) {
                UserDto userDto = userMapper.mapToDto(user);
                if (userDto == null) {
                    continue;
                }
                Optional<HmUserRoundScore> userRoundScoreOpt = userRoundScoreRepository.findByUserIdAndRoundIdAndLeagueId(user.getId(), round.getId(), league.getId());
                if (userRoundScoreOpt.isPresent()) {
                    HmUserRoundScore userRoundScore = userRoundScoreOpt.get();
                    if (Objects.nonNull(userRoundScore.getScore())) {
                        userRankings.add(new UserRankingDto(userDto, userRoundScore.getScore()));
                    } else {
                        // User round score with null value means that the league memberships ist deleted and by this the membership was not evaluated
                        // orphan User round scores should be deleted by cleanup
                        log.info("getManagerRoundRankingByLeague: UserRoundScore id[" + userRoundScore.getId() + "] score = null detected.");
                    }
                }
            }
            List<UserRankingDto> userRankingsToSort = new ArrayList<>(userRankings);
            List<UserRankingDto> sortedUserRankings = userRankingsToSort.stream().sorted(Comparator.comparing(UserRankingDto::getScore).reversed()).toList();
            userRoundRankings.add(new UserRoundRankingDto(roundDto, sortedUserRankings));
        }
        return userRoundRankings;
    }

    public List<UserRankingDto> getManagerRankingByLeague(HmLeague league) {
        List<UserRankingDto> userRankings = new ArrayList<>();
        List<HmUserProfile> userProfiles = league.getLeagueMemberships().stream()
                .filter(m -> {
                    try {
                        return userProfileService.exists(m.getUserProfile().getId());
                    } catch (Exception e) {
                        // could happen on deleted users when trying to call getId()
                        return false;
                    }
                })
                .map(m -> {
                    try {
                        return m.getUserProfile();
                    } catch (Exception e) {
                        // could happen on deleted users
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .toList();
        for (HmUserProfile user : userProfiles) {
            int userLeagueScore = Util.toStream(userRoundScoreRepository.findByUserIdAndLeagueId(user.getId(), league.getId()))
                    .filter(urs -> nonNull(urs.getScore())).mapToInt(HmUserRoundScore::getScore).sum();
            userRankings.add(new UserRankingDto(userMapper.mapToDto(user), userLeagueScore));
        }
        List<UserRankingDto> sortedUserRankings = new ArrayList<>(userRankings);
        return sortedUserRankings.stream().sorted(Comparator.comparing(UserRankingDto::getScore).reversed()).toList();
    }

    // this should be called by premium users (new endpoint)
    public UserRankingResultDto getManagerRankingResultByLeague(HmLeague league) throws InvalidOperationException {
        HmRound currentRound;
        Optional<HmRound> previousRoundOptional;
        // use this one to hardly disable streaming on mobile
        // return new MatchResultDetailsDto(false, null, null, null, emptyList());
        try {
            currentRound = seasonService.getCurrentRound();
        } catch (Exception e) {
            log.info("current round not found: isMatchDayRunning = false. Skipping..");
            return new UserRankingResultDto(false, null, emptyList());
        }
        // The LIVE tab should not be locked immediately with the final whistle of the last game
        // (we are outside the game day...), but remain for another 5 hours
        try {
            previousRoundOptional = seasonService.getPreviousRound(currentRound.getId());
        } catch (Exception e) {
            log.info("previous round not found: isMatchDayRunning = false. Skipping..");
            return new UserRankingResultDto(false, null, emptyList());
        }

        // Used to display the results of the previous round after its closing
        HmRound previousRound = null;
        LocalDateTime previousMatchDayLiveEnd = null;

        LocalDateTime now = LocalDateTime.now();
        // disable streaming
        if (previousRoundOptional.isEmpty()) {
            if (now.isBefore(currentRound.getClosing())) {
                // No need to calculate the temporary user rankings because the result is not displayed in this case
                return new UserRankingResultDto(false, null, emptyList());
            }
        } else {
            // get the end date until which live results should be displayed
            previousRound = previousRoundOptional.get();
            previousMatchDayLiveEnd = seasonService.getLiveResultsDisplayEndDate(previousRound.getTo());
            if (now.isBefore(currentRound.getClosing()) && now.isAfter(previousMatchDayLiveEnd)) {
                // No need to calculate the temporary user rankings because the result is not displayed in this case
                return new UserRankingResultDto(false, null, emptyList());
            }
        }
        //HmRound round = Objects.isNull(previousRound) || now.isBefore(previousMatchDayLiveEnd)? previousRound: currentRound;
        HmRound round = Objects.nonNull(previousMatchDayLiveEnd) && now.isBefore(previousMatchDayLiveEnd)? previousRound: currentRound;
        ZonedDateTime nearestGameStart = matchService.getNearestGameStart(round, Constants.SYSTEM_USERNAME);
        // Calculate the temporary user rankings like in the user ranking streaming method
        List<UserLiveRankingDto> currentUserRankings = liveLeagueScoreHandler.calculateManagerRankingByLeagueOnTheFly(league.getId(), round);
        return new UserRankingResultDto(true, nearestGameStart, currentUserRankings);
    }

    public void deleteUserScoresByLeague(UUID leagueId) {
        userRoundScoreRepository.deleteByLeague(leagueId);
    }

    public void deleteScoresByLeagueAndUser(UUID leagueId, UUID userId) {
        userRoundScoreRepository.deleteByLeagueAndUser(leagueId, userId);
    }

    public int getTotalScoreByUserAndRound(UUID userId, UUID roundId) {
        List<HmUserRoundScore> userRoundScores = userRoundScoreRepository.findAllByUserIdAndRoundId(userId, roundId);
        log.info("found [" + userRoundScores.size() + "] UserRoundScore(s) by user " + userId + " and round " + roundId);
        return userRoundScores.stream().filter(s -> Objects.nonNull(s.getScore())).mapToInt(HmUserRoundScore::getScore).sum();
    }

    public void activateUserRoundScoresByUserAndLeague(UUID userId, UUID leagueId) {
        // Find all deleted user round scores by user and league
        List<HmUserRoundScoreDeletedDO> deletedUserRoundScores = userRoundScoreRepository.findDeletedUserRoundScoresByUserIdAndLeagueId(userId, leagueId);
        // group deleted user league score by round
        Map<String, List<HmUserRoundScoreDeletedDO>> userLeagueScoresByRoundId = deletedUserRoundScores.stream().collect(Collectors.groupingBy(HmUserRoundScoreDeletedDO::getRoundId));
        // keep only one deleted user round score by user, league and round: the last deleted one
        List<HmUserRoundScoreDeletedDO> distinctUserLeagueScoresByRound = userLeagueScoresByRoundId.values().stream().map(hmUserRoundScoreDeletedDOS -> hmUserRoundScoreDeletedDOS.stream().max(Comparator.comparing(HmUserRoundScoreDeletedDO::getDeletedAt)).orElse(null)).filter(Objects::nonNull).toList();
        // check if user round score already exists before activation the deleted one to avoid unique index constraint violation
        List<HmUserRoundScoreDeletedDO> userRoundScoresToActivate = distinctUserLeagueScoresByRound.stream().filter(urs ->
                !userRoundScoreRepository.existsByUserIdAndLeagueIdAndRoundId(userId, leagueId, UUID.fromString(urs.getRoundId()))).toList();

        int rows = userRoundScoreRepository.activateUserRoundScoresByIds(userRoundScoresToActivate.stream().map(ursDo -> UUID.fromString(ursDo.getId())).toList());
        log.info("activateUserRoundScoresByUserAndLeague: [" + rows + "] user round scores activated for user id[" + userId + "] and league id[" + leagueId + "]");
    }

    public void checkUserRoundScore(@NotNull HmUserProfile user, @NotNull HmLeague league, @NotNull LocalDateTime now) throws EntityNotExistException {
        HmRound currentRound = seasonService.getCurrentRound();
        if (now.isAfter(currentRound.getClosing()) && now.isBefore(currentRound.getTo())) {
            if (!userRoundScoreRepository.existsByUserIdAndLeagueIdAndRoundId(user.getId(), league.getId(), currentRound.getId())) {
                boolean isLeagueCreatedBeforeClosing = league.getCreatedAt().isBefore(currentRound.getClosing());
                // leagues that were created after closing are not evaluated. That's why the start balance will not be saved
                if (isLeagueCreatedBeforeClosing) {
                    int balance = leagueMembershipRepository.getBalance(league.getId(), user.getId());
                    userRoundScoreRepository.save(new HmUserRoundScore(user, league, currentRound, balance));
                }
            }
        }
    }

    public Map<String, String> getUserRoundScoreDetails(HmUserProfile user, HmLeague league, HmRound round) throws EntityNotExistException, FormatException {
        Map<String, String> result = new HashMap<>();
        List<HmLineup> lineupByLeague = teamService.getLineupByLeague(user, league.getId().toString(), round.getId().toString());
        List<Pair<UUID, String>> playerNameByIdPairs = lineupByLeague.stream().map(l -> {
            HmPlayer player = l.getTeam().getPlayer();
            return Pair.of(player.getId(), player.getFirstName() + ", " + player.getLastName());
        }).toList();
        // calculate the total round score of the manager's team
        List<UUID> playerIds = playerNameByIdPairs.stream().map(Pair::getLeft).toList();
        // No lineup found in the given round
        if (playerNameByIdPairs.isEmpty()) {
            result.put("Message", "lineup not found");
        } else {
            List<HmPlayerRoundScore> playerRoundScores = playerService.getPlayerRoundScores(playerIds, round.getId());
            int teamRoundScore = playerRoundScores.stream().mapToInt(HmPlayerRoundScore::getScore).sum();
            playerNameByIdPairs.forEach(pair -> {
                String playerName = pair.getRight();
                List<HmPlayerRoundScore> matchScoresByPlayer = playerRoundScores.stream().filter(prs -> Objects.equals(prs.getPlayer().getId(), pair.getLeft())).toList();
                if (matchScoresByPlayer.isEmpty()) {
                    result.put(playerName, "score not found");
                } else {
                    int playerScore = matchScoresByPlayer.stream().filter(Objects::nonNull).mapToInt(HmPlayerRoundScore::getScore).filter(Objects::nonNull).sum();
                    result.put(playerName, Integer.toString(playerScore));
                }
            });
            result.put("Total score", Integer.toString(teamRoundScore));
        }
        return result;
    }

    public Map<String, String> addPointsToUserRoundScore(HmUserProfile user, HmLeague league, HmRound round, int pointsToAdd) {
        return leagueScoreUpdateHandler.addRoundScoreToManager(user.getId(), league.getId(), round, pointsToAdd);
    }

    public List<HmUserRoundScoreTransactionDO> getUserRoundScoresSinceDate(UUID userId, UUID leagueId, LocalDateTime startDate) {
        return userRoundScoreRepository.findAllByUserIdAndLeagueIdSinceDate(userId, leagueId, startDate);
    }

    @Lock(LockModeType.READ)
    @Transactional(propagation = REQUIRES_NEW, readOnly = true)
    public boolean areAllLeaguesEvaluated(UUID roundId, List<HmLeague> leagues, String process) {
        Set<UUID> alreadyEvaluated = userRoundScoreRepository.findAlreadyEvaluatedLeaguesByRoundId(roundId);
        List<HmLeague> leaguesToProcess = leagues.stream()
                .filter(l -> !alreadyEvaluated.contains(l.getId()))
                .toList();

        if (leaguesToProcess.size() == 0) {
            log.info(process + ": all [" + leagues.size() + "] for round-Id [" + roundId + "] were already processed");
            return true;
        } else {
            log.info(process + ": Leagues to process for round-Id [" + roundId + "]: " + leaguesToProcess.size() + "/" + leagues.size());
            return false;
        }
    }

    public List<UserRoundRankingDto> getUserRoundRankingDtosFromCache(String leagueId, String roundId) {
        // Define cache key
        String cacheKey = null;
        List<UserRoundRankingDto> roundRankingDtos = new ArrayList<>();
        // Attempt to access the cache with a try-catch block to handle Redis issues
        try {
            // Access the cache named "leagueCash"
            Cache cache = cacheManager.getCache(LEAGUE_CACHE);
            UserRoundRankingDto userRoundRankingDto;
            List<HmRound> alreadyPlayedRounds = null;
            //for most security this part it will be conserved for the old call without roundId param
            if (isNull(roundId)) {
                alreadyPlayedRounds = seasonService.getAlreadyPlayedRounds();
                assert alreadyPlayedRounds != null;
                for (HmRound round : alreadyPlayedRounds) {
                    // cacheKey:leagueId:roundId 2cacheKey="leagueCache:9221d9c2-5fe0-43f5-b143-e9d7b651b643:8221d9c2-5fe0-43f5-b143-e9d7b651b647
                    cacheKey = cacheHandler.buildCacheKey(LEAGUE_CACHE, leagueId, round.getId());
                    // Check if the cache contains the specific key
                    userRoundRankingDto = cache.get(cacheKey, UserRoundRankingDto.class);
                    if (isNull(userRoundRankingDto)) {
                        // the round returned from cache doesn't contain the order of round, so we replace by another one
                        RoundDto roundDto = roundMapper.mapToDto(round);
                        userRoundRankingDto.setRound(roundDto);
                        roundRankingDtos.add(userRoundRankingDto);
                    }
                }
            } else {
                cacheKey = cacheHandler.buildCacheKey(LEAGUE_CACHE, leagueId, roundId);
                // Check if the cache contains the specific key
                userRoundRankingDto = cache.get(cacheKey, UserRoundRankingDto.class);
                if (nonNull(userRoundRankingDto)) {
                    roundRankingDtos.add(userRoundRankingDto);
                }
            }
        } catch (Exception e) {
            // Log the warning and fall back to the handler if Redis is unavailable
            log.warn("Redis cache access failed for cache " + LEAGUE_CACHE + " and key " + cacheKey + ", falling back to DB. Error: " + e.getMessage());
        }
        return roundRankingDtos;
    }
}
