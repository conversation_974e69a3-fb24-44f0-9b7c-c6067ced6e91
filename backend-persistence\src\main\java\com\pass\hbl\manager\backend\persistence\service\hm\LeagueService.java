package com.pass.hbl.manager.backend.persistence.service.hm;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.github.fge.jsonpatch.JsonPatch;
import com.github.fge.jsonpatch.JsonPatchException;
import com.pass.hbl.manager.backend.persistence.domain.admin.AdminLeagueMembersCountDO;
import com.pass.hbl.manager.backend.persistence.domain.hm.*;
import com.pass.hbl.manager.backend.persistence.dto.hm.*;
import com.pass.hbl.manager.backend.persistence.dto.shared.ImageDomain;
import com.pass.hbl.manager.backend.persistence.entity.hm.*;
import com.pass.hbl.manager.backend.persistence.exception.*;
import com.pass.hbl.manager.backend.persistence.mapper.hm.*;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmLeagueInvitationRepository;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmLeagueMembershipRepository;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmLeagueRepository;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmUserAwardRepository;
import com.pass.hbl.manager.backend.persistence.service.AbstractService;
import com.pass.hbl.manager.backend.persistence.service.hm.handlers.*;
import com.pass.hbl.manager.backend.persistence.service.hm.helpers.CacheHandler;
import com.pass.hbl.manager.backend.persistence.service.hm.helpers.LeagueHelper;
import com.pass.hbl.manager.backend.persistence.service.shared.ImageService;
import com.pass.hbl.manager.backend.persistence.service.shared.TransactionHandler;
import com.pass.hbl.manager.backend.persistence.util.PageableResult;
import com.pass.hbl.manager.backend.persistence.util.Util;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.reflections.Reflections;
import org.reflections.scanners.Scanners;
import org.reflections.util.ConfigurationBuilder;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.persistence.LockModeType;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;

import static com.pass.hbl.manager.backend.persistence.dto.hm.NotificationEvent.REMOVE_LEAGUE_MEMBER;
import static com.pass.hbl.manager.backend.persistence.service.hm.helpers.LeagueHelper.checkLeagueMembership;
import static com.pass.hbl.manager.backend.persistence.service.hm.helpers.LeagueHelper.checkLeagueOwnership;
import static com.pass.hbl.manager.backend.persistence.util.Constants.*;
import static com.pass.hbl.manager.backend.persistence.util.Util.sneakyThrow;
import static com.pass.hbl.manager.backend.persistence.util.Util.toZonedDateTime;
import static java.util.Collections.*;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static java.util.stream.Collectors.toList;

@Slf4j
@Service
@Transactional
public class LeagueService extends AbstractService<HmLeague, LeagueInfoDto> {

    private final HmLeagueRepository hmLeagueRepository;
    private final HmLeagueInfoMapper hmLeagueInfoMapper;
    private final HmLeagueMapper hmLeagueMapper;

    private final HmLeagueMembershipRepository leagueMembershipRepository;
    private final HmLeagueMembershipMapper hmLeagueMembershipMapper;
    private final HmLeagueInvitationRepository leagueInvitationRepository;
    private final HmLeagueInvitationMapper hmLeagueInvitationMapper;
    private final HmUserAwardRepository userAwardRepository;

    private final UserProfileService userProfileService;
    private final ImageService imageService;
    private final TeamService teamService;
    private final SeasonService seasonService;
    private final TransferMarketService transferMarketService;
    private final MessagingService messagingService;
    private final AwardService awardService;

    private final LeagueScoreHandler leagueScoreHandler;
    private final TransactionHandler transactionHandler;
    private final LeagueMembershipHandler leagueMembershipHandler;
    private final UserNotificationHandler userNotificationHandler;
    private final LeagueResetHandler leagueResetHandler;
    private final LeagueStateHandler leagueStateHandler;
    private final LeagueScoreAwardsHandler leagueScoreAwardsHandler;
    private final LeagueAdminHandler leagueAdminHandler;
    private final LeagueLobbyInfoHandler leagueLobbyInfoHandler;
    private final LeagueTransactionsHandler leagueTransactionsHandler;
    private final LeagueHelper leagueHelper;
    private final UserLoginHandler userLoginHandler;


    public LeagueService(HmLeagueInfoMapper hmLeagueInfoMapper,
                         HmLeagueRepository HmLeagueRepository,
                         HmLeagueMembershipRepository leagueMembershipRepository,
                         HmLeagueMembershipMapper hmLeagueMembershipMapper,
                         HmLeagueInvitationRepository hmLeagueInvitationRepository,
                         HmLeagueInvitationMapper hmLeagueInvitationMapper,
                         HmLeagueMapper hmLeagueMapper,
                         HmUserAwardRepository userAwardRepository,
                         @Lazy UserProfileService userProfileService,
                         @Lazy TeamService teamService,
                         @Lazy SeasonService seasonService,
                         @Lazy ImageService imageService,
                         TransferMarketService transferMarketService,
                         MessagingService messagingService, @Lazy AwardService awardService, LeagueScoreHandler leagueScoreHandler, TransactionHandler transactionHandler, LeagueMembershipHandler leagueMembershipHandler, UserNotificationHandler userNotificationHandler, LeagueResetHandler leagueResetHandler, LeagueStateHandler leagueStateHandler, @Lazy LeagueScoreAwardsHandler leagueScoreAwardsHandler, LeagueAdminHandler leagueAdminHandler, @Lazy LeagueLobbyInfoHandler leagueLobbyInfoHandler, @Lazy LeagueTransactionsHandler leagueTransactionsHandler, LeagueHelper leagueHelper, CacheHandler cacheHandler, UserLoginHandler userLoginHandler) {
        super(HmLeagueRepository, hmLeagueInfoMapper, HmLeague.class);
        this.hmLeagueInfoMapper = hmLeagueInfoMapper;
        this.hmLeagueRepository = HmLeagueRepository;
        this.userAwardRepository = userAwardRepository;
        this.imageService = imageService;
        this.leagueInvitationRepository = hmLeagueInvitationRepository;
        this.leagueMembershipRepository = leagueMembershipRepository;
        this.hmLeagueMembershipMapper = hmLeagueMembershipMapper;
        this.hmLeagueInvitationMapper = hmLeagueInvitationMapper;
        this.userProfileService = userProfileService;
        this.hmLeagueMapper = hmLeagueMapper;
        this.teamService = teamService;
        this.seasonService = seasonService;
        this.transferMarketService = transferMarketService;
        this.messagingService = messagingService;
        this.awardService = awardService;
        this.leagueScoreHandler = leagueScoreHandler;
        this.transactionHandler = transactionHandler;
        this.leagueMembershipHandler = leagueMembershipHandler;
        this.userNotificationHandler = userNotificationHandler;
        this.leagueResetHandler = leagueResetHandler;
        this.leagueStateHandler = leagueStateHandler;
        this.leagueScoreAwardsHandler = leagueScoreAwardsHandler;
        this.leagueAdminHandler = leagueAdminHandler;
        this.leagueLobbyInfoHandler = leagueLobbyInfoHandler;
        this.leagueTransactionsHandler = leagueTransactionsHandler;
        this.leagueHelper = leagueHelper;
        this.userLoginHandler = userLoginHandler;
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true)
    public PageableResult<LeagueInfoDto> search(String name, Boolean publicOnly, Boolean freeOnly, Pageable pageable) throws EntityNotExistException {
        HmSeason currentSeason = seasonService.getCurrentSeason();
        UUID currentSeasonId = currentSeason.getId();

        if (StringUtils.isNotEmpty(name)) {
            name = "%" + name + "%";
        }
        if (name == null && publicOnly == null && freeOnly == null) {
            return mapEntityPageToDtoPageableResult(hmLeagueRepository.findAllBySeasonId(currentSeasonId, pageable), hmLeagueInfoMapper, pageable);
        }
        if (name == null & publicOnly == null) {
            if (freeOnly) {
                return mapEntityPageToDtoPageableResult(hmLeagueRepository.findByFree(currentSeasonId, pageable), hmLeagueInfoMapper, pageable);
            } else {
                return mapEntityPageToDtoPageableResult(hmLeagueRepository.findByClosed(currentSeasonId, pageable), hmLeagueInfoMapper, pageable);
            }
        }
        if (name == null & freeOnly == null) {
            return mapEntityPageToDtoPageableResult(hmLeagueRepository.findBySeasonIdAndPublicAccess(currentSeasonId, publicOnly, pageable), hmLeagueInfoMapper, pageable);
        }
        if (publicOnly == null && freeOnly == null) {
            return mapEntityPageToDtoPageableResult(hmLeagueRepository.findBySeasonIdAndNameIgnoreCaseLike(currentSeasonId, name, pageable), hmLeagueInfoMapper, pageable);
        }
        if (name == null) {
            if (freeOnly) {
                return mapEntityPageToDtoPageableResult(hmLeagueRepository.findByPublicAccessAndFree(currentSeasonId, publicOnly, pageable), hmLeagueInfoMapper, pageable);
            } else {
                return mapEntityPageToDtoPageableResult(hmLeagueRepository.findByPublicAccessAndClosed(currentSeasonId, publicOnly, pageable), hmLeagueInfoMapper, pageable);
            }
        }
        if (publicOnly == null) {
            if (freeOnly) {
                return mapEntityPageToDtoPageableResult(hmLeagueRepository.findByNameIgnoreCaseLikeAndFree(currentSeasonId, name, pageable), hmLeagueInfoMapper, pageable);
            } else {
                return mapEntityPageToDtoPageableResult(hmLeagueRepository.findByNameIgnoreCaseLikeAndClosed(currentSeasonId, name, pageable), hmLeagueInfoMapper, pageable);
            }
        }
        if (freeOnly == null) {
            return mapEntityPageToDtoPageableResult(hmLeagueRepository.findBySeasonIdAndNameIgnoreCaseLikeAndPublicAccess(currentSeasonId, name, publicOnly, pageable), hmLeagueInfoMapper, pageable);
        }
        if (freeOnly) {
            return mapEntityPageToDtoPageableResult(hmLeagueRepository.findByNameIgnoreCaseLikeAndPublicAccessAndFree(currentSeasonId, name, publicOnly, pageable), hmLeagueInfoMapper, pageable);
        }
        return mapEntityPageToDtoPageableResult(hmLeagueRepository.findByNameIgnoreCaseLikeAndPublicAccessAndClosed(currentSeasonId, name, publicOnly, pageable), hmLeagueInfoMapper, pageable);
    }

    @Transactional(readOnly = true)
    public List<LeagueInfoDto> getUserLeagues(@NotNull String userId, boolean ownerOnly, boolean joinedOnly, boolean applicantsOnly) throws EntityNotExistException, FormatException {
        HmUserProfile userProfile = getUser(userId);
        return doGetUserLeaguesAsDtos(userProfile, ownerOnly, joinedOnly, applicantsOnly);
    }

    private List<LeagueInfoDto> doGetUserLeaguesAsDtos(HmUserProfile userProfile, boolean ownerOnly, boolean joinedOnly, boolean applicantsOnly) throws EntityNotExistException {
        HmSeason currentSeason = seasonService.getCurrentSeason();
        UUID currentSeasonId = currentSeason.getId();
        return Util.toStream(leagueMembershipRepository.findByLeagueSeasonIdAndUserProfile(currentSeasonId, userProfile))
                .filter(leagueMembership -> !joinedOnly || nonNull(leagueMembership.getJoined()))
                .filter(leagueMembership -> !applicantsOnly || Objects.isNull(leagueMembership.getJoined()))
                .map(HmLeagueMembership::getLeague)
                .filter(leagueInfo -> !ownerOnly || Objects.equals(leagueInfo.getOwner(), userProfile))
                .map(hmLeagueInfoMapper::mapToDto)
                .collect(toList());
    }

    public LeagueInfoDto getLeagueAsDto(@NotNull String userId, @NotNull String leagueId) throws EntityNotExistException, FormatException, ForbiddenOperationException {
        HmLeague hmLeague = getById(leagueId);
        checkLeagueMembership(hmLeague, userId, "Get league");
        return hmLeagueInfoMapper.mapToDto(hmLeague);
    }

    public HmLeague getLeague(@NotNull String userId, @NotNull String leagueId) throws EntityNotExistException, FormatException, ForbiddenOperationException {
        HmLeague hmLeague = getById(leagueId);
        checkLeagueMembership(hmLeague, userId, "Get league");
        return hmLeague;
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true)
    public LeagueDto getLeagueDetails(@NotNull String userId, @NotNull String leagueId) throws EntityNotExistException, FormatException {
        HmLeague hmLeague = getById(leagueId);
        LeagueDto leagueDto = hmLeagueMapper.mapToDto(hmLeague);
        try {
            checkLeagueMembership(hmLeague, userId, "getValue league details");
        } catch (ForbiddenOperationException e) {
            anonymize(leagueDto);
        }
        return leagueDto;
    }

    public void deleteLeague(@NotNull String userId, @NotNull String leagueId, boolean checkOwnership) throws EntityNotExistException, FormatException, ForbiddenOperationException {
        HmLeague hmLeague = getLeague(leagueId);
        if (checkOwnership) checkLeagueOwnership(hmLeague, userId, "Delete league");
        doDeleteLeague(hmLeague.getId());
    }

    private void doDeleteLeague(UUID leagueId) {
        transferMarketService.deleteAppearancesAndBidsByLeague(leagueId);
        leagueScoreHandler.deleteUserScoresByLeague(leagueId);
        teamService.deleteTeamsByLeague(leagueId);
        leagueInvitationRepository.deleteByLeague(leagueId);
        leagueMembershipRepository.deleteByLeague(leagueId);
        userAwardRepository.deleteByLeague(leagueId);
        hmLeagueRepository.clearPreviousLeague(leagueId);
        hmLeagueRepository.deleteById(leagueId);
    }

    public LeagueInfoDto create(@NotNull String name, boolean publicAccess, MultipartFile logo, @NotNull UserProfileDto dto) throws IOException, EntityNotExistException, FormatException, InvalidOperationException, ForbiddenOperationException, TechnicalException, SchedulingException {
        return doCreateLeague(name, publicAccess, logo, dto, null);
    }

    public LeagueInfoDto createFromPrevious(@NotNull String name, boolean publicAccess, MultipartFile logo,
                                            @NotNull UserProfileDto user, @NotNull String previousLeagueId) throws EntityNotExistException, FormatException, IOException, InvalidOperationException, ForbiddenOperationException, TechnicalException, SchedulingException {
        return doCreateLeague(name, publicAccess, logo, user, previousLeagueId);
    }

    private LeagueInfoDto doCreateLeague(String name, boolean publicAccess, MultipartFile logo, UserProfileDto user, String previousLeagueId) throws EntityNotExistException, FormatException, IOException, InvalidOperationException, ForbiddenOperationException, TechnicalException, SchedulingException {
        long start = System.currentTimeMillis();
        HmSeason hmSeason = seasonService.getCurrentSeason();
        if (previousLeagueId == null)
            leagueHelper.validateLeagueNameIsNotExistingOrBlacklisted("Create league", name, user.getId(), isNameBlackListed(name), hmSeason.getId());
        HmUserProfile owner = getUser(user.getId());
        HmLeague previousLeague = previousLeagueId == null ? null : getById(previousLeagueId);
        HmLeague hmLeague = new HmLeague(name, hmSeason, owner, publicAccess, previousLeague);
        if (logo != null) {
            hmLeague.setPicture(imageService.saveAsUUID(logo, ImageDomain.LEAGUE_LOGO));
        }

        if (hmLeague.getMaxSize() <= 0 || hmLeague.getMaxSize() > leagueHelper.getMaxLeagueSize()) {
            hmLeague.setMaxSize(leagueHelper.getMaxLeagueSize());
        }
        hmLeague = hmLeagueRepository.save(hmLeague);
        leagueMembershipHandler.join(owner, hmLeague, LocalDateTime.now(), true, false);
        LeagueInfoDto leagueInfoDto = hmLeagueInfoMapper.mapToDto(hmLeague);
        long end = System.currentTimeMillis();
        log.info("create league execution time: " + (end - start) + " milliseconds");
        return leagueInfoDto;
    }

    public LeagueInfoDto update(@NotNull LeagueInfoDto leagueDto, @NotNull String userId) throws EntityNotExistException, ForbiddenOperationException, FormatException, InvalidOperationException, TechnicalException {
        HmLeague existing = getById(leagueDto.getId());
        checkLeagueOwnership(existing, userId, "Modify league");
        String leagueName = leagueDto.getName();
        HmSeason season = seasonService.getCurrentSeason();
        if (!existing.getName().equalsIgnoreCase(leagueName))
            leagueHelper.validateLeagueNameIsNotExistingOrBlacklisted("Update league", leagueName, userId, isNameBlackListed(leagueName), season.getId());
        HmLeague hmLeague = hmLeagueInfoMapper.mapToEntity(leagueDto, existing);
        return doUpdateLeague(userId, hmLeague);
    }

    private LeagueInfoDto doUpdateLeague(String userId, HmLeague hmLeague) throws InvalidOperationException {
        if (leagueHelper.getLeagueSizeExcludingOnHoldMembers(hmLeague) > hmLeague.getMaxSize()) {
            throw new InvalidOperationException("Modify league", userId, "League size cannot be reduced as more members already joined");
        }

        if (hmLeague.getMaxSize() <= 0 || hmLeague.getMaxSize() > leagueHelper.getMaxLeagueSize()) {
            hmLeague.setMaxSize(leagueHelper.getMaxLeagueSize());
        }

        return hmLeagueInfoMapper.mapToDto(hmLeagueRepository.save(hmLeague));
    }


    public LeagueInfoDto update(@NotNull String leagueId, @NotNull JsonPatch patch, @NotNull String userId) throws EntityNotExistException, ForbiddenOperationException, FormatException, InvalidOperationException, JsonPatchException, JsonProcessingException {
        HmLeague existing = getById(leagueId);
        LeagueInfoDto existingDto = hmLeagueInfoMapper.mapToDto(existing);

        checkLeagueOwnership(existing, userId, "Modify league");
        JsonNode patched = patch.apply(getObjectMapper().convertValue(existingDto, JsonNode.class));
        LeagueInfoDto updatedDto = getObjectMapper().treeToValue(patched, LeagueInfoDto.class);

        Reflections reflections = new Reflections(new ConfigurationBuilder().forPackage(LeagueInfoDto.class.getPackageName()).setScanners(Scanners.FieldsAnnotated));
        reflections.getFieldsAnnotatedWith(Schema.class).stream()
                .filter(f -> f.getDeclaringClass().equals(LeagueInfoDto.class))
                .filter(f -> f.getAnnotation(Schema.class).accessMode() == Schema.AccessMode.READ_ONLY)
                .forEach(field -> {
                            field.setAccessible(true);
                            try {
                                if (!Objects.equals(field.get(existingDto), field.get(updatedDto))) {
                                    throw new InvalidOperationException("Update league", userId, field.getName() + " cannot be set via patch");
                                }
                            } catch (InvalidOperationException | IllegalAccessException e) {
                                Util.sneakyThrow(e);
                            }
                        }
                );

        HmLeague updated = hmLeagueInfoMapper.mapToEntity(updatedDto, existing);
        return doUpdateLeague(userId, updated);
    }


    public LeagueInfoDto setLogo(@NotNull String leagueId, @NotNull MultipartFile logo, @NotNull String userId) throws FormatException, EntityNotExistException, ForbiddenOperationException, IOException {
        HmLeague hmLeague = getById(leagueId);
        checkLeagueOwnership(hmLeague, userId, "Change league logo");
        imageService.delete(hmLeague.getPicture());
        hmLeague.setPicture(imageService.saveAsUUID(logo, ImageDomain.LEAGUE_LOGO));
        hmLeague = hmLeagueRepository.save(hmLeague);
        return hmLeagueInfoMapper.mapToDto(hmLeagueRepository.save(hmLeague));
    }

    public LeagueInvitationDto invite(@NotNull String userId, @NotNull String leagueId) throws EntityNotExistException, FormatException, ForbiddenOperationException {
        HmLeague hmLeague = getLeague(leagueId);
        if (!hmLeague.getPublicAccess()) checkLeagueOwnership(hmLeague, userId, "Create invitation for private league");
        HmLeagueInvitation invitation = new HmLeagueInvitation(LocalDateTime.now().plusDays(7), hmLeague,UUID.fromString(userId));
        return hmLeagueInvitationMapper.mapToDto(leagueInvitationRepository.save(invitation));
    }

    public void removePlayer(@NotNull String ownerId, @NotNull String leagueId, @NotNull String userId) throws EntityNotExistException, FormatException, ForbiddenOperationException {
        HmLeague league = getLeague(leagueId);
        HmUserProfile owner = getUser(ownerId);
        HmUserProfile user = getUser(userId);
        checkLeagueOwnership(league, ownerId, "Remove player");
        leagueMembershipHandler.deleteLeagueMember(user, league);
        UUID picture = league.getPicture();
        String leagueLogo = nonNull(picture) ? picture.toString() : null;
        Map<NotificationBodyKeyword, String> keywords = userNotificationHandler.getNotificationBodyKeywords(null, owner.getUsername(), null, league.getName(), null);
        Locale locale = Util.getLocaleByLanguageTagOrDefault(user.getAppLanguage());
        log.info("Firebase message with id [" + messagingService.sendNotification(userNotificationHandler.getNotificationTitle(REMOVE_LEAGUE_MEMBER, locale),
                userNotificationHandler.getNotificationBody(REMOVE_LEAGUE_MEMBER, keywords, locale),
                userId, new LeagueNotificationDo(leagueId, userId, REMOVE_LEAGUE_MEMBER, leagueLogo)) + "] sent. Event: " + REMOVE_LEAGUE_MEMBER);
    }


    public void removeLeagueMemberByAdmin(@NotNull String leagueId, @NotNull String userId) throws EntityNotExistException, FormatException, ForbiddenOperationException {
        leagueMembershipHandler.removeLeagueMemberByAdmin(getUser(userId), getLeague(leagueId));
        }

    public LeagueMembershipDto joinByInvitation(@NotNull String userId, @NotNull String invitationId) throws EntityNotExistException, FormatException, InvalidOperationException, ForbiddenOperationException, SchedulingException {
        Optional<HmLeagueInvitationDO> leagueMembershipInfoOpt = leagueInvitationRepository.findLeagueMembershipInfoById(UUID.fromString(invitationId));
        if (leagueMembershipInfoOpt.isEmpty()) {
            throw new InvalidOperationException("join league by invitation", userId, "invitation with id[" + invitationId + "] does not exist");
        }
        HmLeagueInvitationDO invitation = leagueMembershipInfoOpt.get();
        LocalDateTime now = LocalDateTime.now();
        if (now.isAfter(invitation.getValidUntil())) {
            // Do not delete the invitation: it can be user multiple times
            throw new InvalidOperationException("join by invitation", userId, "invitation " + invitationId + " already has expired");
        }

        leagueHelper.checkLeagueCompleteness(UUID.fromString(invitation.getLeagueId()), userId);

        Optional<HmLeagueMembershipJoinedDO> membershipJoinedOpt = leagueMembershipRepository.findJoinedInfoByUserIdAndLeagueId(UUID.fromString(userId), UUID.fromString(invitation.getLeagueId())).stream().findAny();
        UUID leagueMembershipId;
        LocalDateTime joinedDate = now;
        HmLeagueMembershipJoinedDO membershipJoinedDO;
        if (membershipJoinedOpt.isPresent()) {
            membershipJoinedDO = membershipJoinedOpt.get();
            leagueMembershipId = membershipJoinedDO.getId();
            if (membershipJoinedDO.getJoined() == null) {
                leagueMembershipRepository.setJoinedDateById(membershipJoinedDO.getId(), now);
                log.debug("User " + userId + " is already attendant of league " + invitation.getLeagueId() + ". Using invitation " + invitationId + " to approve membership.");
            } else {
                joinedDate = membershipJoinedDO.getJoined();
                log.debug("User " + userId + " is already accepted member of league " + invitation.getLeagueId() + ". Just devalue invitation " + invitationId);
            }
        } else {
            log.debug("User " + userId + " joined new league " + invitation.getLeagueId());
            leagueMembershipId = leagueMembershipHandler.join(getUser(userId), getLeague(invitation.getLeagueId()), now, false, false);
            //leagueMembershipId = leagueMembership.getId();
        }
        // Do not delete the invitation: it can be user multiple times
        return new LeagueMembershipDto(leagueMembershipId.toString(), userId, invitation.getLeagueId(), toZonedDateTime(joinedDate), null);
    }

    public LeagueMembershipDto joinPublic(@NotNull String userId, @NotNull String leagueId) throws EntityNotExistException, FormatException, ForbiddenOperationException, InvalidOperationException, SchedulingException {
        HmLeague hmLeague = getLeague(leagueId);
        if (!hmLeague.getPublicAccess()) {
            throw new ForbiddenOperationException("Join league", userId, "League is not public");
        }
        LocalDateTime now = LocalDateTime.now();
        UUID leagueMembershipId = leagueMembershipHandler.join(getUser(userId), hmLeague, now, false, false);
        return new LeagueMembershipDto(leagueMembershipId.toString(), userId, leagueId, toZonedDateTime(now), null);
    }

    @Lock(LockModeType.PESSIMISTIC_WRITE)
    public void leave(@NotNull String userId, @NotNull String leagueId) throws EntityNotExistException, FormatException {
        HmUserProfile user = getUser(userId);
        doLeaveLeague(Util.convertId(leagueId), user, true);
    }

    public void doLeaveLeague(UUID leagueId, HmUserProfile user, boolean keepUser) {
        UUID userId = user.getId();
        if (!isMember(userId, leagueId)) {
            log.info("Leave league [" + leagueId + "] skipped for user [" + userId + "]. Reason: user is not member of the league or already left the league");
            return;
        }
        UUID ownerId = getOwnerIdByLeagueId(leagueId);
        // user ist the owner of the league
        if (Objects.nonNull(ownerId) && ownerId.equals(userId)) {
            Optional<UUID> firstMemberJoinedOpt = getFirstMemberJoinedLeague(userId, leagueId);
            // league has other members
            if (firstMemberJoinedOpt.isPresent()) {
                hmLeagueRepository.updateOwner(leagueId, firstMemberJoinedOpt.get());
                //leave league: set league membership as on hold if user should be kept, otherwise false
                leagueMembershipHandler.doDeleteLeagueMember(leagueId, userId, keepUser);
                // league has only the owner as member
            } else {
                doDeleteLeague(leagueId);
            }
        } else {
            //leave league: set league membership as on hold if user should be kept, otherwise false
            leagueMembershipHandler.doDeleteLeagueMember(leagueId, userId, keepUser);
        }
        // True, if the user leaves the league but will be kept, otherwise the user will be deleted
        // If user will be deleted, no need to set a new current league.
        if (keepUser) {
            Set<UUID> allLeaguesByUser = transactionHandler.runInNewTransactionReadOnly(() -> getAllLeaguesInCurrentSeasonByUser(user.getId()));
            // assign a random league of his leagues to the user
            Optional<UUID> newCurrentLeagueIdOpt = allLeaguesByUser.stream().filter(id -> !(id.equals(leagueId))).findAny();
            if (newCurrentLeagueIdOpt.isPresent()) {
                userProfileService.setSessionAttribute(user, SessionAttribute.CURRENT_LEAGUE, newCurrentLeagueIdOpt.get().toString());
            } else {
                userProfileService.setSessionAttribute(user, SessionAttribute.CURRENT_LEAGUE, null);
            }
        }
    }

    public UUID getOwnerIdByLeagueId(UUID leagueId) {
        return hmLeagueRepository.findLeagueOwnerIdById(leagueId);
    }

    @Transactional(readOnly = true)
    public List<LeagueMembershipDto> getApplicants(@NotNull String userId, @NotNull String leagueId) throws ForbiddenOperationException, FormatException, EntityNotExistException {
        HmLeague hmLeague = getById(leagueId);
        checkLeagueOwnership(hmLeague, userId, "getValue applicants");
        return Util.toStream(hmLeague.getApplicants()).map(hmLeagueMembershipMapper::mapToDto).toList();
    }

    public LeagueMembershipDto getMembershipAsDto(@NotNull String membershipId) throws EntityNotExistException, FormatException {
        return hmLeagueMembershipMapper.mapToDto(doGetById(leagueMembershipRepository, membershipId, HmLeagueMembership.class));
    }

    @Transactional(propagation = Propagation.MANDATORY)
    public void addMoneyToBalance(HmUserProfile user, HmLeague league, Integer money) throws EntityNotExistException {
        if (isNull(money)) {
            return;
        }
        Optional<HmLeagueMembership> hmLeagueMembershipOptional = getMembership(user, league);
        if (hmLeagueMembershipOptional.isPresent()) {
            leagueScoreHandler.addMoneyToBalance(user, league, seasonService.getCurrentRound(), money, hmLeagueMembershipOptional.get(), true);
        } else {
            throw new EntityNotExistException(HmLeagueMembership.class, "league_id", league.getId().toString());
        }
    }

    public Optional<HmLeagueMembership> getMembership(@NotNull HmUserProfile userProfile, @NotNull HmLeague league) {
        return leagueMembershipRepository.findFirstByUserProfileAndLeague(userProfile, league);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true)
    public Optional<HmLeagueMembership> getMembership(@NotNull UUID userProfileId, @NotNull UUID leagueId) {
        return leagueMembershipRepository.findFirstByUserProfileIdAndLeagueId(userProfileId, leagueId);
    }

    @SuppressWarnings("BooleanMethodIsAlwaysInverted")
    public boolean isMember(@NotNull UUID userProfileId, @NotNull UUID leagueId) {
        return leagueMembershipRepository.existsByUserProfileIdAndLeagueId(userProfileId, leagueId);
    }

    public void updateApplicants(@NotNull String userId, @NotEmpty List<LeagueMembershipDto> applicants, boolean accept) {
        applicants.stream().map(LeagueMembershipDto::getLeagueId).forEach(leagueId -> {
            try {
                HmLeague hmLeague = getLeague(leagueId);
                checkLeagueOwnership(hmLeague, userId, "update applicants");
            } catch (ForbiddenOperationException | FormatException | EntityNotExistException e) {
                sneakyThrow(e);
            }
        });

        if (accept) {
            leagueMembershipRepository.saveAll(applicants.stream().map(lm -> {
                try {
                    return getMembership(lm.getId());
                } catch (EntityNotExistException | FormatException e) {
                    sneakyThrow(e);
                    return null;
                }
            }).peek(m -> m.setJoined(LocalDateTime.now())).toList());
        } else {
            leagueMembershipRepository.deleteAll(applicants.stream().map(lm -> {
                try {
                    return getMembership(lm.getId());
                } catch (EntityNotExistException | FormatException e) {
                    sneakyThrow(e);
                    return null;
                }
            }).toList());
        }
    }

    @Transactional(readOnly = true)
    public WelcomeDataDto getWelcomeDataV2(@NotNull String userId, int minVersionRequired, @NotNull String appVersion, String appLanguage) throws EntityNotExistException, FormatException, InvalidOperationException, ForbiddenOperationException {
        return getWelcomeData(userId, minVersionRequired, appVersion, appLanguage, true);
    }

    @Transactional(readOnly = true)
    public WelcomeDataDto getWelcomeDataV2(@NotNull String userId, int minVersionRequired) throws EntityNotExistException, FormatException, InvalidOperationException, ForbiddenOperationException {
        return getWelcomeData(userId, minVersionRequired, null, null, false);
    }

    @Transactional(readOnly = true)
    public WelcomeDataDto getWelcomeDataV1(@NotNull String userId, int minVersionRequired) throws EntityNotExistException, FormatException, InvalidOperationException, ForbiddenOperationException {
        return getWelcomeData(userId, minVersionRequired, DEFAULT_APP_VERSION, DEFAULT_APP_LANGUAGE, true);
    }

    @org.jetbrains.annotations.NotNull
    private WelcomeDataDto getWelcomeData(String userId, int minVersionRequired, String appVersion, String appLanguage, boolean userRequest) throws FormatException, EntityNotExistException, InvalidOperationException, ForbiddenOperationException {
        HmUserProfile user = userProfileService.getById(userId);
        List<LeagueInfoDto> leagues = doGetUserLeaguesAsDtos(user, false, true, false);

        // Record user login after generating welcome data
        try {
            userLoginHandler.processUserLogin(user);
        } catch (Exception e) {
            log.warn("getWelcomeData: failed to record user login, reason: " + e.getMessage());
        }

        // fix invalid current league
        final String currentLeague = user.getSessionAttribute(SessionAttribute.CURRENT_LEAGUE);
        if (StringUtils.isNotEmpty(currentLeague) && Util.toStream(leagues).map(LeagueInfoDto::getId).noneMatch(s -> Objects.equals(s, currentLeague))) {
            String newCurrentLeague = Util.toStream(leagues).map(LeagueInfoDto::getId).findFirst().orElse(null);
            if (StringUtils.isNotEmpty(newCurrentLeague)) {
                userProfileService.setUserCurrentLeague(user.getId(), newCurrentLeague);
            } else {
                userProfileService.setUserCurrentLeague(user.getId(), null);
            }
        }

        // update user app version if changed, only in case of user request. Not valid for admin request
        String currentAppVersion = user.getAppVersion();
        // No update for v1 since all users have default app version and the method is called with default app version
        if (userRequest && !Objects.equals(currentAppVersion, appVersion)) {
            userProfileService.setAppVersion(user.getId(), appVersion);
        }

        // update the user specific app language if changed, only in case of user request. Not valid for admin request
        String currentAppLanguage = user.getAppLanguage();
        if (userRequest && nonNull(appLanguage) && nonNull(currentAppLanguage) && !Objects.equals(currentAppLanguage.toLowerCase(), appLanguage.toLowerCase())) {
            userProfileService.setUserAppLanguage(user.getId(), appLanguage);
        }

        WelcomeDataDto welcomeDataDto = getWelcomeDataDto(userId, minVersionRequired, user, leagues);

        // reset manual subscription flag & downgrade user, if the manual subscription is active but expired
        if (user.isManualSubscription() && welcomeDataDto.getUser().getManualSubscriptionDaysLeft() == null) {
            int row = userProfileService.downgradeManualSubscription(user.getId());
            if (row == 1) welcomeDataDto.getUser().setPremium(false);
        }
        return welcomeDataDto;
    }

    @org.jetbrains.annotations.NotNull
    private WelcomeDataDto getWelcomeDataDto(String userId, int minVersionRequired, HmUserProfile user, List<LeagueInfoDto> leagues) throws EntityNotExistException, FormatException, InvalidOperationException, ForbiddenOperationException {
        WelcomeDataDto info = new WelcomeDataDto();
        info.setLeagues(leagues);
        info.setPendingLeagues(doGetUserLeaguesAsDtos(user, false, false, true));
        info.setUser(userProfileService.mapToReducedDto(user));
        info.setSessionAttributes(user.getSessionAttributes());
        HmSeason currentSeason = seasonService.getCurrentSeason();
        info.setCurrentSeason(seasonService.getSeasonLabel(currentSeason));
        info.setMinVersionRequired(minVersionRequired);
        if (nonNull(user.getPicture())) {
            info.setPicture(user.getPicture().toString());
        }
        if (StringUtils.isNotEmpty(info.getSessionAttributes().get(SessionAttribute.CURRENT_LEAGUE))) {
            info.setCurrentUserScore(getUserScore(userId, info.getSessionAttributes().get(SessionAttribute.CURRENT_LEAGUE), false));
        }
        Boolean isTransferMarketActive = transferMarketService.getIsTransferMarketActive();
        // activate transfer market (default) if isTransferMarketActive = null
        info.setTransferMarketActive(Objects.isNull(isTransferMarketActive) || isTransferMarketActive);
        info.setTransferMarketInactiveReason(transferMarketService.getTransferMarketInactiveReason());
        return info;
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true)
    public UserScoreDto getUserScore(@NotNull String userId, @NotNull String leagueId, boolean setAsCurrentLeague) throws EntityNotExistException, FormatException, InvalidOperationException, ForbiddenOperationException {
        if (StringUtils.isEmpty(leagueId)) {
            throw new InvalidOperationException("Get user score", userId, "No league provided");
        }

        HmUserProfile userProfile = getUser(userId);
        HmLeague league = getById(leagueId);

        if (setAsCurrentLeague) {
            userProfile.setSessionAttribute(SessionAttribute.CURRENT_LEAGUE, leagueId);
            userProfile = userProfileService.save(userProfile);
        }

        Optional<HmLeagueMembership> membershipOptional = leagueMembershipRepository.findFirstByUserProfileAndLeague(userProfile, league);
        if (membershipOptional.isEmpty()) {
            throw new ForbiddenOperationException("Get user score", userId, "User not member of league " + leagueId);
        }

        return new UserScoreDto(UUID.randomUUID().toString(), userProfile.getLevel(), membershipOptional.get().getScore(), membershipOptional.get().getBalance());
    }

    public LeagueInfoDto mapToDto(HmLeague entity) {
        return hmLeagueInfoMapper.mapToDto(entity);
    }

    @Transactional(readOnly = true)
    public HmLeagueMembership getMembership(String id) throws EntityNotExistException, FormatException {
        return doGetById(leagueMembershipRepository, id, HmLeagueMembership.class);
    }

    @SuppressWarnings("BooleanMethodIsAlwaysInverted")
    @Transactional(propagation = Propagation.MANDATORY)
    public boolean addMoneyToUserBalance(@NotNull UUID leagueId, @NotNull UUID userId, int amount) {
        return leagueMembershipRepository.updateBalance(leagueId, userId, amount) > 0;
    }

    @Transactional(propagation = Propagation.MANDATORY, readOnly = true)
    public int getBalance(@NotNull UUID leagueId, @NotNull UUID userId) {
        return leagueMembershipRepository.getBalance(leagueId, userId);
    }

    private HmLeague getLeague(String id) throws FormatException, EntityNotExistException {
        return transactionHandler.runInNewTransactionReadOnly(() -> {
            try {
                return getById(id);
            } catch (Exception e) {
                log.error("Entity HmLeague with id[" + id + "] not found");
                return null;
            }
        });

    }

    private HmUserProfile getUser(String id) throws EntityNotExistException, FormatException {
        return userProfileService.getByIdInNewTransaction(id);
    }

    private void anonymize(LeagueDto dto) {
        Util.toStream(dto.getMembers()).forEach(UserDto::anonymize);
        Util.toStream(dto.getApplicants()).forEach(UserDto::anonymize);
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true)
    public LeagueDto getLeagueByInvitation(String invitationId) throws FormatException, EntityNotExistException {
        HmLeagueInvitation leagueInvitation = doGetById(leagueInvitationRepository, invitationId, HmLeagueInvitation.class);
        return hmLeagueMapper.mapToDto(leagueInvitation.getLeague());
    }

    public void updateManagerScores(HmRound round, boolean firstResultInRound) throws EntityNotExistException {
        List<HmLeague> allLeaguesForRoundResult = leagueHelper.getAllRoundResultLeagues(round, seasonService.getCurrentSeason().getId(), "updateManagerScores");
        leagueScoreHandler.updateManagerScores(round, allLeaguesForRoundResult, firstResultInRound, false);
    }

    public void correctManagerScores(HmRound round) throws EntityNotExistException {
        UUID currentSeasonId = seasonService.getCurrentSeason().getId();
        List<HmLeague> allLeaguesInSeason = leagueHelper.getAllRoundResultLeagues(round, currentSeasonId, "correctManagerScores");
        //boolean areAllLeaguesEvaluated = leagueScoreHandler.areAllLeaguesEvaluated(round.getId(), allLeaguesInSeason, "correctManagerScores");
        // temporary removed in order to handle leagues with no memberships
        boolean areAllLeaguesEvaluated = true;
        if (areAllLeaguesEvaluated) {
            leagueScoreHandler.resetAllManagerScores(round, currentSeasonId, allLeaguesInSeason);
            leagueScoreHandler.updateManagerScores(round, allLeaguesInSeason, false, false);
        } else {
            log.info("correctManagerScores: there are still not evaluated leagues for round Id[" + round.getId() + "]. Correction of manager leagues skipped..");
        }
    }

    // Simulates the update of all manager scores
    public void updateAllScores(String roundId, boolean firstResultInRound) throws EntityNotExistException, FormatException {
        updateManagerScores(seasonService.getRound(roundId), firstResultInRound);
    }

    // Simulates the correction of all manager scores
    public void correctAllScores(String roundId) throws EntityNotExistException, FormatException {
        correctManagerScores(seasonService.getRound(roundId));
    }

    // Simulates the reset of all manager scores
    public void resetAllScores(String roundId) throws EntityNotExistException, FormatException {
        UUID currentSeasonId = seasonService.getCurrentSeason().getId();
        HmRound round = seasonService.getRound(roundId);
        List<HmLeague> allLeaguesInSeason = leagueHelper.getAllRoundResultLeagues(round, currentSeasonId, "resetAllScores");
        leagueScoreHandler.resetAllManagerScores(round, currentSeasonId, allLeaguesInSeason);
    }

    // Simulates the reset of manager scores by given leagues
    public void resetLeaguesScores(String roundId, List<String> leagueIds) throws EntityNotExistException, FormatException {
        List<UUID> ids = leagueIds.stream().map(UUID::fromString).toList();
        leagueScoreHandler.resetManagerScoresByLeagues(seasonService.getRound(roundId), ids, false);
    }

    // Simulates the update of manager scores by given leagues
    public void updateLeaguesScores(String roundId, List<String> leagueIds) throws EntityNotExistException, FormatException {
        List<UUID> ids = leagueIds.stream().map(UUID::fromString).toList();
        List<HmLeague> leagues = transactionHandler.runInNewTransactionReadOnly(() -> hmLeagueRepository.findAllByIdIn(ids));
        leagueScoreHandler.updateManagerScores(seasonService.getRound(roundId), leagues, true, true);
    }

    // Simulates the correction of manager scores by given leagues
    public void correctLeaguesScores(String roundId, List<String> leagueIds) throws EntityNotExistException, FormatException {
        HmRound round = seasonService.getRound(roundId);
        List<UUID> ids = leagueIds.stream().map(UUID::fromString).toList();
        leagueScoreHandler.resetManagerScoresByLeagues(round, ids, false);
        leagueScoreHandler.updateManagerScores(round,
                transactionHandler.runInNewTransactionReadOnly(() -> hmLeagueRepository.findAllByIdIn(ids)), false, true);
    }

    // Simulates the update of all manager levels based on the score of a given round
    public void updateAllUserLevels(String roundId) {
        leagueScoreHandler.updateUserLevels(UUID.fromString(roundId));
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true)
    public List<UserRoundRankingDto> getManagerRoundRankingByLeague(String leagueId, String roundId) throws EntityNotExistException, FormatException {
        List<UserRoundRankingDto> roundRankingDtos = leagueScoreHandler.getUserRoundRankingDtosFromCache(leagueId, roundId);
        // If the data is not found in the cache, fetch it from the handler (or DB equivalent)
        if (roundRankingDtos.isEmpty()) {
            // Log info when we are going to fallback to the handler or DB
            log.info("getManagerRoundRankingByLeague: Failed to get roundRankingDtos from Cache, fetching result from the database for leagueId: " + leagueId);            roundRankingDtos = leagueScoreHandler.getManagerRoundRankingByLeague(getById(leagueId), roundId);
        }
        return roundRankingDtos;
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true)
    public List<UserRankingDto> getManagerRankingByLeague(String leagueId) throws EntityNotExistException, FormatException {
        return leagueScoreHandler.getManagerRankingByLeague(getById(leagueId));
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true)
    public UserRankingResultDto getManagerRankingResultByLeague(String leagueId) throws EntityNotExistException, FormatException, InvalidOperationException {
        return leagueScoreHandler.getManagerRankingResultByLeague(getById(leagueId));
    }

    public void cleanupMembers(String leagueId) throws EntityNotExistException, FormatException {
        HmLeague league = getById(leagueId);
        leagueMembershipHandler.doCleanupMembers(league);
    }

    public void cleanupAllLeagues() {
        List<HmLeague> leagues = Util.toStream(hmLeagueRepository.findAll()).toList();
        for (HmLeague league : leagues) leagueMembershipHandler.doCleanupMembers(league);
    }

    @Transactional(readOnly = true)
    public int getMemberBalanceAtRoundClosing(HmLeagueMembership leagueMembership, HmRound round) {
        Optional<HmUserRoundScore> userRoundScoreOpt = leagueScoreHandler.getUserRoundScore(round, leagueMembership.getUserProfile(), leagueMembership.getLeague());
        // balance change was detected in the interval [closing - end]
        if (userRoundScoreOpt.isPresent()) {
            Integer startBalance = userRoundScoreOpt.get().getStartBalance();
            return isNull(startBalance) ? leagueMembership.getBalance() : startBalance;
        } else {
            return leagueMembership.getBalance();
        }
    }

    public WelcomeDataDto getDefaultWelcomeData(int minVersionRequired) throws EntityNotExistException {
        WelcomeDataDto info = new WelcomeDataDto();
        info.setLeagues(emptyList());
        info.setPendingLeagues(emptyList());
        info.setSessionAttributes(emptyMap());
        HmSeason currentSeason = seasonService.getCurrentSeason();
        info.setCurrentSeason(seasonService.getSeasonLabel(currentSeason));
        info.setMinVersionRequired(minVersionRequired);
        return info;
    }

    @Transactional(readOnly = true)
    public Boolean checkIfLeagueNameAvailable(String name) throws TechnicalException, EntityNotExistException {
        HmSeason currentSeason = seasonService.getCurrentSeason();
        return !(leagueHelper.leagueNameExists(name, currentSeason.getId()) || isNameBlackListed(name));
    }

    public WelcomeDataDto keepOnlyOneLeague(String id, String leagueId, int minVersionRequired) throws EntityNotExistException, FormatException, InvalidOperationException {
        HmUserProfile user = userProfileService.getById(id);
        UUID userId = user.getId();
        if (!isMember(userId, UUID.fromString(leagueId))) {
            throw new InvalidOperationException("Keep only one league", id, "user is not member of the league [" + leagueId + "]");
        }
        List<UUID> allLeaguesExceptingOne = getAllLeaguesInCurrentSeasonByUser(user.getId()).stream().filter(tempLeagueId -> !tempLeagueId.toString().equals(leagueId)).toList();
        List<HmLeagueOwnerDO> leagueOwnerDoList = hmLeagueRepository.findAllLeagueOwnerInfo(allLeaguesExceptingOne);
        if (leagueOwnerDoList.isEmpty())
            throw new InvalidOperationException("Keep only one league for user: " + id, id, "User has no leagues");
        for (HmLeagueOwnerDO leagueOwnerDO : leagueOwnerDoList) {
            //User is league owner
            UUID tempLeagueId = leagueOwnerDO.getId();
            if (leagueOwnerDO.getOwnerId().equals(userId)) {
                Optional<UUID> firstMemberJoinedOpt = getFirstMemberJoinedLeague(user.getId(), tempLeagueId);
                // league has other members
                if (firstMemberJoinedOpt.isPresent()) {
                    hmLeagueRepository.updateOwner(tempLeagueId, firstMemberJoinedOpt.get());
                    //leave league
                    leagueMembershipHandler.doDeleteLeagueMember(tempLeagueId, userId, true);
                    // league has only the owner as member
                } else {
                    doDeleteLeague(tempLeagueId);
                }
            } else {
                //leave league
                leagueMembershipHandler.doDeleteLeagueMember(tempLeagueId, userId, true);
            }
        }
        user.setSessionAttribute(SessionAttribute.CURRENT_LEAGUE, leagueId);
        return transactionHandler.runInNewTransactionReadOnly(() -> {
            try {
                return getWelcomeDataDto(id, minVersionRequired, user, List.of(hmLeagueInfoMapper.mapToDto(getById(leagueId))));
            } catch (Exception e) {
                return null;
            }
        });
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true)
    public List<HmLeagueOwnerDO> getHmLeagueOwnersByLeagueIds(List<UUID> leagueIds) {
        return hmLeagueRepository.findAllLeagueOwnerInfo(leagueIds);
    }

    private Optional<UUID> getFirstMemberJoinedLeague(UUID ownerId, UUID leagueId) {
        List<HmLeagueMembershipJoinedDO> leagueMembershipJoinedDos = leagueMembershipRepository.findAllByLeagueNotEqualUserIdOrderByJoinedAsc(leagueId, ownerId);
        if (leagueMembershipJoinedDos.isEmpty()) {
            return Optional.empty();
        } else {
            HmLeagueMembershipJoinedDO firstLeagueMembershipJoinedDo = leagueMembershipJoinedDos.get(0);
            return Optional.of(firstLeagueMembershipJoinedDo.getMemberId());
        }
    }

    public void activateOnHoldLeaguesByUser(UUID userId) {
        List<HmLeagueMembershipDO> leagueMembershipDOs = leagueMembershipRepository.findOnHoldMembershipsByUserId(userId);
        if (leagueMembershipDOs.isEmpty()) {
            log.info("activateOnHoldLeaguesByUser: User id[" + userId + "] has no on hold memberships. Skipping..");
            return;
        }
        //filter all memberships of leagues with full members (max membersCount)
        List<HmLeagueMembershipDO> leagueMembershipsToActivate = leagueMembershipDOs.stream().filter(lm -> {
            try {
                return !leagueHelper.isLeagueComplete(UUID.fromString(lm.getLeagueId()), userId);
            } catch (Exception e) {
                return false;
            }
        }).toList();
        // activate the league memberships, user awards and user round scores; Team and lineup were not deleted for on hold league memberships
        int rows = leagueMembershipRepository.activateMembershipsByIds(leagueMembershipsToActivate.stream().map(m -> UUID.fromString(m.getId())).toList());
        log.info("activateOnHoldLeaguesByUser: [" + rows + "] on hold memberships activated for user id[" + userId + "]");
        awardService.activateAwardsByUserAndLeagues(userId, leagueMembershipsToActivate.stream().map(m -> UUID.fromString(m.getLeagueId())).toList());
        for (HmLeagueMembershipDO leagueMembershipDO : leagueMembershipsToActivate) {
            leagueScoreHandler.activateUserRoundScoresByUserAndLeague(UUID.fromString(leagueMembershipDO.getMemberId()), UUID.fromString(leagueMembershipDO.getLeagueId()));
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true)
    public Set<UUID> getAllLeaguesInCurrentSeasonByUser(UUID userId) {
        try {
            UUID seasonId = seasonService.getCurrentSeason().getId();
            return leagueMembershipRepository.findLeagueIdsByUserProfileIdAndSeasonId(userId, seasonId);
        } catch (Exception e) {
            log.error("getAllLeaguesInCurrentSeasonByUser for user id[" + userId + "] failed. Skipping ..", e);
            return emptySet();
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true)
    public Set<UUID> getAllLeaguesByUserInAllSeasons(UUID userId) {
        return leagueMembershipRepository.findLeagueIdsByUserProfileId(userId);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true)
    public String getLeagueNameById(UUID id) {
        return hmLeagueRepository.findLeagueNameById(id);
    }

    public void checkOnHoldMemberships(LocalDateTime now) {
        leagueMembershipHandler.checkOnHoldMemberships(now);
    }

    @Transactional(readOnly = true)
    public void assignAwardsToAllLeagueWinners(String seasonId) {
        leagueScoreAwardsHandler.assignAwardsToAllLeagueWinners(seasonId);
    }

    public void resetAllLeaguesBySeasonChange(String oldSeasonId, String newSeasonId) throws EntityNotExistException, FormatException {
        leagueResetHandler.resetAllLeaguesBySeasonChange(oldSeasonId, newSeasonId);
    }

    public void revertResetAllLeaguesBySeasonChange(String newSeasonId) throws EntityNotExistException, FormatException {
        leagueResetHandler.revertResetAllLeaguesBySeasonChange(newSeasonId);
    }

    @Transactional(readOnly = true)
    public UUID getPreviousLeagueId(UUID id) {
        return hmLeagueRepository.findPreviousLeagueById(id);
    }

    @Lock(LockModeType.READ)
    @Transactional(propagation = Propagation.REQUIRES_NEW ,readOnly = true)
    public List<Pair<UUID, Boolean>> getAllIdsInCurrentSeason() throws EntityNotExistException {
        return hmLeagueRepository.findAllBySeasonId(seasonService.getCurrentSeason().getId()).stream().map(league -> Pair.of(league.getId(), league.isActive())).toList();
    }

    @Lock(LockModeType.PESSIMISTIC_WRITE)
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public int setLeagueActiveState(UUID leagueId, boolean activeState) {
        return leagueStateHandler.setLeagueActiveState(leagueId, activeState);
    }

    public void setAllLeaguesActiveState() throws EntityNotExistException, FormatException {
        List<Pair<UUID, Boolean>> allIdsInCurrentSeason = transactionHandler.runInNewTransactionReadOnly(() -> {
            try {
                return getAllIdsInCurrentSeason();
            } catch (Exception e) {
                return emptyList();
            }
        });
        leagueStateHandler.setAllLeaguesActiveState(allIdsInCurrentSeason);
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true)
    public List<UserLeagueTransactionDto> getUserTransactionHistory(String id, String leagueId) {
        return leagueTransactionsHandler.getUserTransactionHistory(id, leagueId);
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true)
    public Map<String, String> getUserRoundScoreDetails(String username, String leagueName, String roundNumber, @NotNull Boolean specialRound) throws EntityNotExistException, FormatException {
        return leagueAdminHandler.getUserRoundScoreDetails(username, leagueName, seasonService.getByRoundNumber(Integer.parseInt(roundNumber), specialRound));
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true)
    public Map<String, String> getLeagueByNameAsDto(String leagueName) throws EntityNotExistException {
        return leagueAdminHandler.getLeagueByNameAsDto(leagueName);
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true)
    public List<AdminLeagueMembersCountDO> getMembersCountByLeague() throws EntityNotExistException {
        return leagueAdminHandler.getMembersCountByLeague();
    }

    public Map<String, String> addPointsToUserRoundScore(String username, String leagueName, String roundNumber, int pointsToAdd) throws EntityNotExistException {
        return leagueAdminHandler.addPointsToUserRoundScore(username, leagueName, seasonService.getByRoundNumber(Integer.parseInt(roundNumber), null), pointsToAdd);
    }

    public void removeLeagueMemberByAdmin(@NotNull String leagueName, @NotNull String username, @NotNull String currentRole) throws EntityNotExistException, ForbiddenOperationException {
        leagueAdminHandler.removeLeagueMemberByAdmin(leagueName, username, currentRole);
    }

    public LeagueInfoDto createLeagueByAdmin(String name, boolean publicAccess, MultipartFile logo, String userId) throws TechnicalException, EntityNotExistException, InvalidOperationException, ForbiddenOperationException, IOException, SchedulingException, FormatException {
        return create(name, publicAccess, logo, userProfileService.getByIdAsDtoInNewTransaction(userId));
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true)
    public LobbyInfoDto getLeagueLobbyInfo(String userId, String leagueId, int countNewOnMarket, int countLastTransfers) throws EntityNotExistException, FormatException {
        HmLeague league = getLeague(leagueId);
        return leagueLobbyInfoHandler.getLeagueLobbyInfo(userId, league.getId() ,countNewOnMarket, countLastTransfers);
    }
}
